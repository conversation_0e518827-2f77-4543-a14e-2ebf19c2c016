import { Badge } from "@/components/ui/badge";
import { TableCell, TableRow } from "@/components/ui/table";
import { NATIVE_SYMBOL, ZERO_ADDRESS } from "@/constants";
import { PATH_ROUTER } from "@/constants/routers";
import { formatEtherValue } from "@/helpers/format";
import { detectMethod } from "@/helpers/transaction";
import type { Transaction } from "@/types/transactions";
import { formatUnits } from "ethers";
import Link from "next/link";
import { useReadContract } from "wagmi";

interface TokenTransferDecoderProps {
  transaction: Transaction;
  decodedFunction: {
    name: string;
    params: Array<{ name: string; value: string; type: string }>;
  } | null;
  contractAbi?: any;
}

export function TokenTransferDecoder({
  transaction,
  decodedFunction,
  contractAbi,
}: TokenTransferDecoderProps) {
  // Detect the transaction method using the contract ABI if available
  const method = detectMethod(transaction, contractAbi);
  console.log("Method detected:", method);

  const { data: symbol } = useReadContract({
    address: transaction.to as `0x${string}`,
    abi: contractAbi || [
      {
        constant: true,
        inputs: [],
        name: "symbol",
        outputs: [{ name: "", type: "string" }],
        payable: false,
        stateMutability: "view",
        type: "function",
      },
    ],
    functionName: "symbol",
    query: {
      enabled:
        !!transaction.to &&
        (method.name === "Transfer" ||
          method.name === "transfer" ||
          method.name === "Approve" ||
          method.name === "approve"),
    },
  });

  const { data: decimals } = useReadContract({
    address: transaction.to as `0x${string}`,
    abi: contractAbi || [
      {
        constant: true,
        inputs: [],
        name: "decimals",
        outputs: [{ name: "", type: "uint8" }],
        payable: false,
        stateMutability: "view",
        type: "function",
      },
    ],
    functionName: "decimals",
    query: {
      enabled:
        !!transaction.to &&
        (method.name === "Transfer" ||
          method.name === "transfer" ||
          method.name === "Approve" ||
          method.name === "approve"),
    },
  });

  const tokenSymbol = typeof symbol === "string" ? symbol : "TOKEN";
  const tokenDecimals = decimals ? Number(decimals) : 18;

  // Format addresses for display
  const formatShortAddress = (address: string) => {
    if (!address || address === ZERO_ADDRESS) return "N/A";
    return `${address.substring(0, 10)}...${address.substring(
      address.length - 8,
    )}`;
  };

  // Render different transaction types based on the method
  const renderTransactionAction = () => {
    // Check if this is a token transfer (either from method.name or functionName)
    if (
      (method.name === "Transfer" ||
        method.name === "transfer" ||
        method.functionName === "transfer") &&
      (method.type === "token" || method.type === "call")
    ) {
      let recipient = "";
      let amount = "0";

      // Try to get transfer details from decoded function
      if (
        decodedFunction?.name === "transfer" &&
        decodedFunction.params?.length >= 2
      ) {
        recipient = decodedFunction.params[0].value;
        amount = decodedFunction.params[1].value;
      } else if (method.params && method.params.length >= 2) {
        // If we have params from the method detection
        recipient = method.params[0].toString();
        amount = method.params[1].toString();
      }

      if (recipient) {
        return (
          <div className="flex items-center gap-2 flex-wrap">
            <Link
              href={`/address/${transaction.from}`}
              className="text-blue-600 hover:underline font-medium"
            >
              {formatShortAddress(transaction.from)}
            </Link>
            <span>transferred</span>
            <span className="font-semibold">
              {formatUnits(amount, tokenDecimals)}
            </span>
            <Link
              className="font-medium cursor-pointer text-blue-600 hover:underline"
              href={PATH_ROUTER.TOKEN_DETAIL(transaction.to)}
            >
              {tokenSymbol}
            </Link>
            <span>to</span>
            <Link
              href={`/address/${recipient}`}
              className="text-blue-600 hover:underline font-medium"
            >
              {formatShortAddress(recipient)}
            </Link>
          </div>
        );
      }
    }

    // Handle token approvals
    if (
      (method.name === "Approve" ||
        method.name === "approve" ||
        method.functionName === "approve") &&
      (method.type === "token" || method.type === "call")
    ) {
      let spender = "";
      let amount = "0";

      // Try to get approval details from decoded function
      if (
        decodedFunction?.name === "approve" &&
        decodedFunction.params?.length >= 2
      ) {
        spender = decodedFunction.params[0].value;
        amount = decodedFunction.params[1].value;
      } else if (method.params && method.params.length >= 2) {
        spender = method.params[0].toString();
        amount = method.params[1].toString();
      }

      const isUnlimited =
        amount ===
          "115792089237316195423570985008687907853269984665640564039457584007913129639935" ||
        amount ===
          "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff";

      return (
        <div className="flex items-center gap-2 flex-wrap">
          <Link
            href={`/address/${transaction.from}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.from)}
          </Link>
          <span>approved</span>
          {isUnlimited ? (
            <span className="font-semibold">unlimited</span>
          ) : (
            <span className="font-semibold">
              {formatUnits(amount, tokenDecimals)}
            </span>
          )}
          <span className="font-medium">{tokenSymbol}</span>
          <span>spending for</span>
          <Link
            href={`/address/${spender}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(spender)}
          </Link>
        </div>
      );
    }

    // Handle native transfers (native token)
    if (method.type === "native") {
      return (
        <div className="flex items-center gap-2 flex-wrap">
          <Link
            href={`/address/${transaction.from}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.from)}
          </Link>
          <span>transferred</span>
          <span className="font-semibold">
            {formatEtherValue(transaction.value)}
          </span>
          <span className="font-medium">{NATIVE_SYMBOL}</span>
          <span>to</span>
          <Link
            href={`/address/${transaction.to}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.to)}
          </Link>
        </div>
      );
    }

    // Handle contract creation
    if (method.type === "create") {
      return (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="font-medium">Contract Creation</span>
        </div>
      );
    }

    // Handle DEX swaps
    if (method.type === "dex") {
      return (
        <div className="flex items-center gap-2 flex-wrap">
          <span>Swap via</span>
          <Link
            href={`/address/${transaction.to}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.to)}
          </Link>
        </div>
      );
    }

    // Handle clone creation
    if (method.type === "clone") {
      return (
        <div className="flex items-center gap-2 flex-wrap">
          <span>Call</span>
          <span className="px-2 py-0.5 bg-gray-100 rounded text-sm">
            Create Clone
          </span>
          <span>Function by</span>
          <Link
            href={`/address/${transaction.from}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.from)}
          </Link>
          <span>on</span>
          <Link
            href={`/address/${transaction.to}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.to)}
          </Link>
        </div>
      );
    }

    // Handle function calls
    if (
      method.type === "call" ||
      method.type === "execute" ||
      method.type === "unknown"
    ) {
      return (
        <div className="flex items-center gap-2 flex-wrap">
          <span>Call</span>
          {method.action && (
            <span className="px-2 py-0.5 bg-gray-100 rounded text-sm">
              {method.action}
            </span>
          )}
          <span>Function by</span>
          <Link
            href={`/address/${transaction.from}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.from)}
          </Link>
          <span>on</span>
          <Link
            href={`/address/${transaction.to}`}
            className="text-blue-600 hover:underline font-medium"
          >
            {formatShortAddress(transaction.to)}
          </Link>
        </div>
      );
    }

    // Default fallback for other transaction types
    return (
      <div className="flex items-center gap-2 flex-wrap">
        <Badge
          variant="outline"
          className="bg-blue-100 text-blue-800 border-blue-200"
        >
          {method.name}
        </Badge>
        {transaction.value && transaction.value !== "0" && (
          <>
            <span className="font-semibold">
              {formatEtherValue(transaction.value)}
            </span>
            <span className="font-medium">{NATIVE_SYMBOL}</span>
          </>
        )}
      </div>
    );
  };

  return (
    <TableRow>
      <TableCell className="font-medium">Transaction Action:</TableCell>
      <TableCell>{renderTransactionAction()}</TableCell>
    </TableRow>
  );
}
