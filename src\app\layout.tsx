import { Footer } from "@/components/layout/footer"
import { Head<PERSON> } from "@/components/layout/header"
import ContextProvider from "@/context"
import { AuthProvider } from "@/lib/auth"
import SocketProvider from "@/lib/socket/SocketProvider"
import { QueryProvider } from "@/providers/query-provider"
import { ThemeProvider } from "@/providers/theme-provider"
import type { Metadata } from "next"
import { headers } from "next/headers"
import { ToastContainer } from "react-toastify"
import { fonts } from "./fonts"
import "./globals.css"

export const metadata: Metadata = {
  title: "Helios Explorer | Track Transactions, Validators & Governance",
  description:
    "Explore the Helios network in detail. Track blocks, transactions, validator performance, token data, and governance activity — all in one transparent dashboard.",
  openGraph: {
    title: "Helios Explorer | Real-Time Blockchain Data & Analytics",
    description:
      "Get full visibility into the Helios blockchain. Monitor validator stats, wallet activity, token flows, and proposal votes with the Helios Explorer.",
    url: "https://explorer.helioschainlabs.org",
    images: [
      {
        url: "http://helios-landing.vercel.app/img/thumbnail.jpg",
        width: 1200,
        height: 630,
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Helios Explorer | Dive Into Network Activity",
    description:
      "Analyze Helios blocks, transactions, governance proposals, and validator rankings with full transparency.",
    images: ["http://helios-landing.vercel.app/img/thumbnail.jpg"],
  },
  icons: {
    icon: "/favicon.svg",
  },
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const cookies = (await headers()).get("cookie")

  return (
    <html lang="en" suppressHydrationWarning>
      <ContextProvider cookies={cookies}>
        <body className={fonts}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <QueryProvider>
              <AuthProvider>
                <SocketProvider>
                  <div className="min-h-screen flex flex-col">
                    <div className="top">
                      <Header />
                    </div>
                    <main className="flex-1">{children}</main>
                    <Footer />
                    <ToastContainer />
                  </div>
                </SocketProvider>
              </AuthProvider>
            </QueryProvider>
          </ThemeProvider>
        </body>
      </ContextProvider>
    </html>
  )
}
