import { helios<PERSON>hai<PERSON> } from "@/config";
import { ethers } from "ethers";

export const getWei = async () => {
  try {
    const provider = new ethers.JsonRpcProvider(
      heliosChain.rpcUrls.default.http[0],
    );

    const feeData = await provider.getFeeData();

    const latestBlock = await provider.getBlock("latest");

    const gasPrice = feeData.gasPrice;
    const gasPriceInGwei = gasPrice
      ? ethers.formatUnits(gasPrice, "gwei")
      : "0";

    const baseFeePerGas = latestBlock?.baseFeePerGas || null;
    const baseFeeInGwei = baseFeePerGas
      ? ethers.formatUnits(baseFeePerGas, "gwei")
      : "0";

    return {
      gasPriceInGwei,
      maxFeePerGas: feeData.maxFeePerGas,
      maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
      baseFeePerGas: baseFeePerGas,
      baseFeeInGwei,
    };
  } catch (error) {
    console.error("Error fetching gas data:", error);
    return {
      gasPriceInGwei: "0",
      maxFeePerGas: null,
      maxPriorityFeePerGas: null,
      baseFeePerGas: null,
      baseFeeInGwei: "0",
    };
  }
};
