"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ChevronDown, Maximize2, Minimize2 } from "lucide-react";
import { useState } from "react";
import { DecodedFunctionDisplay } from "./decoded-fucntion-display";

export type InputDataViewType = "original" | "decoded" | "default" | "utf8";

interface InputDataViewerProps {
  input: string;
  decodedFunction: {
    name: string;
    params: Array<{ name: string; value: string; type: string }>;
  } | null;
  hasAbi: boolean;
}

export function InputDataViewer({
  input,
  decodedFunction,
  hasAbi,
}: InputDataViewerProps) {
  const [inputDataView, setInputDataView] =
    useState<InputDataViewType>("original");
  const [isInputExpanded, setIsInputExpanded] = useState(false);

  if (!input || input === "0x") {
    return (
      <Textarea
      value={typeof input === "string" ? input : ""}
      readOnly
      className={`font-mono text-xs w-full resize-y ${
        isInputExpanded ? "min-h-[200px]" : "max-h-24"
      }`}
    />
    );
  }

  const tryToDecodeAsUTF8 = (hexString: string) => {
    try {
      // Remove 0x prefix if present
      const hex = hexString.startsWith("0x") ? hexString.slice(2) : hexString;

      // Convert hex to bytes
      let str = "";
      for (let i = 0; i < hex.length; i += 2) {
        const byte = Number.parseInt(hex.substr(i, 2), 16);
        // Only include printable ASCII characters
        if (byte >= 32 && byte <= 126) {
          str += String.fromCharCode(byte);
        } else {
          str += "."; // Replace non-printable characters with dots
        }
      }
      return str;
    } catch (error) {
      return "Unable to decode as UTF-8";
    }
  };

  const getInputContent = () => {
    switch (inputDataView) {
      case "original":
        return input;
      case "utf8":
        return input && input !== "0x" ? tryToDecodeAsUTF8(input) : "";
      case "decoded":
        if (!decodedFunction) {
          const methodId = input.slice(0, 10);
          return `Method ID: ${methodId}`;
        }
        return null; // Will render the decoded function component instead
      default:
        return input.length > 66 && !isInputExpanded
          ? `${input.substring(0, 66)}...`
          : input;
    }
  };

  const inputContent = getInputContent();

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mb-2">
        <div className="flex space-x-2">
          <Button
            variant={inputDataView === "original" ? "secondary" : "outline"}
            size="sm"
            onClick={() => setInputDataView("original")}
          >
            Original
          </Button>

          <Button
            variant={inputDataView === "utf8" ? "secondary" : "outline"}
            size="sm"
            onClick={() => setInputDataView("utf8")}
          >
            UTF-8
          </Button>
          <Button
            variant={inputDataView === "decoded" ? "secondary" : "outline"}
            size="sm"
            onClick={() => setInputDataView("decoded")}
            disabled={!hasAbi && !input}
          >
            Decoded
          </Button>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsInputExpanded(!isInputExpanded)}
        >
          {isInputExpanded ? (
            <Minimize2 className="h-4 w-4" />
          ) : (
            <Maximize2 className="h-4 w-4" />
          )}
        </Button>
      </div>

      {inputDataView === "decoded" && decodedFunction ? (
        <div
          className={`${
            isInputExpanded ? "min-h-[200px]" : "max-h-96"
          } overflow-auto p-4 border rounded-md bg-muted/20`}
        >
          <DecodedFunctionDisplay decodedFunction={decodedFunction} />
        </div>
      ) : (
        <Textarea
          value={typeof inputContent === "string" ? inputContent : ""}
          readOnly
          className={`font-mono text-xs w-full resize-y ${
            isInputExpanded ? "min-h-[200px]" : "max-h-24"
          }`}
        />
      )}

      {!isInputExpanded && input.length > 66 && inputDataView !== "decoded" && (
        <Button
          variant="ghost"
          size="sm"
          className="w-full flex items-center justify-center py-1 text-xs"
          onClick={() => setIsInputExpanded(true)}
        >
          <ChevronDown className="h-3 w-3 mr-1" />
          Show more
        </Button>
      )}
    </div>
  );
}
