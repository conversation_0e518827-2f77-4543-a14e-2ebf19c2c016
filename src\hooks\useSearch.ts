import apiClient from "@/lib/api/apiClient";
import { SearchResponse } from "@/types/search";
import { useQuery } from "@tanstack/react-query";

export function useSearch(address?: string, from = "0x1", to = "0xF") {
  return useQuery({
    queryKey: ["search", address, from, to],
    queryFn: async () => {
      if (!address) return null; 

      const response = await apiClient.post<SearchResponse["result"]>("api/", {
        jsonrpc: "2.0",
        method: "eth_search",
        params: [address, from, to],
        id: 74,
      });

      return response?.result ?? null;
    },
    enabled: <PERSON><PERSON><PERSON>(address), 
    staleTime: 10 * 1000, 
  });
}
