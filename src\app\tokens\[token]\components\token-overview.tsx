import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { TokenResult } from "@/types/tokens"

interface TokenOverviewProps {
  tokenDetail: TokenResult
}

export function TokenOverview({ tokenDetail }: TokenOverviewProps) {
  const formattedTotalSupply = tokenDetail.totalSupply
    ? formatTokenAmount(
        tokenDetail.totalSupply,
        tokenDetail.decimals,
        tokenDetail.symbol,
      )
    : "N/A"

  const holdersChangePercentage: number = 0.024

  return (
    <div className="card">
      <Card className="bg-card">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground">MAX TOTAL SUPPLY</p>
            <p className="text-lg font-semibold">{formattedTotalSupply}</p>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <p className="text-sm text-muted-foreground">{tokenDetail.holdersCount === 1 ? 'HOLDER' : 'HOLDERS'}</p>
              {holdersChangePercentage !== 0 && (
                <Badge
                  variant="outline"
                  className={
                    holdersChangePercentage > 0
                      ? "text-emerald-500"
                      : "text-red-500"
                  }
                >
                  {holdersChangePercentage > 0 ? "+" : ""}
                  {holdersChangePercentage.toFixed(3)}%
                </Badge>
              )}
            </div>
            <p className="text-lg font-semibold">
              {tokenDetail.holdersCount.toLocaleString()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function formatTokenAmount(
  amount: string,
  decimals: number,
  symbol: string,
): string {
  try {
    const value = Number(amount) / Math.pow(10, decimals)
    return `${value.toLocaleString(undefined, {
      maximumFractionDigits: decimals,
    })} ${symbol}`
  } catch (error) {
    return `${amount} ${symbol}`
  }
}
