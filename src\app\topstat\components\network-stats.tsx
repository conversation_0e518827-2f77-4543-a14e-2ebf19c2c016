"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { formatHash } from "@/helpers/format"
import type { StatsTopNetworkEntries } from "@/types/charts"
import Link from "next/link"
import { useRef } from "react"
import { StatsChart } from "./stats-chart"

interface NetworkStatsProps {
  data: { entries: StatsTopNetworkEntries }
}

export function NetworkStats({ data }: NetworkStatsProps) {
  const hasData = useRef(!!(data && data.entries))
  const { entries } = data || { entries: {} }
  const COLORS = [
    "#0088FE", // blue
    "#00C49F", // green
    "#FFBB28", // yellow
    "#FF8042", // orange
    "#8884d8", // purple
    "#82ca9d", // light green
    "#ffc658", // light yellow
    "#8dd1e1", // light blue
    "#a4de6c", // lime
    "#d0ed57", // light lime
  ]

  if (!hasData.current) return <div>No network data available</div>

  return (
    <div className="space-y-8">
      {/* Top Gas Users */}
      {entries.topGasUsers && (
        <div className="card">
          <Card className="shadow-sm">
            <CardHeader className="border-b bg-gray-50 px-6 py-4">
              <CardTitle className="text-base font-medium">
                Top Accounts by Gas Used
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="grid md:grid-cols-2">
                {/* Chart */}
                <div className="p-4 border-r">
                  <h3 className="text-sm font-medium text-center mb-2">
                    Account vs Gas Used
                  </h3>
                  <StatsChart
                    data={entries.topGasUsers}
                    dataKey="value"
                    nameKey="address"
                    colors={COLORS}
                    valueFormatter={(value) => Number(value).toLocaleString()}
                  />
                </div>

                {/* Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500">
                          Rank
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500">
                          Address
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500">
                          Gas Used
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500">
                          Percentage
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {entries.topGasUsers.map((entry) => (
                        <tr
                          key={entry.rank}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="px-4 py-2 text-sm">{entry.rank}</td>
                          <td className="px-4 py-2 text-sm">
                            <Link
                              href={`/address/${entry.address}`}
                              className="text-blue-600 hover:underline"
                            >
                              {formatHash(entry.address)}
                            </Link>
                          </td>
                          <td className="px-4 py-2 text-sm text-right">
                            {Number(entry.value).toLocaleString()}
                          </td>
                          <td className="px-4 py-2 text-sm text-right">
                            {Number(entry.percentage).toFixed(2)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Active Addresses */}
      {entries.topActiveAddresses && (
        <div className="card">
          <Card className="shadow-sm">
            <CardHeader className="border-b bg-gray-50 px-6 py-4">
              <CardTitle className="text-base font-medium">
                Top Accounts by Transaction Count
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="grid md:grid-cols-2">
                {/* Chart */}
                <div className="p-4 border-r">
                  <h3 className="text-sm font-medium text-center mb-2">
                    Account vs Txn Count
                  </h3>
                  <StatsChart
                    data={entries.topActiveAddresses}
                    dataKey="count"
                    nameKey="address"
                    colors={COLORS}
                    valueFormatter={(value) => Number(value).toLocaleString()}
                  />
                </div>

                {/* Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500">
                          Rank
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500">
                          Address
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500">
                          Txn Count
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500">
                          Percentage
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {entries.topActiveAddresses.map((entry) => (
                        <tr
                          key={entry.rank}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="px-4 py-2 text-sm">{entry.rank}</td>
                          <td className="px-4 py-2 text-sm">
                            <Link
                              href={`/address/${entry.address}`}
                              className="text-blue-600 hover:underline"
                            >
                              {formatHash(entry.address)}
                            </Link>
                          </td>
                          <td className="px-4 py-2 text-sm text-right">
                            {entry.count.toLocaleString()}
                          </td>
                          <td className="px-4 py-2 text-sm text-right">
                            {Number(entry.percentage).toFixed(2)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
