"use client";

import { Button } from "@/components/ui/button";

export function SubmitButton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-center items-center">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 rounded-full border-2 border-green-500 flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-green-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <span className="text-green-600 font-medium">Success!</span>
        </div>
        <div className="ml-4">
          <img
            src="https://sjc.microlink.io/dK6jgqVObdnsHEm8Zu1sOWMbBLI5lkBbnkJVenfbSJjgiaUFYvtsUS1ozojWOhT-kr7YAaOjcH8nZt6t7JcMOA.jpeg"
            alt="Cloudflare"
            className="h-8"
          />
        </div>
      </div>

      <div className="flex justify-center">
        <Button type="submit" className="w-full max-w-xs">
          Send Message
        </Button>
      </div>
    </div>
  );
}
