export interface Metadata {
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface Transaction {
  id: number
  createdAt: string
  updatedAt: string
  accessList: string[] | string
  blockHash: string
  blockNumber: number
  chainId: string
  cronId?: string | null
  from: string
  gas: string | number
  gasUsed?: string
  cumulativeGasUsed: string
  effectiveGasPrice?: string
  gasPrice: string
  hash: string
  input: string
  maxFeePerGas: string
  maxPriorityFeePerGas: string
  nonce: number
  r: string
  s: string
  to: string
  transactionIndex: number
  type: string | number
  v: string
  value: string
  yParity?: string
  createdBy?: string
  timestamp: string
  status?: string
  stateChanges: StateStatus[]
  internalTxns: InternalTransaction[]
  logs?: Log[]
}

export interface Log {
  id: number
  createdAt: string
  updatedAt: string
  address: string
  topics: string[]
  cronId: string | null
  functionSignature: string
  data: string
  logIndex: number
  removed: boolean
  transactionHash: string
  blockNumber: number
  blockHash: string
  transactionIndex: number
  timestamp: string
}

export interface InternalTransaction {
  to: string
  gas: string
  from: string
  type: string
  input: string
  value: string
  output: string
  gasUsed: string
}

export interface StateStatus {
  address: string
  balanceAfter: string
  balanceBefore: string
  balanceChanges: string
}

export interface TransactionReceipt {
  blockHash: string
  blockNumber: number
  contractAddress: string | undefined
  cumulativeGasUsed: string
  from: string
  gasPrice: string
  blobGasUsed: string
  blobGasPrice: string
  gasUsed: string
  hash: string
  index: number
  logs: any[]
  logsBloom: string
  status: number
  to: string
}

export interface TransactionListResponse {
  result: {
    data: Transaction[]
    metadata: {
      total: number
    }
  }
  mapNameTag: {
    [address: string]: string
  }
}

export interface TransactionByTxHashResponse {
  result: Transaction
}

export interface TransactionReceiptByTxHashResponse {
  result: TransactionReceipt
}

export interface TransactionCountByAddressResponse {
  result: string
}

export interface ListAccountCronsResponse {
  jsonrpc: string
  id: number
  result: {
    id: number
    address: string
    ownerAddress: string
    contractAddress: string
    abiJson: string
    methodName: string
    frequency: number
    nextExecutionBlock: number
    expirationBlock: number
    executionStage: number
    gasLimit: number
    maxGasPrice: string
    totalExecutedTransactions: number
    totalFeesPaid: string
    cronType: string
  }[]
}

export interface TransactionInternalFilter {
  fromBlock?: number
  toBlock?: number
  fromAddress?: string
  toAddress?: string
  parentHash?: string
}

export interface InternalTransaction {
  id: number
  createdAt: string
  updatedAt: string
  from: string
  gas: string
  gasUsed: string
  input: string
  output: string
  to: string
  type: string
  value: string
  parentHash: string
  signature: string
  timestamp: string
  blockNumber: number
}

export interface InternalTransactionResponse {
  result: {
    data: InternalTransaction[]
    metadata: Metadata
  }
}

export interface TransferTokensResponse {
  jsonrpc: string
  id: number
  result: TransferTokens[]
  mapNameTag: {
    [address: string]: string
  }
}

export interface TransferTokens {
  id: number
  createdAt: string
  updatedAt: string
  blockNumber: number
  type: string
  from: string
  transactionHash: string
  to: string
  amount: string
  value: string
  tokenAddress: string
  tokenId: any
  functionSignature: string
  timestamp: string
  tokenDetails: TokenDetails
  nftDetails: any
}

export interface TokenDetails {
  address: string
  name: string
  symbol: string
  logo: any
}
