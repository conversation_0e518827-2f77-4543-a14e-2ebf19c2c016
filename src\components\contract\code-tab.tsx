"use client";

import CopyTooltip from "@/app/components/copy-tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import type { Source } from "@/types/verify-contract";
import {
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ExternalLink,
} from "lucide-react";
import { useState } from "react";

interface CodeTabProps {
  source?: Source;
  isLoading?: boolean;
}

export function CodeTab({ source, isLoading = false }: CodeTabProps) {
  const [expandedSections, setExpandedSections] = useState<{
    [key: string]: boolean;
  }>({
    sourceCode: true,
    abi: false,
    bytecode: false,
    constructorArgs: false,
  });

  const files =
    source && source.sourceFiles
      ? Object.entries(source.sourceFiles).map(([name, content]) => ({
          name,
          content,
        }))
      : [];

  const openExternalLink = (fileName: string, content: string) => {
    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    window.open(url, "_blank");
  };

  if (isLoading) {
    return (
      <div className="mt-4 space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-64" />
          <Skeleton className="h-9 w-24" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!source || !files.length) {
    return (
      <Alert className="mt-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          No source code available for this contract. The contract may not be
          verified.
        </AlertDescription>
      </Alert>
    );
  }

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const formatABI = (abi: string) => {
    try {
      return JSON.stringify(JSON.parse(abi), null, 2);
    } catch (_e) {
      return abi;
    }
  };

  const formattedAbi = source.abi ? formatABI(source.abi) : "";

  return (
    <div className="mt-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Contract Source Code</span>
          <span className="text-xs text-muted-foreground">
            (Solidity Standard Json-Input format)
          </span>
        </div>
      </div>

      {files.map((file, index) => (
        <div key={index} className="space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-sm">
              File {index + 1} of {files.length}: {file.name}
            </p>
            <div className="flex gap-1">
              <CopyTooltip content={file.content} />
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => openExternalLink(file.name, file.content)}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>
          {/* <div className="relative rounded-md border bg-muted/50">
            <pre className="overflow-x-auto p-4 text-xs">
              <code>{file.content}</code>
            </pre>
          </div> */}
          <div className="relative rounded-md border bg-muted/50">
            <Textarea
              readOnly
              className="font-mono text-xs h-64 resize-none bg-transparent numbered"
              value={file.content}
            />
          </div>
        </div>
      ))}

      {/* ABI Section */}
      {source?.abi && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-auto"
                onClick={() => toggleSection("abi")}
              >
                {expandedSections.abi ? (
                  <ChevronUp className="h-4 w-4 mr-2" />
                ) : (
                  <ChevronDown className="h-4 w-4 mr-2" />
                )}
                <span className="text-sm font-medium">Contract ABI</span>
              </Button>
            </div>
            <CopyTooltip content={source.abi || ""} />
          </div>

          {expandedSections.abi && (
            <div className="relative rounded-md border bg-muted/50">
              <Textarea
                readOnly
                className="font-mono text-xs h-64 resize-none bg-transparent numbered"
                value={formattedAbi}
              />
            </div>
          )}
        </div>
      )}

      {/* Constructor Arguments Section */}
      {source?.constructorArguments && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-auto"
                onClick={() => toggleSection("constructorArgs")}
              >
                {expandedSections.constructorArgs ? (
                  <ChevronUp className="h-4 w-4 mr-2" />
                ) : (
                  <ChevronDown className="h-4 w-4 mr-2" />
                )}
                <span className="text-sm font-medium">
                  Constructor Arguments
                </span>
              </Button>
              <span className="text-xs text-muted-foreground">
                (ABI-encoded)
              </span>
            </div>
            <CopyTooltip content={source.constructorArguments || ""} />
          </div>

          {expandedSections.constructorArgs && (
            <div className="relative rounded-md border bg-muted/50">
              <pre className="overflow-x-auto p-4 text-xs">
                <code>{source.constructorArguments}</code>
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Bytecode Section */}
      {source?.deployedBytecodeArtifacts && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-auto"
                onClick={() => toggleSection("bytecode")}
              >
                {expandedSections.bytecode ? (
                  <ChevronUp className="h-4 w-4 mr-2" />
                ) : (
                  <ChevronDown className="h-4 w-4 mr-2" />
                )}
                <span className="text-sm font-medium">Deployed Bytecode</span>
              </Button>
            </div>
            <CopyTooltip content={source.deployedBytecodeArtifacts || ""} />
          </div>

          {expandedSections.bytecode && (
            <div className="relative rounded-md border bg-muted/50">
              <pre className="overflow-x-auto p-4 text-xs break-all">
                <code>{source.deployedBytecodeArtifacts}</code>
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
