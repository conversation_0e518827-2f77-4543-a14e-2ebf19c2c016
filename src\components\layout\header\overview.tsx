import { formatNumber } from "@/lib/utils"
import { Area, AreaChart, ResponsiveContainer } from "recharts"
import s from "./header.module.scss"

interface ChartProps {
  data: { date: string; value: number }[]
}

const Chart = ({ data }: ChartProps) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data}>
        <defs>
          <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#fff" stopOpacity={0.1} />
            <stop offset="95%" stopColor="#fff" stopOpacity={0} />
          </linearGradient>
        </defs>
        <Area
          type="monotone"
          dataKey="value"
          stroke="rgba(255,255,255,0.5)"
          strokeWidth={1}
          fillOpacity={1}
          fill="url(#colorValue)"
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

export const Overview = () => {
  const dataTotal = [
    { date: "Jan", value: 0 },
    { date: "Feb", value: 0 },
    { date: "Mar", value: 0 },
    { date: "Apr", value: 0 },
    { date: "May", value: 0 },
    { date: "Jun", value: 0 },
    { date: "Jul", value: 0 },
    { date: "Aug", value: 0 },
    { date: "Sep", value: 0 },
    { date: "Oct", value: 0 },
    { date: "Nov", value: 0 },
    { date: "Dec", value: 0 },
  ]

  const dataGovernance = [
    { date: "Jan", value: 0 },
    { date: "Feb", value: 0 },
    { date: "Mar", value: 0 },
    { date: "Apr", value: 0 },
    { date: "May", value: 0 },
    { date: "Jun", value: 0 },
    { date: "Jul", value: 0 },
    { date: "Aug", value: 0 },
    { date: "Sep", value: 0 },
    { date: "Oct", value: 0 },
  ]

  return (
    <div className="container mx-auto px-4" data-overview>
      <div className={s.overview}>
        <div className={s.item}>
          <div className={s.left}>
            <div className={s.label}>Total TVL</div>
            <div className={s.value}>${formatNumber(0)}</div>
            <div className={s.message}>
              Restaked Assets: <strong>0 Tokens</strong>
            </div>
          </div>
          <div className={s.right}>
            <Chart data={dataTotal} />
          </div>
        </div>
        <div className={s.item}>
          <div className={s.left}>
            <div className={s.label}>Total Transactions</div>
            <div className={s.value}>{formatNumber(0)}</div>
            <div className={s.message}>
              Governance Votes: <strong>{formatNumber(0)}</strong>
            </div>
          </div>
          <div className={s.right}>
            <Chart data={dataGovernance} />
          </div>
        </div>
      </div>
    </div>
  )
}
