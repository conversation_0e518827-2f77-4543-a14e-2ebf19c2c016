"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { normalizeAddress, truncateAddress } from "@/helpers/format"
import Link from "next/link"

interface AddressWithTooltipProps {
  address: string
  nameTag?: string
  className?: string
}

export function AddressWithTooltip({
  address,
  nameTag,
  className = "",
}: AddressWithTooltipProps) {
  const normalizedAddress = normalizeAddress(address) || address
  const displayText = nameTag || truncateAddress(address)

  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link
              href={PATH_ROUTER.ADDRESS_DETAIL(address)}
              className={`text-blue-600 hover:underline font-medium ${className}`}
            >
              {displayText}
            </Link>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            <span className="font-mono text-xs">{normalizedAddress}</span>
          </TooltipContent>
        </Tooltip>
        <CopyTooltip content={normalizedAddress} />
      </TooltipProvider>
    </div>
  )
}
