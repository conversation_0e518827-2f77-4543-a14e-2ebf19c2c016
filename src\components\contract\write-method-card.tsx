"use client";

import {
  useAccount,
  useWaitForTransactionReceipt,
  useWriteContract,
} from "wagmi";
import { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Copy, ExternalLink } from "lucide-react";
import type { ContractMethod } from "@/types/contract";
import { ContractMethodCardBase } from "./contract-method-card-base";

interface WriteMethodCardProps {
  method: ContractMethod;
  contractAddress: string;
  index: number;
}

export function WriteMethodCard({
  method,
  contractAddress,
  index,
}: WriteMethodCardProps) {
  const { address } = useAccount();
  const [txHash, setTxHash] = useState<`0x${string}` | undefined>(undefined);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    writeContractAsync,
    isPending: isWritePending,
    error: writeError,
  } = useWriteContract();

  // Wait for transaction
  const { isLoading: isTxLoading, isSuccess: isTxSuccess } =
    useWaitForTransactionReceipt({
      hash: txHash,
      query: {
        enabled: !!txHash,
      },
    });

  const copyToClipboard = (text: string): void => {
    navigator.clipboard.writeText(text);
  };

  const renderActions = (args: unknown[], shouldFetch: boolean) => {
    const handleWrite = async () => {
      if (!address) {
        return;
      }

      try {
        setIsSubmitting(true);
        const hash = await writeContractAsync({
          address: contractAddress as `0x${string}`,
          abi: [method],
          functionName: method.name,
          args,
        });

        setTxHash(hash);
      } catch (error) {
        console.error("Error writing to contract:", error);
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <div className="flex items-center gap-2">
        <Button
          type="submit"
          variant="default"
          disabled={isWritePending || isTxLoading || !address || isSubmitting}
          onClick={() => {
            if (shouldFetch) {
              handleWrite();
            }
          }}
        >
          {isWritePending || isTxLoading || isSubmitting
            ? "Processing..."
            : "Write"}
        </Button>

        {isTxSuccess && (
          <span className="text-sm text-green-600">
            Transaction successful!
          </span>
        )}
      </div>
    );
  };

  const renderResults = () => {
    return (
      <>
        {/* Transaction hash display */}
        {txHash && (
          <Alert className="mt-2">
            <AlertDescription className="flex items-center gap-2">
              <span>
                Transaction Hash: {txHash.slice(0, 10)}...{txHash.slice(-8)}
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => copyToClipboard(txHash)}
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="icon" className="h-6 w-6" asChild>
                <a
                  href={`https://explorer.helioschainlabs.org/tx/${txHash}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="h-3 w-3" />
                </a>
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Error display */}
        {writeError && (
          <Alert variant="destructive" className="mt-2">
            <AlertDescription>
              {writeError?.message || "An error occurred"}
            </AlertDescription>
          </Alert>
        )}

        {!address && (
          <Alert className="mt-2">
            <AlertDescription>
              Please connect your wallet to interact with this contract.
            </AlertDescription>
          </Alert>
        )}
      </>
    );
  };

  return (
    <ContractMethodCardBase
      method={method}
      contractAddress={contractAddress}
      index={index}
      isWrite={true}
      renderActions={renderActions}
      renderResults={renderResults}
    />
  );
}
