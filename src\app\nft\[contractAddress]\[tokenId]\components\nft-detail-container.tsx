"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useGetNftTokenDetail } from "@/hooks/useNft"
import { useGetTransferTokenEventByAddress } from "@/hooks/useTokens"
import { AlertCircle } from "lucide-react"
import { NFTDescription } from "./nft-description"
import { NFTDetails } from "./nft-details"
import { NFTHeader } from "./nft-header"
import { NFTImage } from "./nft-image"
import { NFTPriceStats } from "./nft-price-stats"
import { NFTProperties } from "./nft-properties"
import { NFTTransactions } from "./nft-transactions"

interface NFTDetailContainerProps {
  contractAddress: string
  tokenId: string
}

export function NFTDetailContainer({
  contractAddress,
  tokenId,
}: NFTDetailContainerProps) {
  const {
    data: nft,
    isLoading: isNftLoading,
    isError: isNftError,
  } = useGetNftTokenDetail(contractAddress, tokenId)

  const {
    data: transactionData,
    isLoading: isTransactionsLoading,
    isError: isTransactionsError,
  } = useGetTransferTokenEventByAddress(
    contractAddress,
    null,
    1,
    25,
    Number(tokenId),
  )

  if (isNftError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">Error Loading NFT</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Failed to load NFT details. Please try again later.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isNftLoading) {
    return <NFTDetailSkeleton />
  }

  if (!nft) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <h3 className="text-lg font-semibold">NFT Not Found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              The requested NFT could not be found.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Left Column - NFT Image */}
      <div className="lg:col-span-1">
        <NFTImage image={nft.image} name={nft.name} tokenId={tokenId} />
      </div>

      {/* Right Column - NFT Details */}
      <div className="lg:col-span-2 space-y-6">
        <NFTHeader name={nft.name} />

        <NFTPriceStats contractAddress={contractAddress} tokenId={tokenId} />

        <NFTDetails
          nft={nft}
          contractAddress={contractAddress}
          tokenId={tokenId}
        />

        <NFTProperties attributes={nft.attributes} />

        <NFTDescription description={nft.description} />
      </div>

      <div className="lg:col-span-3">
        <NFTTransactions
          contractAddress={contractAddress}
          tokenId={tokenId}
          transactions={transactionData?.data || []}
          isLoading={isTransactionsLoading}
          isError={isTransactionsError}
          totalCount={transactionData?.metadata?.total || 0}
        />
      </div>
    </div>
  )
}

function NFTDetailSkeleton() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-1">
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <Skeleton className="aspect-square w-full" />
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-2 space-y-6">
        <div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-6 w-32" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>

        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-32 w-full" />
        ))}
      </div>
    </div>
  )
}
