"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Info, MessageCircle } from "lucide-react"

interface NFTHeaderProps {
  name?: string
  creator?: string
}

export function NFTHeader({ name, creator }: NFTHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
      <div>
        <h1 className="text-2xl font-bold">{name || "Unknown NFT"}</h1>
        <div className="flex items-center mt-1">
          {creator && (
            <Badge variant="outline" className="mr-2">
              {creator}
            </Badge>
          )}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-gray-400" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Creator of this NFT</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  )
}
