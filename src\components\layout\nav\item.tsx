import { ChevronDown } from "lucide-react"
import Link from "next/link"
import s from "./nav.module.scss"

export interface NavItemProps {
  label: string
  href?: string
  sub?: {
    label: string
    href: string
  }[]
  isOpen?: boolean
  setIsOpen?: (isOpen: boolean) => void
}

export const NavItem = ({
  label,
  href,
  sub,
  isOpen,
  setIsOpen,
}: NavItemProps) => {
  return (
    <li>
      {href ? (
        <Link className={s.item} href={href} onClick={() => setIsOpen?.(false)}>
          {label}
        </Link>
      ) : (
        <span className={s.item} data-sub>
          {label}
          <ChevronDown className={s.icon} />
        </span>
      )}
      {sub && (
        <ul className={s.sub}>
          {sub.map((item) => (
            <NavItem
              key={item.href}
              {...item}
              isOpen={isOpen}
              setIsOpen={setIsOpen}
            />
          ))}
        </ul>
      )}
    </li>
  )
}
