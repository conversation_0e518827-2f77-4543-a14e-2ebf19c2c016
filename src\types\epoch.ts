export interface EpochDetailsResponse {
  jsonrpc: string | undefined
  id: number | undefined
  result: EpochDetail | undefined
}

export interface EpochDetail {
  id: number
  createdAt: string
  updatedAt: string
  epochNumber: number
  epochLength: number
  startBlock: number
  endBlock: number
  isFinalized: boolean
  blockNumber: number
  timestamp: string
  totalBlockCount: number
  votesMap: {
    [key: string]: string
  }

  repartitionMap: {
    [key: string]: string
  }

  totalTokens: string
  totalVotingPower: string
  validators: Validator[]
}

export interface Validator {
  id: number
  createdAt: string
  updatedAt: string
  operatorAddress: string
  status: boolean
  epochNumber: number
  assetWeights: AssetWeight[]
  votingPower: string
  commissionRate: string
  blocksSigned: BlockSignature[]
  blocksMissed: BlockSignature[] | null
}

export interface BlockSignature {
  height: number
  signature: boolean
}

export interface AssetWeight {
  denom: string
  baseAmount: string
  weightedAmount: string
  symbol: string
}

export interface CommissionRates {
  rate: string
  max_rate: string
  max_change_rate: string
}

export interface ListEpochResponse {
  jsonrpc: string
  id: number
  result: Result
  // mapNameTag: {
  //   [address: string]: string
  // }
}

export interface Result {
  data: Daum[]
  metadata: Metadata
}

export interface Daum {
  id: number
  createdAt: string
  updatedAt: string
  epochNumber: number
  epochLength: number
  startBlock: number
  endBlock: number
  timestamp: string
}

export interface Metadata {
  total: number
  page: number
  limit: number
  totalPages: number
}
