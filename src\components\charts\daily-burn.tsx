"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ChartTypeEnum, getTimeRanges, use<PERSON>hart } from "@/hooks/useChart"
import { Download, Info } from "lucide-react"
import { useMemo, useState } from "react"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import TimeRangeSelector, { type TimeRangeOption } from "./time-range-seletor"

// Define the native symbol for the blockchain
const NATIVE_SYMBOL = "HLS" // Replace with your actual native symbol

interface DailyBurntTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
}

const DailyBurntTooltip = ({ active, payload, label }: DailyBurntTooltipProps) => {
  if (!active || !payload || !payload.length) return null

  const data = payload[0].payload
  // Format the date safely
  let formattedDate = ""
  try {
    if (label) {
      const date = new Date(label)
      formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    }
  } catch (e) {
    console.error("Error formatting date:", e)
    formattedDate = String(label)
  }

  return (
    <div className="rounded-lg border bg-background/95 p-3 shadow-md">
      <div className="mb-2 flex items-center gap-2">
        <div className="h-3 w-3 rounded-full bg-rose-500" />
        <span className="font-medium">{formattedDate}</span>
      </div>
      <div className="space-y-1 text-sm">
        <p>
          <span className="text-muted-foreground">{NATIVE_SYMBOL} Burnt: </span>
          <span className="font-medium">
            {data.burntAmount.toFixed(4)} {NATIVE_SYMBOL}
          </span>
        </p>
        <p>
          <span className="text-muted-foreground">USD Value: </span>
          <span className="font-medium">
            ${(data.burntAmount * (data.tokenPrice || 2000)).toLocaleString(undefined, { maximumFractionDigits: 2 })}
          </span>
        </p>
      </div>
    </div>
  )
}

const timeRangeOptions: TimeRangeOption[] = [
  { value: "1m", label: "1m" },
  { value: "3m", label: "3m" },
  { value: "6m", label: "6m" },
  { value: "1y", label: "1y" },
  { value: "all", label: "All" },
]

const weiToEth = (weiString: string): number => {
  if (!weiString || typeof weiString !== "string") return 0

  try {
    const wei = BigInt(weiString)
    const eth = Number(wei) / 1e18
    return eth
  } catch (error) {
    console.error("Error converting wei to ETH:", error)
    return 0
  }
}

export default function DailyBurntChart() {
  const [timeRange, setTimeRange] = useState<string>("1m")

  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d")
      case "3m":
        return getTimeRanges("90d")
      case "6m":
        return getTimeRanges("180d")
      case "1y":
        return getTimeRanges("1y")
      case "all":
        return getTimeRanges("all")
      default:
        return getTimeRanges("30d")
    }
  }, [timeRange])

  const { data, isLoading, error } = useChart(startTime, endTime, ChartTypeEnum.DAILY_BURNT)

  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    return data.map((item: any) => ({
      date: new Date(item.timestamp).toISOString().split("T")[0],
      burntAmount: weiToEth(item.burnFee),
      tokenPrice: item.tokenPrice || 2000, // Default price if not available
    }))
  }, [data])

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Daily {NATIVE_SYMBOL} Burnt</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Daily {NATIVE_SYMBOL} Burnt</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <TimeRangeSelector timeRange={timeRange} onChange={setTimeRange} options={timeRangeOptions} />
        </CardHeader>
        <CardContent>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Skeleton className="h-[450px] w-full" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <defs>
                    <linearGradient id="burntGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => {
                      const date = new Date(value)
                      return `${date.getMonth() + 1}/${date.getDate()}`
                    }}
                  />
                  <YAxis
                    tickFormatter={(value) => {
                      return `${value.toFixed(2)} ${NATIVE_SYMBOL}`
                    }}
                    label={{
                      value: `${NATIVE_SYMBOL} Burnt`,
                      angle: -90,
                      position: "insideLeft",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <Tooltip content={<DailyBurntTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="burntAmount"
                    name={`${NATIVE_SYMBOL} Burnt`}
                    stroke="#ef4444"
                    fillOpacity={1}
                    fill="url(#burntGradient)"
                    strokeWidth={2}
                    activeDot={{ r: 6, fill: "#ef4444" }}
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" className="flex items-center">
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Daily {NATIVE_SYMBOL} Burnt chart shows the amount of {NATIVE_SYMBOL} that is permanently removed from
            circulation each day. This burning mechanism was introduced with EIP-1559, which requires a portion of
            transaction fees to be burnt, reducing the overall {NATIVE_SYMBOL} supply and potentially making it
            deflationary over time.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Click and drag in the plot area to zoom in. You can also use the time range buttons to view different time
            periods.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

