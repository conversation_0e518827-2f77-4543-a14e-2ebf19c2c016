import apiClient from "@/lib/api/apiClient";
import { useQuery } from "@tanstack/react-query";

interface HotContractStatsResponse {
  result: { [key: string]: number };
}

export const useGetHotContractStats = (
  type: "five_minute" | "one_hour" | "four_hour" | "one_day",
) => {
  return useQuery({
    queryKey: ["hot-contract-stats", type],
    queryFn: async () => {
      const response = await apiClient.post<
        HotContractStatsResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "stats_hotcontract",
        params: [type],
        id: 1,
      });
      return response?.result;
    },
  });
};
