import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ColumnConfig {
  width?: string;
  align?: "left" | "center" | "right";
  hideOnMobile?: boolean;
}

interface TableSkeletonProps {
  columns: number | ColumnConfig[];
  rows: number;
}

export function TableSkeleton({ columns, rows }: TableSkeletonProps) {
  const columnConfigs = Array.isArray(columns)
    ? columns
    : Array(columns).fill({});

  return (
    <>
      {/* Desktop Table Skeleton - hidden on mobile */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              {columnConfigs.map((column, index) => (
                <TableHead
                  key={index}
                  className={`${column.align === "right" ? "text-right" : ""} ${
                    column.hideOnMobile ? "hidden md:table-cell" : ""
                  }`}
                >
                  <Skeleton className={`h-9 ${column.width || "w-full"}`} />
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: rows }).map((_, rowIndex) => (
              <TableRow key={rowIndex} className="h-14">
                {columnConfigs.map((column, colIndex) => (
                  <TableCell
                    key={colIndex}
                    className={`${
                      column.align === "right" ? "text-right" : ""
                    } ${column.hideOnMobile ? "hidden md:table-cell" : ""}`}
                  >
                    <Skeleton className={`h-11 ${column.width || "w-full"}`} />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card Skeleton - shown only on mobile */}
      <div className="block md:hidden space-y-4">
        {Array.from({ length: rows }).map((_, index) => (
          <div
            key={index}
            className="rounded-lg border p-4 bg-white shadow-sm flex flex-col gap-2 text-sm"
          >
            <div className="flex items-center gap-2">
              <Skeleton className="h-9 w-9 rounded" />
              <Skeleton className="h-5 w-32" />
            </div>

            <div className="flex items-center gap-1">
              <Skeleton className="h-4 w-10" />
              <Skeleton className="h-4 w-40" />
            </div>

            <div className="flex items-center gap-1">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>

            <div className="flex items-center gap-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-32" />
            </div>

            <div className="flex items-center gap-1">
              <Skeleton className="h-4 w-14" />
              <Skeleton className="h-4 w-40" />
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
