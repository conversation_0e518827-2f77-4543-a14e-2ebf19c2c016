import apiClient, { ApiResponse } from "@/lib/api/apiClient"
import {
  AccountInfoResponse,
  GetAbiResponse,
  SearchCodeParams,
  SearchCodeResponse,
  SolidityVerifierMultiplePartParams,
  SolidityVerifierStandardParams,
  VerifyContractResponseResult,
  VersionsContractResponse,
  VyperVerifierMultiplePartParams,
  VyperVerifierStandardParams,
} from "@/types/verify-contract"
import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query"

export const useGetVersionsSolidity = () => {
  return useQuery({
    queryKey: ["solidity-versions"],
    queryFn: async () => {
      try {
        const response = await apiClient.get<VersionsContractResponse>(
          "api/contract-v2/verifier/solidity/versions",
        )
        return response
      } catch (error) {
        console.error("Error fetching Solidity versions:", error)
        throw error
      }
    },
  })
}

export function useSolidityVerifierMultiplePart(): UseMutationResult<
  ApiResponse<VerifyContractResponseResult> | undefined,
  Error,
  SolidityVerifierMultiplePartParams
> {
  return useMutation({
    mutationFn: (params: SolidityVerifierMultiplePartParams) =>
      apiClient.post<VerifyContractResponseResult>(
        "api/contract-v2/verifier/solidity/sources/verify-multi-part",
        params,
      ),
  })
}

export const useSolidityVerifierStandard = (): UseMutationResult<
  ApiResponse<VerifyContractResponseResult> | undefined,
  Error,
  SolidityVerifierStandardParams
> => {
  return useMutation({
    mutationFn: (params: SolidityVerifierStandardParams) =>
      apiClient.post<VerifyContractResponseResult>(
        "api/contract-v2/verifier/solidity/sources/verify-standard-json",
        params,
      ),
  })
}

export const useGetVersionsVyper = () => {
  return useQuery({
    queryKey: ["vyper-versions"],
    queryFn: async () => {
      try {
        const response = await apiClient.get<VersionsContractResponse>(
          "api/contract-v2/verifier/vyper/versions",
        )
        return response
      } catch (error) {
        console.error("Error fetching Vyper versions:", error)
        throw error
      }
    },
  })
}

export const useVyperVerifierStandard = (): UseMutationResult<
  ApiResponse<VerifyContractResponseResult> | undefined,
  Error,
  VyperVerifierStandardParams
> => {
  return useMutation({
    mutationFn: (params: VyperVerifierStandardParams) =>
      apiClient.post<VerifyContractResponseResult>(
        "api/contract-v2/verifier/vyper/sources/verify-standard-json",
        params,
      ),
  })
}

export const useVyperVerifierMultiplePart = (): UseMutationResult<
  ApiResponse<VerifyContractResponseResult> | undefined,
  Error,
  VyperVerifierMultiplePartParams
> => {
  return useMutation({
    mutationFn: (params: VyperVerifierMultiplePartParams) =>
      apiClient.post<VerifyContractResponseResult>(
        "api/contract-v2/verifier/vyper/sources/verify-multi-part",
        params,
      ),
  })
}
export const useGetCode = (address: string) => {
  return useQuery({
    queryKey: ["code", address],
    queryFn: async () => {
      const response = await apiClient.post("api/", {
        jsonrpc: "2.0",
        method: "eth_getCode",
        params: [address, "latest"],
        id: 1,
      })
      return response?.result as string
    },
    enabled: !!address,
  })
}

export const useSearchCode = (): UseMutationResult<
  ApiResponse<SearchCodeResponse> | undefined,
  Error,
  SearchCodeParams
> => {
  return useMutation({
    mutationFn: (params: SearchCodeParams) =>
      apiClient.post<SearchCodeResponse>(
        "api/contract-v2/bytecodes/sources/search",
        params,
      ),
  })
}

export const useGetAccountInfo = (accountAddress: string) => {
  return useQuery({
    queryKey: ["accountAddress", accountAddress],
    queryFn: async () => {
      const response = await apiClient.post<AccountInfoResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getAccountInfo",
          params: [accountAddress],
          id: 1,
        },
      )
      return response?.result
    },
    enabled: !!accountAddress,
  })
}

export const useGetImplementationAddress = (
  accountAddress: string,
  isProxy?: boolean,
) => {
  return useQuery({
    queryKey: ["implementationAddress", accountAddress],
    queryFn: async () => {
      const response = await apiClient.post("api/", {
        jsonrpc: "2.0",
        method: "eth_getImplementationAddress",
        params: [accountAddress],
        id: 1,
      })
      return response?.result as string
    },
    enabled: !!accountAddress && isProxy === true,
  })
}

export const useGetAbi = (accountAddress: string) => {
  return useQuery({
    queryKey: ["abi", accountAddress],
    queryFn: async () => {
      try {
        const response = await apiClient.post<GetAbiResponse["result"]>(
          "api/",
          {
            jsonrpc: "2.0",
            method: "eth_getAbi",
            params: [accountAddress],
            id: 1,
          },
        )

        return response?.result ?? "0x"
      } catch (error) {
        console.error("Error fetching ABI:", error)
        return "0x"
      }
    },
    enabled: !!accountAddress,
  })
}
