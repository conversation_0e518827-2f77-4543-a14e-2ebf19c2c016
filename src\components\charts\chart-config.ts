import { NATIVE_SYMBOL } from "@/constants";
import { type ChartType, ChartTypeEnum } from "@/hooks/useChart";

export type ChartVisualType = "area" | "line" | "bar" | "pie" | "mixed";

export interface ChartConfig {
  title: string;
  chartType: ChartType;
  visualType: ChartVisualType;
  color: string;
  description?: string;
  detailComponent?: string;
  yAxisLabel?: string;
}

// Chart data
export const marketDataCharts = [
  {
    id: "price",
    title: "Ether Daily Price (USD) Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "marketcap",
    title: "Ether Market Capitalization Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "supply-cap",
    title: "Total Supply & Market Cap Chart",
    type: "pie" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "supply-growth",
    title: "Ether Supply Growth Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
];

export const blockchainDataCharts = [
  {
    id: "transactions",
    title: "Daily Transactions Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "token-transfer",
    title: "HRC20 Daily Token Transfer Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "addresses",
    title: "Unique Addresses Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "block-size",
    title: "Average Block Size Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "block-time",
    title: "Average Block Time Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "gas-price",
    title: "Average Gas Price Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "gas-limit",
    title: "Average Gas Limit Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "gas-used",
    title: "Daily Gas Used Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "block-rewards",
    title: "Block Count and Rewards Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "node-sync-default",
    title: "Full Node Sync (Default) Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "active-address",
    title: "Daily Active Helios Address",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "active-erc20-address",
    title: "Daily Active ERC20 Address",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "transaction-fee",
    title: "Average Transaction Fee Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "burnt-chart",
    title: `Daily ${NATIVE_SYMBOL} Burnt`,
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
];

export const contractDataCharts = [
  {
    id: "verified-contracts",
    title: "Daily Verified Contracts Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "deployed-contracts",
    title: "Daily Deployed Contracts Chart",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
];

export const dashboardCharts = [
  {
    id: "pending-transactions",
    title: "Daily Pending Transactions",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
  {
    id: "eip1559-metrics",
    title: "EIP-1559 Metrics",
    type: "area" as ChartVisualType,
    color: "#f39c12",
  },
  {
    id: "contract-stats",
    title: "Contract Statistics",
    type: "mixed" as ChartVisualType,
    color: "#3498db",
  },
];

export const networkDataCharts = [
  {
    id: "network-transaction-fee",
    detailComponent: "network-transaction-fee",
    title: "Network Transaction Fee Chart",
    type: "line" as ChartVisualType,
    color: "#3498db",
  },
];

export const statisticsCharts = [
  {
    id: "dextracker",
    title: "DEX Activity",
    type: "area" as ChartVisualType,
    color: "#3498db",
  },
];

export const chartConfig: Record<string, ChartConfig> = {
  price: {
    title: "Ether Daily Price (USD) Chart",
    chartType: ChartTypeEnum.DAILY_TXN,
    visualType: "area",
    color: "#3498db",
    description:
      "The Ether price chart shows the historical daily price of Ether (ETH) in USD.",
    detailComponent: "price",
  },
  marketcap: {
    title: "Ether Market Capitalization Chart",
    chartType: ChartTypeEnum.DAILY_TXN,
    visualType: "area",
    color: "#2ecc71",
    description:
      "The market capitalization is calculated by multiplying the Ether price with the total supply.",
  },
  "supply-cap": {
    title: "Total Supply & Market Cap Chart",
    chartType: ChartTypeEnum.DAILY_TXN,
    visualType: "pie",
    color: "#3498db",
    description:
      "The total supply chart shows the distribution of Ether from various sources.",
    detailComponent: "supply",
  },
  transactions: {
    title: "Daily Transactions Chart",
    chartType: ChartTypeEnum.DAILY_TXN,
    visualType: "area",
    color: "#9b59b6",
    description:
      "The daily transactions chart shows the number of transactions per day on the Helios blockchain.",
    detailComponent: "transactions",
  },
  "token-transfer": {
    title: "HRC20 Daily Token Transfer Chart",
    chartType: ChartTypeEnum.ERC20_TOKEN_TRANSFER,
    visualType: "area",
    color: "#f39c12",
    description:
      "The daily token transfer chart shows the number of HRC20 token transfers per day.",
    yAxisLabel: "Token Transfers",
  },
  addresses: {
    title: "Unique Addresses Chart",
    chartType: ChartTypeEnum.UNIQUE_ADDRESS,
    visualType: "area",
    color: "#1abc9c",
    description:
      "The unique addresses chart shows the growth of unique addresses on the Helios blockchain.",
    yAxisLabel: "Unique Addresses",
  },
  "block-size": {
    title: "Average Block Size Chart",
    chartType: ChartTypeEnum.AVG_BLOCK_SIZE,
    visualType: "bar",
    color: "#e74c3c",
    description:
      "The Average Block Size chart indicates the historical average block size in bytes of the Helios blockchain.",
    yAxisLabel: "Block Size in Bytes",
  },
  "block-time": {
    title: "Average Block Time Chart",
    chartType: ChartTypeEnum.AVG_BLOCK_TIME,
    visualType: "line",
    color: "#3498db",
    description:
      "The Average Block Time chart shows the time taken to mine a block on the Helios blockchain.",
    yAxisLabel: "Block Time in Seconds",
  },
  "gas-price": {
    title: "Average Gas Price Chart",
    chartType: ChartTypeEnum.AVG_GAS_PRICE,
    visualType: "line",
    color: "#2ecc71",
    description:
      "The Average Gas Price chart shows the average gas price in Gwei over time.",
    yAxisLabel: "Gas Price (Gwei)",
  },
  "gas-limit": {
    title: "Average Gas Limit Chart",
    chartType: ChartTypeEnum.AVG_GAS_LIMIT,
    visualType: "line",
    color: "#f39c12",
    description:
      "The Average Gas Limit chart shows the average gas limit per block over time.",
    yAxisLabel: "Gas Limit",
  },
  "gas-used": {
    title: "Daily Gas Used Chart",
    chartType: ChartTypeEnum.GAS_USED,
    visualType: "line",
    color: "#9b59b6",
    description:
      "The Daily Gas Used chart shows the total amount of gas used per day.",
    yAxisLabel: "Gas Used",
  },
  "block-rewards": {
    title: "Block Count and Rewards Chart",
    chartType: ChartTypeEnum.BLOCK_COUNT_AND_REWARD,
    visualType: "bar",
    color: "#3498db",
    description:
      "The Helios Block Count and Rewards Chart shows the historical number of blocks produced daily on the Helios network and the total block reward.",
    yAxisLabel: "Block Count",
  },
  "node-sync-default": {
    title: "Full Node Sync (Default) Chart",
    chartType: ChartTypeEnum.FULL_NODE_SYNC,
    visualType: "area",
    color: "#3498db",
    description:
      "Helios Full Node Sync (Default) Chart details the chain data size for Parity accompanied with block number and client version.",
    detailComponent: "node-sync",
    yAxisLabel: "Chain data size (in GB)",
  },
  "active-address": {
    title: "Active Helios Addresses",
    chartType: ChartTypeEnum.ACTIVE_ADDRESS,
    visualType: "area",
    color: "#3498db",
    description:
      "The Active Helios Address chart shows the daily number of unique addresses that were active on the network as a sender or receiver.",
    detailComponent: "active-address",
    yAxisLabel: "Active Helios Addresses",
  },
  "active-erc20-address": {
    title: "Active ERC20 Addresses",
    chartType: ChartTypeEnum.ACTIVE_ERC20_ADDRESS,
    visualType: "area",
    color: "#3498db",
    detailComponent: "active-erc20-address",
    description:
      "The Active ERC20 Address chart shows the daily number of unique addresses that were active in ERC20 token transfers on the network.",
    yAxisLabel: "Active ERC20 Addresses",
  },
  "transaction-fee": {
    title: "Transaction Fees Chart",
    chartType: ChartTypeEnum.AVG_TXN_FEE,
    visualType: "area",
    color: "#3498db",
    detailComponent: "transaction-fee",
    description:
      "The chart shows the daily average amount in USD spent per transaction on the Ethereum network.",
    yAxisLabel: "Average Transaction Fee",
  },
  "burnt-chart": {
    title: `Daily ${NATIVE_SYMBOL} Burnt`,
    chartType: ChartTypeEnum.DAILY_BURNT,
    visualType: "area",
    color: "#ef4444",
    detailComponent: "burnt-chart",
    description: `The Daily ${NATIVE_SYMBOL} Burnt chart shows the amount of ${NATIVE_SYMBOL} that is permanently removed from circulation each day through the EIP-1559 burning mechanism.`,
    yAxisLabel: `${NATIVE_SYMBOL} Burnt`,
  },
  "verified-contracts": {
    title: "Daily Verified Contracts Chart",
    chartType: ChartTypeEnum.DAILY_VERIFIED_CONTRACT,
    visualType: "area",
    color: "#9b59b6",
    description:
      "The chart shows the total number of contracts verified daily on the Helios blockchain.",
    yAxisLabel: "Verified Contracts",
  },
  "deployed-contracts": {
    title: "Daily Deployed Contracts Chart",
    chartType: ChartTypeEnum.DAILY_DEPLOYED_CONTRACT,
    visualType: "area",
    color: "#2ecc71",
    description:
      "The chart shows the number of smart contracts deployed daily on the Helios blockchain.",
    yAxisLabel: "Deployed Contracts",
  },
  "pending-transactions": {
    title: "Daily Pending Transactions",
    chartType: ChartTypeEnum.AVG_PENDING_TRANSACTION,
    visualType: "area",
    color: "#3498db",
    description:
      "The chart shows the average number of pending transactions in the mempool over time.",
    detailComponent: "pending-transactions",
    yAxisLabel: "Pending Transactions",
  },
  "eip1559-metrics": {
    title: "EIP-1559 Metrics",
    chartType: ChartTypeEnum.EIP1559_METRICS,
    visualType: "area",
    color: "#f39c12",
    description:
      "The chart shows key metrics related to EIP-1559 implementation including base fee, priority fee, and burnt fees.",
    detailComponent: "eip1559-metrics",
    yAxisLabel: "Gwei",
  },
  "contract-stats": {
    title: "Contract Statistics",
    chartType: ChartTypeEnum.CONTRACT_STATS,
    visualType: "mixed",
    color: "#3498db",
    description:
      "Comprehensive statistics about smart contracts on the Helios blockchain, including deployment trends, verification rates, and interaction patterns.",
    detailComponent: "contract-stats",
    yAxisLabel: "Contracts",
  },
  dextracker: {
    title: "DEX Activity",
    chartType: ChartTypeEnum.DAILY_TXN,
    visualType: "area",
    color: "#3498db",
    description:
      "The chart shows the total number of transactions on the Helios blockchain.",
    detailComponent: "dextracker",
    yAxisLabel: "Transactions",
  },
  "network-transaction-fee": {
    title: "Network Transaction Fee Chart",
    chartType: ChartTypeEnum.AVG_TXN_FEE,
    visualType: "bar",
    color: "#9b59b6",
    description:
      "The chart shows the average transaction fee in Hls over time.",
    detailComponent: "network-transaction-fee",
    yAxisLabel: "Transaction Fee (Hls)",
  },  
};

// Custom formatters for specific chart types
export const getYAxisFormatter = (chartType: ChartType) => {
  switch (chartType) {
    case "AVG_GAS_PRICE":
      return (value: number) => `${value.toFixed(2)} Gwei`;
    case "AVG_BLOCK_TIME":
      return (value: number) => `${value.toFixed(2)} s`;
    case "AVG_BLOCK_SIZE":
      return (value: number) => `${(value / 1000).toFixed(0)}K`;
    case "DAILY_VERIFIED_CONTRACT":
    case "DAILY_DEPLOYED_CONTRACT":
      return (value: number) => value.toLocaleString();
    default:
      return (value: number) => value.toLocaleString();
  }
};
