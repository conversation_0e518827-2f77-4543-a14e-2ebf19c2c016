import { Skeleton } from "@/components/ui/skeleton";

export const AnalyticsChartSkeleton = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array(4)
        .fill(0)
        .map((_, i) => (
          <div key={i} className="border rounded-lg p-4 space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-7 w-40" />
            <Skeleton className="h-4 w-24" />
          </div>
        ))}
    </div>
    <div className="border rounded-lg p-4">
      <Skeleton className="h-[300px] w-full" />
    </div>
  </div>
);
