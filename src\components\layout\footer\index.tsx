import { SocialLinks } from "@/components/social-links"
import { PATH_ROUTER } from "@/constants/routers"
import Image from "next/image"
import Link from "next/link"
import s from "./footer.module.scss"

export function Footer() {
  return (
    <footer className={s.footer}>
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/brand"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                >
                  Brand Assets
                </Link>
              </li>
              <li>
                <Link
                  href={PATH_ROUTER.CONTACT_US}
                  className="text-sm text-muted-foreground hover:text-blue-700"
                >
                  Contact Us
                </Link>
              </li>
              <li>
                <Link
                  href="/careers"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                >
                  Careers
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Community</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="https://discord.gg/AjpJnJxt5e"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Discord
                </Link>
              </li>
              <li>
                <Link
                  href="https://t.me/helioschain"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Telegram
                </Link>
              </li>
              <li>
                <Link
                  href="https://medium.com/@helioschain"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Medium
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Products & Services</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="https://helioschain.network"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Main Website
                </Link>
              </li>
              <li>
                <Link
                  href="https://portal.helioschain.network"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Portal
                </Link>
              </li>
              <li>
                <Link
                  href="https://testnet.helioschain.network"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Testnet App
                </Link>
              </li>
              <li>
                <Link
                  href="https://hub.helioschain.network"
                  className="text-sm text-muted-foreground hover:text-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Helios Hub
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Follow Us</h3>
            <SocialLinks />
          </div>
        </div>
        <div className={s.bottom}>
          <Image
            src="/assets/images/logo-primary.svg"
            alt="Logo"
            width={204}
            height={32}
          />
          <p>© 2025 Helios Explorer. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
