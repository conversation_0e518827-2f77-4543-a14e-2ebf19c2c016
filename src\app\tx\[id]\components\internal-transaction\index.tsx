"use client"

import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { NATIVE_SYMBOL } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatEtherValue,
  formatHash,
  normalizeAddress,
} from "@/helpers/format"
import type { InternalTransaction } from "@/types/transactions"
import { ArrowDownToLine, ArrowUpFromLine, FileIcon } from "lucide-react"
import Link from "next/link"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useRouter } from "next/navigation"
interface InternalTransactionsViewerProps {
  internalTxns?: InternalTransaction[]
  isLoading?: boolean
}

export function InternalTransactionsViewer({
  internalTxns,
  isLoading = false,
}: InternalTransactionsViewerProps) {
  const router = useRouter()

  if (isLoading) {
    return (
      <Card className="border rounded-lg p-6 mt-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
          <h3 className="text-lg font-semibold">
            Loading internal transactions...
          </h3>
        </div>
      </Card>
    )
  }

  if (!internalTxns || internalTxns.length === 0) {
    return (
      <Card className="border rounded-lg p-6 mt-6">
        <div className="flex flex-col items-center justify-center py-4 text-center text-muted-foreground">
          <FileIcon className="h-8 w-8 mb-2 opacity-50" />
          <h3 className="text-base font-medium">
            No internal transactions found
          </h3>
          <p className="text-sm text-gray-500 max-w-md mx-auto mt-1">
            This transaction did not generate any internal transactions.
          </p>
        </div>
      </Card>
    )
  }

  return (
    <Card className="border rounded-lg overflow-hidden mt-6">
      <div className="p-4 bg-muted/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileIcon className="h-5 w-5 text-muted-foreground" />
            <h2 className="text-lg font-semibold">Internal Transactions</h2>
          </div>
          <span className="text-sm text-muted-foreground">
            {internalTxns.length} transaction
            {internalTxns.length !== 1 ? "s" : ""}
          </span>
        </div>
      </div>

      <Tabs defaultValue="all-transfers" className="w-full">
        <div className="px-4 pt-2">
          <TabsList>
            <TabsTrigger value="all-transfers">All Transfers</TabsTrigger>
            <TabsTrigger value="net-transfers">Net Transfers</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all-transfers" className="p-0">
          <div className="p-4">
            {internalTxns.map((tx, index) => {
              const formattedValue = formatEtherValue(tx.value)
              const isSent =
                tx.type === "call" ||
                tx.type === "staticcall" ||
                tx.type === "delegatecall"

              return (
                <div
                  key={`internal-tx-${index}`}
                  className="py-2 border-b last:border-b-0"
                >
                  <div className="flex items-center gap-2">
                    <div className="w-6 flex-shrink-0">
                      {tx.from && tx.to && (
                        <>
                          {isSent ? (
                            <ArrowUpFromLine className="h-4 w-4 text-red-500" />
                          ) : (
                            <ArrowDownToLine className="h-4 w-4 text-green-500" />
                          )}
                        </>
                      )}
                    </div>

                    <div className="flex-grow">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              className="text-blue-700 hover:underline"
                              onClick={() => {
                                router.push(
                                  `${PATH_ROUTER.ADDRESS_DETAIL}/${tx.from}`,
                                )
                              }}
                            >
                              {formatHash(normalizeAddress(tx.from), 6, 4)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {normalizeAddress(tx.from)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <span className="text-xs text-muted-foreground hidden sm:inline">
                          →
                        </span>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              className="text-blue-700 hover:underline"
                              onClick={() => {
                                router.push(
                                  `${PATH_ROUTER.ADDRESS_DETAIL}/${tx.to}`,
                                )
                              }}
                            >
                              {formatHash(normalizeAddress(tx.to), 6, 4)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {normalizeAddress(tx.to)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="font-medium">
                        {isSent ? "sent" : "received"} {formattedValue}{" "}
                        {NATIVE_SYMBOL}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {tx.type || "transfer"}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="net-transfers" className="p-0">
          <div className="p-4">
            {/* Calculate net transfers by address */}
            {(() => {
              const netTransfers = new Map<
                string,
                { value: bigint; type: string }
              >()

              internalTxns.forEach((tx) => {
                const value = BigInt(tx.value || "0")
                if (!value) return

                // Add to receiver
                const receiverCurrent = netTransfers.get(tx.to) || {
                  value: BigInt(0),
                  type: "received",
                }
                netTransfers.set(tx.to, {
                  value: receiverCurrent.value + value,
                  type: "received",
                })

                // Subtract from sender
                const senderCurrent = netTransfers.get(tx.from) || {
                  value: BigInt(0),
                  type: "sent",
                }
                netTransfers.set(tx.from, {
                  value: senderCurrent.value - value,
                  type: "sent",
                })
              })

              // Convert to array and sort by absolute value
              return Array.from(netTransfers.entries())
                .filter(([_, { value }]) => value !== BigInt(0))
                .sort((a, b) => {
                  const absA = a[1].value < 0 ? -a[1].value : a[1].value
                  const absB = b[1].value < 0 ? -b[1].value : b[1].value
                  return Number(absB - absA)
                })
                .map(([address, { value, type }], index) => {
                  const isPositive = value > 0
                  const absValue = isPositive ? value : -value
                  const formattedValue = formatEtherValue(absValue.toString())

                  return (
                    <div
                      key={`net-tx-${index}`}
                      className="py-2 border-b last:border-b-0"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-6 flex-shrink-0">
                          {isPositive ? (
                            <ArrowDownToLine className="h-4 w-4 text-green-500" />
                          ) : (
                            <ArrowUpFromLine className="h-4 w-4 text-red-500" />
                          )}
                        </div>

                        <div className="flex-grow">
                          <Link
                            href={PATH_ROUTER.ADDRESS_DETAIL(address)}
                            className="text-blue-600 hover:underline font-medium text-sm"
                          >
                            {formatHash(address, 6, 4)}
                          </Link>
                        </div>

                        <div className="text-right">
                          <div className="font-medium">
                            {isPositive ? "received" : "sent"} {formattedValue}{" "}
                            {NATIVE_SYMBOL}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            net {isPositive ? "in" : "out"}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })
            })()}
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  )
}
