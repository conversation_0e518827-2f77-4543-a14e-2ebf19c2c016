"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { helios<PERSON>hain } from "@/config"
import { DECIMAL, NATIVE_SYMBOL } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatNumberWithNotation, formatWeiToEther, normalizeAddress } from "@/helpers/format"
import type { TokenBalance } from "@/types/tokens"
import { type AccountInfo, AccountTypeEnum } from "@/types/verify-contract"
import {
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Search
} from "lucide-react"
import Link from "next/link"
import { useEffect, useRef, useState } from "react"

interface AddressOverviewProps {
  balance: string | undefined
  transactionCount: string | undefined
  tokenHoldings: TokenBalance[]
  address: string
  accountInfo: AccountInfo | undefined
}

export function AddressOverview({
  balance,
  transactionCount,
  tokenHoldings: initialTokenHoldings,
  address,
  accountInfo,
}: AddressOverviewProps) {
  const [isTokenDropdownOpen, setIsTokenDropdownOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const dropdownRef = useRef<HTMLDivElement>(null)

  const tokenHoldings = initialTokenHoldings

  const filteredTokens = tokenHoldings?.filter(
    (token: TokenBalance) =>
      token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.address.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const toggleTokenDropdown = () => {
    setIsTokenDropdownOpen(!isTokenDropdownOpen)
    if (!isTokenDropdownOpen) {
      setSearchQuery("")
    }
  }

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsTokenDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const isCronAccount = accountInfo?.accountType === AccountTypeEnum.CRON
  const hasOwnerAddress = isCronAccount && accountInfo?.ownerAddress

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
          <span className="text-2xl">👤</span>
        </div>
        <div className="flex-1">
          <h1 className="text-2xl font-bold">
            {isCronAccount ? "Cron Address" : "Address"}
          </h1>
          <div className="flex items-center gap-2">
            <code className="text-sm">{normalizeAddress(address) || address}</code>
            <CopyTooltip content={normalizeAddress(address) || address} />
            {/* <Button variant="ghost" size="icon" className="h-8 w-8">
              <Share2 className="h-4 w-4" />
            </Button> */}
          </div>

          {hasOwnerAddress && (
            <div className="mt-2">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Alias of</span>
                <code className="text-sm">{accountInfo.ownerAddress}</code>
                <CopyTooltip content={accountInfo.ownerAddress} />
                <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                  <Link
                    href={PATH_ROUTER.ADDRESS_DETAIL(accountInfo.ownerAddress)}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <div className="card">
          <Card>
            <CardContent className="pt-6">
              <div className="text-sm text-muted-foreground mb-2">
                {heliosChain.nativeCurrency.name} Balance
              </div>
              <div className="text-2xl font-bold">
                {balance ? formatWeiToEther(balance, 6, 18) : "0"}{" "}
                {NATIVE_SYMBOL}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="card">
          <Card className="relative min-h-full">
            <CardContent className="pt-6 pb-4">
              <div className="text-sm text-muted-foreground mb-2">
                Token Holdings
              </div>
              <div
                className="flex items-center justify-between cursor-pointer"
                onClick={toggleTokenDropdown}
              >
                <div className="text-xl font-semibold">
                  {tokenHoldings.length} {tokenHoldings.length === 1 ? 'token' : 'tokens'}
                </div>
                <Button variant="ghost" size="sm" className="p-0 h-6">
                  {isTokenDropdownOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {isTokenDropdownOpen && (
                <div
                  ref={dropdownRef}
                  className="absolute left-0 right-0 top-full z-50 mt-1 bg-white dark:bg-gray-900 border rounded-md shadow-lg max-h-96 overflow-y-auto w-full md:w-[450px]"
                >
                  <div className="p-3 border-b relative">
                    <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search for Token Name"
                      className="w-full p-2 pl-8 text-sm border rounded-md"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>

                  <div className="p-2 border-b text-sm font-medium">
                    HRC-20 Tokens ({filteredTokens.length} of{" "}
                    {tokenHoldings.length})
                  </div>

                  <div className="divide-y">
                    {filteredTokens.length > 0 ? (
                      filteredTokens.map(
                        (token: TokenBalance, index: number) => (
                          <Link
                            href={PATH_ROUTER.TOKEN_DETAIL(token.address)}
                            key={index}
                            className="flex items-center justify-between p-3 hover:bg-gray-100 dark:hover:bg-gray-800"
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs">
                                {token.symbol.charAt(0)}
                              </div>
                              <div>
                                <div className="font-medium">
                                  {token.symbol}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {formatWeiToEther(token.balance, 6, 18)}{" "}
                                  {token.symbol}
                                </div>
                              </div>
                            </div>
                            <ExternalLink className="h-4 w-4 text-muted-foreground" />
                          </Link>
                        ),
                      )
                    ) : (
                      <div className="p-4 text-center text-muted-foreground">
                        {searchQuery
                          ? "No tokens found matching your search"
                          : "No tokens found"}
                      </div>
                    )}
                  </div>

                  <div className="p-3 border-t text-center">
                    <Link
                      href={`/token-holdings?a=${address}`}
                      className="text-sm text-blue-500 hover:text-blue-700 flex items-center justify-center gap-1"
                    >
                      VIEW ALL HOLDINGS
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="card">
          <Card className="min-h-full">
            <CardContent className="pt-6 pb-4">
              <div className="text-sm text-muted-foreground mb-2">
                Transactions
              </div>
              <div className="text-2xl font-bold">
                {transactionCount
                  ? formatNumberWithNotation(
                      formatWeiToEther(transactionCount, 0, 0),
                      DECIMAL.DEFAULT,
                      "~",
                    )
                  : "0"}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {isCronAccount && accountInfo?.targetContract && (
        <div className="card">
          <Card className="min-h-full">
            <CardContent className="pt-6 pb-4">
              <div className="text-sm text-muted-foreground mb-2">
                Target Contract
              </div>
              <div className="flex items-center gap-2">
                <Link
                  href={PATH_ROUTER.ADDRESS_DETAIL(accountInfo.targetContract)}
                  className="text-blue-600 hover:underline"
                >
                  {accountInfo.targetContract}
                </Link>
                <CopyTooltip content={accountInfo.targetContract} />
                <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                  <Link
                    href={PATH_ROUTER.ADDRESS_DETAIL(
                      accountInfo.targetContract,
                    )}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
