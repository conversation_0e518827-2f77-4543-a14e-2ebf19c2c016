"use client"

import { Card } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Transaction } from "@/types/transactions"
import BigNumber from "bignumber.js"
import { StateViewer } from "./state-viewer"

interface StorageChange {
  storageAddress: string
  before: string
  after: string
}

interface StateChange {
  address: string
  before: {
    value: string
    nonce?: number
  }
  after: {
    value: string
    nonce?: number
  }
  stateDifference?: string
}

interface StateTabProps {
  transaction: Transaction | undefined
  isLoading: boolean
  isError: boolean
}

function formatWeiToEther(value: string | number) {
  if (!value) return "0"
  return new BigNumber(value.toString()).dividedBy("1e18").toFixed()
}

export function StateTab({ transaction, isLoading, isError }: StateTabProps) {
  const transformStateChanges = (): StateChange[] => {
    if (!transaction?.stateChanges) return []

    return transaction.stateChanges.map((change) => {
      const beforeEther = formatWeiToEther(change.balanceBefore)
      const afterEther = formatWeiToEther(change.balanceAfter)
      const diff = new BigNumber(change.balanceChanges || 0)

      const diffEther = formatWeiToEther(diff.abs().toString())
      const stateDifference = diff.isNegative()
        ? `-${diffEther}`
        : `+${diffEther}`

      return {
        address: change.address,
        before: {
          value: beforeEther,
        },
        after: {
          value: afterEther,
        },
        stateDifference,
      }
    })
  }

  const storageChanges: StorageChange[] = []

  if (isLoading) {
    return (
      <Card className="border rounded-lg p-6">
        <Skeleton className="h-8 w-1/3 mb-4" />
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-5/6 mb-6" />
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, idx) => (
            <div key={idx} className="grid grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-16" />
              ))}
            </div>
          ))}
        </div>
      </Card>
    )
  }

  if (isError || !transaction) {
    return (
      <Card className="border rounded-lg p-6">
        <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
          <div className="h-12 w-12 mb-4 opacity-50 flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-alert-triangle"
            >
              <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
              <path d="M12 9v4" />
              <path d="M12 17h.01" />
            </svg>
          </div>
          <h3 className="text-lg font-medium mb-2">
            Error loading state changes
          </h3>
          <p className="text-gray-500 max-w-md mx-auto">
            We couldn't load the state changes for this transaction. Please try
            again later.
          </p>
        </div>
      </Card>
    )
  }

  const stateChanges = transformStateChanges()

  return (
    <StateViewer stateChanges={stateChanges} storageChanges={storageChanges} />
  )
}
