"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
} from "recharts";

// Mock data for supply distribution
const supplyData = [
  {
    name: "Genesis (60M Crowdsale, 12M Other)",
    value: 72000000,
    color: "#3498db",
  },
  { name: "Mining Block Rewards", value: 47223991, color: "#2ecc71" },
  { name: "Mining Uncle Rewards", value: 3125986, color: "#9b59b6" },
  { name: "HRC20 Staking Rewards", value: 2840091, color: "#f39c12" },
  { name: "Burnt Fees", value: -4574062, color: "#e74c3c" },
];

const COLORS = ["#3498db", "#2ecc71", "#9b59b6", "#f39c12", "#e74c3c"];

const totalSupply = supplyData.reduce((acc, item) => acc + item.value, 0);
const marketCap = totalSupply * 2013.58; // Mock ETH price

export default function SupplyDistribution() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            Ether Total Supply and Market Capitalization Chart
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-6">
            The table and pie chart shows the distribution of Ether from reward
            of both block and uncle block mining to arrive at the current total
            Ether supply. The Ether (ETH) price both in USD and BTC is also
            shown in the table below. With both the total supply of Ether and
            the current price in USD, we can arrive at the market capitalization
            as shown in the pie chart.
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Ether Distribution Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Amount (ETH)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {supplyData.map((item) => (
                  <TableRow key={item.name}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell className="text-right">
                      {item.value.toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className="font-bold">
                  <TableCell>Current Total Supply</TableCell>
                  <TableCell className="text-right">
                    {totalSupply.toLocaleString()}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Price per Ether</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell>In USD</TableCell>
                  <TableCell className="text-right">
                    ${(2013.58).toLocaleString()}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>In BTC</TableCell>
                  <TableCell className="text-right">0.02232856</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Card className="col-span-1 lg:col-span-2">
        <CardHeader className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <CardTitle>Breakdown by Supply Types</CardTitle>
          <div className="flex flex-col items-end">
            <div className="text-2xl font-bold">
              {totalSupply.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">
              Total Ether Supply
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={supplyData.filter((item) => item.value > 0)} // Filter out negative values
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={150}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) =>
                    `${(percent * 100).toFixed(0)}%`
                  }
                >
                  {supplyData
                    .filter((item) => item.value > 0)
                    .map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                </Pie>
                <Tooltip
                  formatter={(value) => [`${value.toLocaleString()} ETH`, ""]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-1 lg:col-span-2">
        <CardHeader className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <CardTitle>Market Capitalization</CardTitle>
          <div className="flex flex-col items-end">
            <div className="text-2xl font-bold">
              ${marketCap.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">
              Market Capitalization
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Market capitalization is calculated by multiplying the total supply
            of Ether ({totalSupply.toLocaleString()} ETH) with the current price
            (${(2013.58).toLocaleString()} USD).
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
