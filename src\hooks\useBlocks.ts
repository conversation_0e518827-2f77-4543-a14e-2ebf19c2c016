import apiClient from "@/lib/api/apiClient"
import type {
  BlockDetailsResponse,
  BlockList,
  BlockListResponse,
} from "@/types/blocks"
import { useQuery, UseQueryOptions } from "@tanstack/react-query"
import { numberToHex } from "viem"

export interface BlockListParams {
  page?: number
  limit?: number
  fromBlock?: number
  toBlock?: number
  refetchInterval?: number
}

export const useBlockList = (
  params: BlockListParams,
  options?: Omit<
    UseQueryOptions<BlockListResponse | undefined, Error>,
    "queryKey" | "queryFn"
  >,
) => {
  const { page = 1, limit = 25, fromBlock, toBlock, refetchInterval } = params

  return useQuery({
    queryKey: ["eth_listBlocks", page, limit, fromBlock, toBlock],
    queryFn: async () => {
      try {
        const response = await apiClient.post<{
          data: BlockList[]
          metadata: {
            total: number
            page: number
            limit: number
          }
        }>("api/", {
          jsonrpc: "2.0",
          method: "eth_listBlocks",
          params: [
            page,
            limit,
            {
              fromBlock,
              toBlock,
            },
          ],
          id: 1,
        })

        if (!response) return undefined

        return {
          id: response.id,
          jsonrpc: response.jsonrpc,
          result: response.result,
          mapNameTag: response.mapNameTag || {},
        }
      } catch (error) {
        console.error("Error fetching blocks:", error)
        throw error
      }
    },
    refetchInterval,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    ...options,
  })
}

export const useBlockDetails = (blockNumber: string) => {
  return useQuery({
    queryKey: ["blockDetails", blockNumber],
    queryFn: async () => {
      const hexBlockNumber =
        typeof blockNumber === "string" && blockNumber.startsWith("0x")
          ? blockNumber
          : numberToHex(
              typeof blockNumber === "string"
                ? Number.parseInt(blockNumber)
                : blockNumber,
            )

      const response = await apiClient.post<BlockDetailsResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getBlockByNumber",
          params: [hexBlockNumber, true],
          id: 1,
        },
      )

      return response?.result
    },
    staleTime: 5 * 60 * 1000,
  })
}

export const useGetBlockByHash = (blockHash: string) => {
  return useQuery({
    queryKey: ["blockDetails", blockHash],
    queryFn: async () => {
      const response = await apiClient.post<BlockDetailsResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getBlockByHash",
          params: [blockHash, true],
          id: 1,
        },
      )

      return response?.result
    },
    staleTime: 5 * 60 * 1000,
  })
}
