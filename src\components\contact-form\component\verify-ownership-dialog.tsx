"use client"

import { useState } from "react"
import { useAccount, useSignMessage } from "wagmi"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react"
import { toast } from "react-toastify"

interface VerifyOwnershipDialogProps {
  contractAddress: string
  onVerificationComplete: (signature: string, message: string) => void
  isVerified?: boolean
}

export function VerifyOwnershipDialog({
  contractAddress,
  onVerificationComplete,
  isVerified = false,
}: VerifyOwnershipDialogProps) {
  const [open, setOpen] = useState(false)
  const [verificationStep, setVerificationStep] = useState<
    "initial" | "signing" | "completed"
  >("initial")
  const { address, isConnected } = useAccount()
  const { signMessage, isPending } = useSignMessage()

  const verificationMessage = `I verify that I own or control the smart contract at address ${contractAddress}. Timestamp: ${Date.now()}`

  const handleVerifyOwnership = async () => {
    if (!isConnected || !address) {
      toast.error("Please connect your wallet first")
      return
    }

    setVerificationStep("signing")

    try {
      signMessage(
        {
          message: verificationMessage,
        },
        {
          onSuccess: (signature) => {
            setVerificationStep("completed")
            onVerificationComplete(signature, verificationMessage)
            toast.success("Ownership verified successfully!")

            // Close dialog after a short delay
            setTimeout(() => {
              setOpen(false)
              setVerificationStep("initial")
            }, 2000)
          },
          onError: (error) => {
            console.error("Verification failed:", error)
            toast.error("Verification failed. Please try again.")
            setVerificationStep("initial")
          },
        },
      )
    } catch (error) {
      console.error("Verification failed:", error)
      toast.error("Verification failed. Please try again.")
      setVerificationStep("initial")
    }
  }

  if (isVerified) {
    return (
      <div className="flex items-center gap-2 text-green-600">
        <CheckCircle className="h-4 w-4" />
        <span className="text-sm">Ownership Verified</span>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          Verify Ownership
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Verify Contract Ownership</DialogTitle>
          <DialogDescription>
            Sign a message to verify that you own or control this smart contract
            address.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Contract Address:</strong> {contractAddress}
            </AlertDescription>
          </Alert>

          {verificationStep === "initial" && (
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                You will be asked to sign a message with your wallet to prove
                ownership of this contract.
              </p>
              <Alert className="bg-yellow-50 border-yellow-200">
                <AlertDescription className="text-sm">
                  <strong>Requirements:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>
                      You must be connected with the wallet that deployed this
                      contract
                    </li>
                    <li>Or have owner/admin privileges on this contract</li>
                    <li>
                      This verification is required for updating contract
                      metadata
                    </li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          )}

          {verificationStep === "signing" && (
            <div className="text-center py-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-sm">
                Please sign the message in your wallet...
              </p>
            </div>
          )}

          {verificationStep === "completed" && (
            <div className="text-center py-4">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-sm text-green-600">
                Ownership verified successfully!
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          {verificationStep === "initial" && (
            <>
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              {!isConnected ? (
                <>
                  {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
                  {/* @ts-ignore */}
                  <appkit-button />
                </>
              ) : (
                <Button onClick={handleVerifyOwnership} disabled={isPending}>
                  {isPending ? "Verifying..." : "Verify Ownership"}
                </Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
