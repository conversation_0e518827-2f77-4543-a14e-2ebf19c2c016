import { ZERO_ADDRESS } from "@/constants"
import { formatHash, normalizeAddress } from "./format"

export const getNameTag = (
  address: string,
  mapNameTag?: { [address: string]: string },
): string | undefined => {
  if (!mapNameTag || !address) return undefined
  return mapNameTag[address.toLowerCase()] || mapNameTag[address]
}

export const truncateNameTag = (
  nameTag: string,
  maxLength: number = 20,
): string => {
  if (nameTag.length <= maxLength) return nameTag
  return `${nameTag.substring(0, maxLength)}...`
}

export const renderAddressWithTag = (
  address: string | null | undefined,
  mapNameTag?: { [address: string]: string },
  isShowTagName = false,
) => {
  const normalizedAddress = normalizeAddress(address || "") || address || ZERO_ADDRESS
  const nameTag = getNameTag(normalizedAddress, mapNameTag)

  if (nameTag && isShowTagName) {
    return truncateNameTag(nameTag)
  }

  return normalizedAddress === ZERO_ADDRESS
    ? "0x0000...0000"
    : formatHash(normalizedAddress)
}
