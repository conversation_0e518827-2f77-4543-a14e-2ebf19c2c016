"use client";

import { But<PERSON> } from "@/components/ui/button";
import { StatsTopMetric } from "@/types/charts";

interface MetricSelectorProps {
  currentMetric: StatsTopMetric;
  onMetricChange: (metric: StatsTopMetric) => void;
}

export function MetricSelector({
  currentMetric,
  onMetricChange,
}: MetricSelectorProps) {
  return (
    <div className="mb-4">
      <div className="flex space-x-2">
        <Button
          variant={
            currentMetric === StatsTopMetric.ONE_DAY ? "default" : "outline"
          }
          size="sm"
          onClick={() => onMetricChange(StatsTopMetric.ONE_DAY)}
        >
          24 Hours
        </Button>
        <Button
          variant={
            currentMetric === StatsTopMetric.THREE_DAYS ? "default" : "outline"
          }
          size="sm"
          onClick={() => onMetricChange(StatsTopMetric.THREE_DAYS)}
        >
          3 Days
        </Button>
        <Button
          variant={
            currentMetric === StatsTopMetric.ONE_WEEK ? "default" : "outline"
          }
          size="sm"
          onClick={() => onMetricChange(StatsTopMetric.ONE_WEEK)}
        >
          7 Days
        </Button>
      </div>
    </div>
  );
}
