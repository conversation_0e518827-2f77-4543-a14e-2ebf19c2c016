"use client";

import { formatHash } from "@/helpers/format";
import { useEffect, useState } from "react";
import { <PERSON>, Pie, Pie<PERSON><PERSON>, ResponsiveC<PERSON>r, Tooltip } from "recharts";

interface StatsChartProps {
  data: any[];
  dataKey: string;
  nameKey: string;
  colors: string[];
  valueFormatter?: (value: any) => string;
}

export function StatsChart({
  data,
  dataKey,
  nameKey,
  colors,
  valueFormatter = (value) => value.toString(),
}: StatsChartProps) {
  const [chartData, setChartData] = useState<any[]>([]);

  // Process data to ensure values are numbers, not strings
  useEffect(() => {
    if (data && data.length > 0) {
      const processedData = data.slice(0, 10).map((item) => {
        return {
          ...item,
          // Ensure the dataKey value is a number
          [dataKey]:
            typeof item[dataKey] === "string"
              ? Number.parseFloat(item[dataKey])
              : item[dataKey],
          // Ensure percentage is a number
          percentage:
            typeof item.percentage === "string"
              ? Number.parseFloat(item.percentage)
              : item.percentage,
        };
      });
      setChartData(processedData);
    }
  }, [data, dataKey]);

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-2 border shadow-sm rounded-md text-xs">
          <p className="font-medium">{`${formatHash(data[nameKey])}`}</p>
          <p>{`${
            dataKey === "count" ? "Transactions" : "Gas"
          }: ${valueFormatter(data[dataKey])}`}</p>
          <p>{`Percentage: ${Number(data.percentage).toFixed(2)}%`}</p>
        </div>
      );
    }
    return null;
  };

  if (!chartData || chartData.length === 0) {
    return (
      <div className="h-[280px] w-full flex items-center justify-center text-gray-500">
        Loading chart data...
      </div>
    );
  }

  return (
    <div className="h-[280px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={true}
            outerRadius={80}
            innerRadius={30}
            fill="#8884d8"
            dataKey={dataKey}
            nameKey={nameKey}
            label={({ name, percent }) =>
              `${formatHash(name)}: ${(percent * 100).toFixed(0)}%`
            }
          >
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={colors[index % colors.length]}
                stroke="#fff"
                strokeWidth={1}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
