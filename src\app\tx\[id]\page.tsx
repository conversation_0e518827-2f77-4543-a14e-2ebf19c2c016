"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { NATIVE_SYMBOL, ZERO_ADDRESS } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatEtherValue,
  formatTransactionType,
  normalizeAddress,
} from "@/helpers/format"
import { useTransactionAndTransactionReceiptByTxHash } from "@/hooks/useTransactions"
import { useGetCode, useSearchCode } from "@/hooks/useVerifyContract"
import { BytecodeType } from "@/types/verify-contract"
import { formatDistanceToNow } from "date-fns"
import { formatUnits, Interface } from "ethers"
import {
  AlertTriangle,
  CheckCircle2,
  Clock,
  FileIcon,
  FireExtinguisher,
  Flame,
  Hash,
  Minus,
  Plus,
} from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { numericFormatter } from "react-number-format"
import { TokenTransfers } from "./components/erc20-token-transfer"
import { InputDataViewer } from "./components/input-data-viewer"
import { InternalTransactionsViewer } from "./components/internal-transaction"
import { LogsViewer } from "./components/logs-viewer"
import { StateTab } from "./components/state-tabs"
import { TokenTransferDecoder } from "./components/token-transfer-decoder"

export default function TransactionPage() {
  const params = useParams()
  const id = params.id as string
  const { data, isLoading, isError, error } =
    useTransactionAndTransactionReceiptByTxHash(id)
  const [contractAbi, setContractAbi] = useState<any>(null)
  const [decodedFunction, setDecodedFunction] = useState<{
    name: string
    params: Array<{ name: string; value: string; type: string }>
  } | null>(null)
  const [previousCode, setPreviousCode] = useState<string | null>(null)

  // Only get code if we have a transaction with a 'to' address
  const { data: contractCode } = useGetCode(data?.transaction?.to || "")
  const { mutate: searchCode } = useSearchCode()
  const [moreDetails, setMoreDetails] = useState<boolean>(false)

  // Check if the 'to' address is a contract by getting its code
  useEffect(() => {
    if (
      contractCode &&
      contractCode !== "0x" &&
      contractCode !== previousCode
    ) {
      searchCode(
        {
          bytecode: contractCode,
          bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
        },
        {
          onSuccess: (data) => {
            console.log("Search code completed successfully", data)
            setPreviousCode(contractCode)

            // Try to decode the transaction input
            if (data?.result?.sources?.[0]?.abi) {
              try {
                const abiData = JSON.parse(data.result.sources[0].abi)
                setContractAbi(abiData)

                // Try to decode the function call if it have input data
                if (transaction?.input && transaction.input !== "0x") {
                  decodeInputData(transaction.input, abiData)
                }
              } catch (e) {
                console.error("Error parsing ABI:", e)
              }
            }
          },
          onError: (error) => {
            console.error("Error searching code:", error)
          },
        },
      )
    }
  }, [contractCode, previousCode, searchCode, data])

  const decodeInputData = (input: string, abi: any[]) => {
    if (!input || input === "0x" || !abi || !abi.length) return

    try {
      const iface = new Interface(abi)
      const functionFragment = iface.getFunction(input.slice(0, 10))

      if (functionFragment) {
        const decoded = iface.decodeFunctionData(functionFragment, input)

        // Format the decoded parameters
        const params = functionFragment.inputs.map((input, index) => {
          return {
            name: input.name,
            type: input.type,
            value: decoded[index].toString(),
          }
        })

        setDecodedFunction({
          name: functionFragment.name,
          params,
        })
      }
    } catch (error) {
      console.error("Error decoding input data:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (isError || !data?.transaction) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 mb-6">
          <AlertTriangle className="h-6 w-6 text-red-500" />
          <h1 className="text-2xl font-bold text-red-500">Error</h1>
        </div>
        <Card className="bg-red-50 border-red-200 p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <h3 className="font-semibold text-red-700">
                Transaction not found or invalid!
              </h3>
              <p className="text-red-600 mt-1">
                {error instanceof Error
                  ? error.message
                  : "An unknown error occurred"}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Link
              href="/"
              className="text-blue-600 hover:underline font-medium"
            >
              Return to Home
            </Link>
          </div>
        </Card>
      </div>
    )
  }

  const transaction = data?.transaction
  const transactionReceipt = data?.receipt

  const formattedDate = new Date(transaction.timestamp).toLocaleString()

  const timestamp = transaction?.timestamp
    ? new Date(transaction.timestamp)
    : null

  const timeAgo =
    timestamp && !isNaN(timestamp.getTime())
      ? formatDistanceToNow(timestamp, { addSuffix: true })
      : "Unknown time"

  const ethValue = formatUnits(transaction.value || "0", "ether")

  const gweiValue = transaction.gasPrice
    ? formatUnits(transaction.gasPrice, "gwei")
    : "N/A"
  const formattedGweiValue = transaction.gasPrice
    ? Number.parseFloat(gweiValue).toLocaleString(undefined, {
        maximumFractionDigits: 2,
      })
    : "N/A"

  const formattedType = transaction.type
    ? formatTransactionType(transaction.type)
    : ""

  const formattedTransactionFee =
    transactionReceipt?.gasPrice && transactionReceipt?.cumulativeGasUsed
      ? formatEtherValue(
          (
            Number.parseFloat(transactionReceipt?.gasPrice) *
            Number.parseFloat(transactionReceipt?.cumulativeGasUsed)
          ).toFixed(0),
        )
      : "N/A"

  const destIsSmartContract = contractCode && contractCode !== "0x"

  const formattedBurntFee =
    transactionReceipt?.gasPrice && transactionReceipt?.cumulativeGasUsed
      ? formatEtherValue(
          (
            Number.parseFloat(transactionReceipt?.gasPrice) *
            Number.parseFloat(transactionReceipt?.cumulativeGasUsed) *
            (destIsSmartContract ? 0.9 : 1)
          ).toFixed(0),
        )
      : "N/A"

  const formattedIncentiveFees =
    transactionReceipt?.gasPrice && transactionReceipt?.cumulativeGasUsed
      ? formatEtherValue(
          (
            Number.parseFloat(transactionReceipt?.gasPrice) *
            Number.parseFloat(transactionReceipt?.cumulativeGasUsed) *
            (destIsSmartContract ? 0.1 : 0)
          ).toFixed(0),
        )
      : "N/A"

  const formatAddress = (address: string) => {
    if (!address) return "N/A"
    const normalizedAddress = normalizeAddress(address)
    if (!normalizedAddress) return "Invalid Address"
    return (
      <div className="flex items-center gap-2">
        <Link
          href={PATH_ROUTER.ADDRESS_DETAIL(normalizedAddress)}
          className="text-blue-600 hover:underline font-medium"
        >
          {normalizedAddress}
        </Link>
        <CopyTooltip content={normalizedAddress} />
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-2 mb-6">
        <FileIcon className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Transaction Details</h1>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {transactionReceipt?.logs && transactionReceipt.logs.length > 0 && (
            <TabsTrigger value="logs">
              Logs ({transactionReceipt.logs.length})
            </TabsTrigger>
          )}
          <TabsTrigger value="state">State</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="card">
            <Card className="border rounded-lg overflow-hidden">
              <div className="p-4 bg-muted/30">
                <div className="flex items-center gap-2">
                  <Hash className="h-5 w-5 text-muted-foreground" />
                  <h2 className="text-lg font-semibold">
                    Transaction Information
                  </h2>
                </div>
              </div>

              <Table>
                <TableBody>
                  <TableRow className="border-b-0">
                    <TableCell className="font-medium w-1/4">
                      Transaction Hash:
                    </TableCell>
                    <TableCell className="break-all">
                      <div className="flex items-center gap-2">
                        <span>{transaction.hash}</span>
                        <CopyTooltip content={transaction.hash} />
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow className="border-b-0">
                    <TableCell className="font-medium">Status:</TableCell>
                    <TableCell>
                      {transactionReceipt?.status == 1 && (
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
                          Success
                        </span>
                      )}
                      {transactionReceipt?.status == 2 && (
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
                          Failed
                        </span>
                      )}
                    </TableCell>
                  </TableRow>

                  <TableRow className="border-b-0">
                    <TableCell className="font-medium">Block:</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <CopyTooltip
                          content={transaction.blockNumber.toString()}
                        />
                        <Link
                          href={PATH_ROUTER.BLOCK_DETAIL(
                            transaction.blockNumber,
                          )}
                          className="text-blue-600 hover:underline font-medium"
                        >
                          {transaction.blockNumber}
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Timestamp:</TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="flex items-center gap-1 text-muted-foreground">
                            <Clock className="h-4 w-4" />
                            {timeAgo}
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formattedDate}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>

                  <TokenTransferDecoder
                    transaction={transaction}
                    decodedFunction={decodedFunction}
                    contractAbi={contractAbi}
                  />
                </TableBody>
              </Table>

              <Separator />

              <Table>
                <TableBody>
                  <TableRow className="border-b-0">
                    <TableCell className="font-medium w-1/4">From:</TableCell>
                    <TableCell className="break-all">
                      {formatAddress(transaction.from || ZERO_ADDRESS)}
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">To:</TableCell>
                    <TableCell className="break-all">
                      {formatAddress(transaction.to || ZERO_ADDRESS)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>

              <Separator />

              {transaction?.createdBy && (
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Created By:</TableCell>
                      <TableCell className="break-all">
                        {transaction?.createdBy}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              )}

              <Separator />

              <Table>
                <TableBody>
                  <TableRow className="border-b-0">
                    <TableCell className="font-medium">Value:</TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="font-medium">
                            {formatEtherValue(transaction.value)}{" "}
                            {NATIVE_SYMBOL}
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{ethValue}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>

                  <TableRow className="border-b-0">
                    <TableCell className="font-medium w-1/4">
                      Transaction Fee:
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="font-medium">
                            {formattedTransactionFee} {NATIVE_SYMBOL}
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formattedTransactionFee}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>

                  <TableRow className="border-b-0">
                    <TableCell className="font-medium w-1/4">
                      Gas Price:
                    </TableCell>
                    <TableCell>{formattedGweiValue} Gwei</TableCell>
                  </TableRow>
                </TableBody>
              </Table>

              <Separator />

              <Table>
                <TableBody></TableBody>
              </Table>
            </Card>
          </div>

          <TokenTransfers transactionHash={id} />

          {transaction.internalTxns && transaction.internalTxns.length > 0 && (
            <InternalTransactionsViewer
              internalTxns={transaction.internalTxns}
            />
          )}

          {moreDetails && (
            <div className="card mt-3">
              <Card className="border rounded-lg overflow-hidden">
                <Table>
                  <TableBody>
                    <TableRow className="border-b-0">
                      <TableCell className="font-medium">
                        Gas Limit & Usage by Txn:
                      </TableCell>
                      <TableCell>
                        <span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger className="font-medium">
                                {transaction?.gas
                                  ? numericFormatter(`${transaction.gas}`, {
                                      thousandSeparator: true,
                                      decimalScale: 0,
                                    })
                                  : "N/A"}
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  The amount of GAS supplied for this
                                  transaction to happen
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>{" "}
                          |{" "}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger className="font-medium">
                                {transactionReceipt?.cumulativeGasUsed
                                  ? numericFormatter(
                                      `${transactionReceipt?.cumulativeGasUsed}`,
                                      {
                                        thousandSeparator: true,
                                        decimalScale: 0,
                                      },
                                    )
                                  : "N/A"}
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  The amount of GAS used by this specific
                                  transaction alone
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </span>
                      </TableCell>
                    </TableRow>

                    <TableRow className="border-b-0">
                      <TableCell className="font-medium">Gas Fees:</TableCell>
                      <TableCell className="flex items-center gap-1 text-muted-foreground">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Base:{" "}
                          {transaction.gasPrice
                            ? formatUnits(transaction.gasPrice, "gwei") +
                              " Gwei"
                            : "N/A"}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Max:{" "}
                          {transaction.maxFeePerGas
                            ? formatUnits(transaction.maxFeePerGas, "gwei") +
                              " Gwei"
                            : "N/A"}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Max Priority:{" "}
                          {transaction.maxPriorityFeePerGas
                            ? formatUnits(
                                transaction.maxPriorityFeePerGas,
                                "ether",
                              ) +
                              " " +
                              NATIVE_SYMBOL
                            : "N/A"}
                        </span>
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell className="font-medium w-1/4">
                        Burnt & Incentives Fees:
                      </TableCell>
                      <TableCell className="flex items-center gap-1 text-muted-foreground">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <Flame className="h-4 w-4"></Flame>
                          Burnt: {formattedBurntFee} {NATIVE_SYMBOL}
                        </span>

                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <FireExtinguisher className="h-4 w-4"></FireExtinguisher>
                          Incentive: {formattedIncentiveFees} {NATIVE_SYMBOL}
                        </span>
                      </TableCell>
                    </TableRow>

                    <TableRow className="border-b-0">
                      <TableCell className="font-medium">
                        Other Attributes:
                      </TableCell>
                      <TableCell className="flex items-center gap-1 text-muted-foreground">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Txn Type: {transaction.type || "-"} (
                          {formattedType || "-"})
                        </span>

                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Nonce: {transaction.nonce}
                        </span>

                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Position In Block:{" "}
                          {transactionReceipt?.index !== undefined &&
                          transactionReceipt?.index !== null
                            ? transactionReceipt.index
                            : "-"}
                        </span>

                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Chain ID: {transaction?.chainId || "N/A"}
                        </span>
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell className="font-medium">Input Data:</TableCell>
                      <TableCell className="break-all">
                        <InputDataViewer
                          input={transaction.input}
                          decodedFunction={decodedFunction}
                          hasAbi={!!contractAbi}
                        />
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </Card>
            </div>
          )}

          <div className="card mt-3">
            <Card className="border rounded-lg overflow-hidden">
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium w-1/4">
                      More Details:
                    </TableCell>
                    <TableCell>
                      <Button
                        onClick={(e) => {
                          setMoreDetails(!moreDetails)
                          e.stopPropagation()
                        }}
                        className="flex items-center"
                        variant="link"
                      >
                        {!moreDetails ? (
                          <Plus className="h-4 w-4" />
                        ) : (
                          <Minus className="h-4 w-4" />
                        )}
                        Click to show more
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="logs">
          {transactionReceipt?.logs && transactionReceipt.logs.length > 0 ? (
            <LogsViewer logs={transactionReceipt.logs} />
          ) : (
            <Card className="border rounded-lg p-6">
              <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                <FileIcon className="h-12 w-12 mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">
                  No logs found for this transaction
                </h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  This transaction either did not call a smart contract or no
                  events were emitted during execution. If this transaction is
                  recent, try refreshing the page after a few moments.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Refresh
                </Button>
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="state">
          <StateTab
            transaction={data?.transaction}
            isLoading={isLoading}
            isError={isError}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
