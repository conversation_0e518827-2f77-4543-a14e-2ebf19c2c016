"use client";

import { ContractMethodCard } from "@/components/contract/contract-method-card";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import type { ContractMethod, Source } from "@/types/contract";
import { Search } from "lucide-react";
import { useState } from "react";

interface ReadTabProps {
  methods?: ContractMethod[];
  source?: Source;
  address: string;
}

export function ReadTab({ methods = [], source, address }: ReadTabProps) {
  const [searchTerm, setSearchTerm] = useState("");

  // Parse ABI from source if available
  const parsedMethods = source
    ? JSON.parse(source.abi)
        ?.filter(
          (item: ContractMethod) =>
            item.type === "function" &&
            (item.stateMutability === "view" ||
              item.stateMutability === "pure"),
        )
        //eslint-disable-next-line @typescript-eslint/no-explicit-any
        ?.map((item: any) => ({
          name: item.name,
          signature: item.name.substring(0, 8),
          inputs: item.inputs,
          outputs: item.outputs,
          stateMutability: item.stateMutability,
          type: item.type,
        }))
    : methods;

  const filteredMethods = parsedMethods.filter((method: ContractMethod) =>
    method.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center justify-between">
          <span>Read Contract Information</span>
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by method name"
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {filteredMethods.length > 0 ? (
          filteredMethods.map((method: ContractMethod, index: number) => (
            <ContractMethodCard
              key={`${method.name}-${index}`}
              method={method}
              contractAddress={address}
              index={index}
            />
          ))
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm
              ? "No methods found matching your search"
              : "No read methods available"}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
