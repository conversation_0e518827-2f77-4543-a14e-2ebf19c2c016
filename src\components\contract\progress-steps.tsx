import { Dispatch, SetStateAction } from "react";

interface ProgressStepsProps {
  currentStep: number;
  steps: { id: number; label: string }[];
  setStep: Dispatch<SetStateAction<number>>;
}

export function ProgressSteps({
  currentStep,
  steps,
  setStep,
}: ProgressStepsProps) {
  return (
    <div className="flex justify-center mb-8">
      <div className="flex items-center">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div
              className={`flex items-center ${
                currentStep === 2 && step.id === 1 ? "cursor-pointer" : ""
              }`}
              onClick={() => {
                if (currentStep === 2 && step.id === 1) {
                  setStep(1);
                }
              }}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep >= step.id
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                } `}
              >
                {step.id}
              </div>
              <span className="mx-2 text-sm md:text-base font-medium">
                {step.label}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className="w-8 h-0.5 bg-muted mx-2"></div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
