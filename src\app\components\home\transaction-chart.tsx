"use client"

import { ChartContainer, ChartTooltip } from "@/components/ui/chart"
import { Area, AreaChart, ResponsiveContainer, XAxis, YA<PERSON>s } from "recharts"

interface TransactionHistoryItem {
  count: number
  date: string
}

interface TransactionChartProps {
  transactionHistory?: TransactionHistoryItem[]
  price?: number
}

export function TransactionChart({
  transactionHistory = [],
}: TransactionChartProps) {
  const chartData = transactionHistory.map((item) => ({
    date: new Date(item.date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    }),
    transactions: item.count,
  }))

  const data =
    chartData.length > 0 ? chartData : [{ date: "", transactions: 0, price: 0 }]

  const maxValue = Math.max(...chartData.map((item) => item.transactions), 1)
  const yAxisDomain = [0, Math.ceil(maxValue * 1.2)] // Add 20% padding to the top

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white border border-gray-200 rounded-md shadow-md p-3 text-sm">
          <p className="text-gray-600 font-medium">{label}</p>
          <p className="font-semibold">
            Transactions: {payload[0].value.toLocaleString()}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[100px] w-full">
      <ChartContainer
        config={{
          transactions: {
            label: "Transactions",
            color: "hsl(var(--primary))",
          },
        }}
        className="h-full w-full"
      >
        <ResponsiveContainer width="100%" height={100}>
          <AreaChart data={data} margin={{ right: 10, left: 5 }}>
            <XAxis
              dataKey="date"
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />

            <YAxis
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value}`}
              width={30}
              domain={yAxisDomain}
            />
            <ChartTooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="transactions"
              stroke="hsl(var(--primary))"
              fill="hsl(var(--primary)/0.2)"
              strokeWidth={2}
              animationDuration={1000}
              isAnimationActive={true}
            />
          </AreaChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  )
}
