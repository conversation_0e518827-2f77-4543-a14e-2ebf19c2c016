.header {
  --header-spacing: var(--spacing);
  --header-transition: 0.6s var(--cubic);
  padding: var(--header-spacing);
  z-index: 100;

  &:not(.home) {
    --search-width: 100%;
    --search-max-width: 100%;

    .heading {
      height: 0;
      opacity: 0;
      margin-bottom: 0;
    }

    .content {
      padding-bottom: var(--content-padding);
    }

    [data-overview] {
      display: none;
    }

    [data-line] {
      opacity: 0;
    }
  }
}

.logo {
  display: block;

  img {
    height: 100%;
    width: 300px;
  }

  @media screen and (max-width: 500px) {
    height: 24px;
  }

  @media screen and (max-width: 450px) {
    height: 18px;
  }
}

.navButton {
  border: 1px solid hsl(var(--white) / 0.1);
  margin-left: calc(var(--content-padding) * -0.5);

  @media screen and (min-width: 1024px) {
    display: none !important;
  }

  @media screen and (max-width: 550px) {
    margin-left: 0;
  }
}

.bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  position: relative;
  z-index: 100;
  padding-inline: 0.75em;
  line-height: 1;

  & > * {
    display: flex;
    align-items: center;
    gap: 0.75em;
    color: hsl(var(--muted-foreground));
  }

  .price {
    color: hsl(var(--primary));
  }

  .gas {
    position: relative;
    cursor: pointer;

    strong {
      color: hsl(var(--primary));
    }

    &:hover {
      .fee {
        opacity: 1;
      }
    }
  }

  .fee {
    transition: opacity 0.2s ease-in-out;
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    background-color: hsl(var(--foreground) / 0.5);
    color: hsl(var(--white));
    backdrop-filter: blur(10px);
    padding: 0.75em 1em;
    border-radius: calc(var(--radius) - 8px);
    opacity: 0;
    pointer-events: none;
    font-size: 0.9em;
    user-select: none;

    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1em;

      & + li {
        margin-top: 0.5em;
      }
    }

    span {
      opacity: 0.75;
    }
  }
}

.content {
  --content-padding: calc(24px + 8 * (100vw - 320px) / 1080);
  transition: padding-bottom var(--header-transition);
  background: url("/assets/images/header-background.png") no-repeat center
    center;
  background-size: cover;
  border-radius: var(--radius);
  padding: var(--content-padding);
  padding-bottom: calc(var(--content-padding) * 1.5);
  margin-top: calc(var(--header-spacing) * 0.85);
  color: hsl(var(--white));
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--content-padding);

  @media screen and (max-width: 550px) {
    gap: calc(var(--content-padding) * 0.5);
  }
}

.theme {
  display: flex;
  align-items: center;
  gap: 0.5em;

  svg {
    width: 1.25em;
    height: 1.25em;
  }
}

.heading {
  transition: height var(--header-transition), opacity var(--header-transition),
    margin-bottom var(--header-transition);
  text-align: center;
  height: calc(124px + 70 * (100vw - 320px) / 1080);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75em;
  margin-bottom: calc(var(--content-padding) * -0.75);

  .small {
    background: hsl(var(--white) / 0.1);
    border-radius: 100px;
    padding: 0.3em 1em 0.25em;
    padding-left: 0.5em;
    font-weight: 400;

    img {
      display: inline-block;
      width: 1.45em;
      vertical-align: middle;
      position: relative;
      top: -0.075em;
      margin-right: 0.25em;
    }
  }

  h1 {
    font-size: calc(24px + 24 * (100vw - 320px) / 1080);
    letter-spacing: -0.015em;
    font-weight: 400;
    line-height: 1.2;

    img {
      display: inline-block;
      height: 1.3em;
      margin-left: 0.25em;
      width: auto;
    }
  }
}

.overview {
  --overview-padding: var(--content-padding);
  display: flex;
  margin-top: calc(var(--overview-padding) * 1.25);
  gap: calc(var(--overview-padding) * 0.75);

  @media screen and (max-width: 1023px) {
    flex-direction: column;
  }

  .item {
    background: hsl(var(--white) / 0.05);
    flex: 1;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    border-radius: var(--radius);
    padding: var(--overview-padding);
    display: flex;
    align-items: stretch;
    gap: var(--overview-padding);
    white-space: nowrap;
  }

  .label {
    font-weight: 300;
    opacity: 0.5;
    font-size: calc(16px + 2 * (100vw - 320px) / 1080);
  }

  .value {
    font-size: calc(24px + 16 * (100vw - 320px) / 1080);
    font-weight: 600;
  }

  .message {
    font-weight: 300;
    opacity: 0.5;
    font-size: calc(14px + 2 * (100vw - 320px) / 1080);

    strong {
      font-weight: 600;
    }
  }

  .right {
    background: hsl(var(--white) / 0.05);
    flex: 1;
    border-radius: var(--radius);
    margin: calc(var(--overview-padding) * -0.25);
    margin-left: 0;
    overflow: hidden;

    @media screen and (max-width: 500px) {
      display: none;
    }

    & > * {
      mask: linear-gradient(
        to right,
        transparent 6px,
        black 30px,
        black calc(100% - 30px),
        transparent calc(100% - 6px)
      );
    }
  }
}
