import { isAddress } from "viem"
import { z } from "zod"

export const baseContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Please enter a valid email address"),
  additionalComment: z.string().optional(),
})

const additionalTagSchema = z.object({
  address: z
    .string()
    .min(1, "Address is required")
    .refine(isAddress, "Invalid Ethereum address"),
  nameTag: z.string().min(1, "Name tag is required"),
  website: z.string().url("Invalid website URL"),
  category: z.string().optional(),
  description: z.string().min(1, "Description is required"),
})

export const addNameTagSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  companyName: z.string().min(1, "Company name is required"),
  companyWebsite: z.string().url("Invalid website URL"),
  isYourAddress: z.enum(["personal", "other"]).optional(),
  smartContract: z
    .string()
    .min(1, "Address is required")
    .refine(isAddress, "Invalid Ethereum address"),
  suggestedNameTag: z.string().min(1, "Suggested name tag is required"),
  website: z.string().url("Invalid website URL"),
  categoryLabel: z.string().optional(),
  shortDescription: z.string().min(1, "Short description is required"),
  additionalComment: z.string().optional(),
  additionalTags: z.array(additionalTagSchema).optional(),
  verificationSignature: z.string().optional(),
  verificationMessage: z.string().optional(),
})

export const removeNameTagSchema = baseContactSchema.extend({
  address: z.string().min(1, "Address is required"),
  message: z.string().min(1, "Message is required"),
})

// Schema for General Inquiry (1.a)
export const generalInquirySchema = baseContactSchema.extend({
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(1, "Message is required"),
})

// Schema for Update Token Info (2.a)
export const updateTokenInfoSchema = baseContactSchema.extend({
  tokenAddress: z.string().min(1, "Token address is required"),
  tokenName: z.string().min(1, "Token name is required"),
  tokenSymbol: z.string().min(1, "Token symbol is required"),
  tokenDecimals: z.string().min(1, "Token decimals is required"),
})

export type AddNameTagFormValues = z.infer<typeof addNameTagSchema>
export type RemoveNameTagFormValues = z.infer<typeof removeNameTagSchema>
export type GeneralInquiryFormValues = z.infer<typeof generalInquirySchema>
export type UpdateTokenInfoFormValues = z.infer<typeof updateTokenInfoSchema>
