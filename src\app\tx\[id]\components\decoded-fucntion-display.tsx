interface DecodedFunctionProps {
    decodedFunction: {
      name: string
      params: Array<{ name: string; value: string; type: string }>
    }
  }
  
  export function DecodedFunctionDisplay({ decodedFunction }: DecodedFunctionProps) {
    return (
      <div className="space-y-4">
        <div className="font-medium">
          Function: <span className="text-blue-600">{decodedFunction.name}</span>
        </div>
        {decodedFunction.params.length > 0 && (
          <div className="space-y-2">
            <div className="font-medium">Parameters:</div>
            <div className="space-y-1">
              {decodedFunction.params.map((param, index) => (
                <div key={index} className="grid grid-cols-12 gap-2">
                  <div className="col-span-3 text-muted-foreground">
                    {param.name} ({param.type}):
                  </div>
                  <div className="col-span-9 break-all">{param.value}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }
  
  