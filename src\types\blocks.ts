export interface Block {
  blockStatus: string
  difficulty: string
  extraData: string
  gasLimit: string
  gasUsed: string
  hash: string
  id: number
  logsBloom: string
  miner: string
  nonce: string
  number: number
  parentHash: string
  receiptsRoot: string
  sha3Uncles: string
  size: string
  stateRoot: string
  timestamp: string
  totalDifficulty: string
  transactionsRoot: string
  uncles: string[]
  transactionCount: number
  epochNumber: number
}

export interface BlockList {
  id: number
  difficulty: string
  extraData: string
  gasLimit: string
  gasUsed: string
  hash: string
  logsBloom: string
  miner: string
  nonce: string
  number: number
  parentHash: string
  receiptsRoot: string
  sha3Uncles: string
  size: string
  stateRoot: string
  timestamp: string
  totalDifficulty: string
  transactionsRoot: string
  uncles: any
  transactionCount: number
  blockStatus: string
  baseFeePerGas: string
  burnedFees: string
  epochNumber: number
  validators: Validator[]
}

export interface Validator {
  id: number
  createdAt: string
  updatedAt: string
  operatorAddress: string
  consensusPubkey: ConsensusPubkey
  jailed: boolean
  status: string
  tokens: string
  delegatorShares: string
  description: Description
  unbondingHeight: string
  unbondingTime: string
  commission: Commission
  minSelfDelegation: string
  lastUpdatedAt: string
  epochNumber: number
}

export interface ConsensusPubkey {
  "@type": string
  key: string
}

export interface Description {
  moniker: string
  identity: string
  website: string
  security_contact: string
  details: string
}

export interface Commission {
  commission_rates: CommissionRates
  update_time: string
}

export interface CommissionRates {
  rate: string
  max_rate: string
  max_change_rate: string
}

export interface BlockListResponse {
  id: number
  jsonrpc: string
  result: {
    data: BlockList[]
    metadata: {
      total: number
      page: number
      limit: number
    }
  }
  mapNameTag: {
    [address: string]: string
  }
}

export interface BlockDetailsResponse {
  result: Block
  id: number
  jsonrpc: string
}

export interface Transaction {
  blockHash: string
  blockNumber: string
  from: string
  gas: string
  gasPrice: string
  maxFeePerGas: string
  maxPriorityFeePerGas: string
  hash: string
  input: string
  nonce: string
  to: string
  transactionIndex: string
  value: string
  type: string
  accessList: []
  chainId: string
  v: string
  r: string
  s: string
}
