import { envs } from "@/constants/envs";
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { cookieStorage, createStorage } from "@wagmi/core";
import { define<PERSON>hain } from "viem";

export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Define the custom Helios chain
export const heliosChain = define<PERSON>hain({
  id: Number(envs.HELIOS_CHAIN_ID),
  name: "Helios",
  network: "helios",
  nativeCurrency: {
    decimals: 18,
    name: "<PERSON><PERSON>",
    symbol: "HLS",
  },
  rpcUrls: {
    default: { http: [envs.HELIOS_RPC_URL] },
    public: { http: [envs.HELIOS_RPC_URL] },
  },
  blockExplorers: {
    default: { name: "Custom Explorer", url: "http://***********:4000" },
  },
});

// // Convert the Helios chain to AppKitNetwork format
// const heliosAppKitNetwork = {
//   id: heliosChain.id,
//   name: heliosChain.name,
//   chainNamespace: "eip155",
//   caipNetworkId: `eip155:${heliosChain.id}`,
//   nativeCurrency: heliosChain.nativeCurrency,
//   rpcUrls: heliosChain.rpcUrls,
//   blockExplorers: heliosChain.blockExplorers
// };

// Use only the Helios chain in the networks array
export const networks = [heliosChain];

export const wagmiAdapter = new WagmiAdapter({
  storage: createStorage({
    storage: cookieStorage,
  }),
  ssr: true,
  projectId,
  networks,
});

export const config = wagmiAdapter.wagmiConfig;
