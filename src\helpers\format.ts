import { DECIMAL } from "@/constants"
import BigNumber from "bignumber.js"
import { formatDistanceToNow } from "date-fns"
import { formatUnits, getAddress } from "ethers"
import { hexToNumber, isHex } from "viem"
export const formatHash = (
  hash?: string | null,
  length: number = 6,
  suffixLength: number = 4,
): string => {
  if (!hash || hash.length < length + suffixLength) return hash ?? ""
  return `${hash.slice(0, length)}...${hash.slice(-suffixLength)}`
}

export function formatTimestamp(timestamp: string): string {
  const date = new Date(timestamp)
  return formatDistanceToNow(date, { addSuffix: true })
}

export function formatWeiToEther(
  value: string | number | bigint,
  decimals: number = 6,
  unit: number = 18,
): string {
  try {
    const isNegative =
      typeof value === "string" ? value.startsWith("-") : value < 0
    const absoluteValue =
      typeof value === "bigint"
        ? isNegative
          ? -value
          : value
        : BigInt(Math.abs(Number(value)))
    const divisor = BigInt(10) ** BigInt(unit)

    const quotient = absoluteValue / divisor
    const remainder = absoluteValue % divisor

    let formattedRemainder = remainder
      .toString()
      .padStart(unit, "0")
      .slice(0, decimals)
    formattedRemainder = formattedRemainder.replace(/0+$/, "")

    const integerPart = quotient
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    const fractionalPart = formattedRemainder ? `.${formattedRemainder}` : ""

    return isNegative
      ? `-${integerPart}${fractionalPart}`
      : `${integerPart}${fractionalPart}`
  } catch (error) {
    console.error("Error formatting wei to ether:", error)
    return "0"
  }
}

export const hexToDecimal = (
  value: string | number | null | undefined,
): string => {
  if (value === null || value === undefined) {
    return "N/A"
  }

  if (typeof value === "number") {
    return value.toString()
  }

  if (isHex(value)) {
    try {
      return hexToNumber(value).toString()
    } catch (error) {
      console.error("Error converting hex to number:", error)
      return "Invalid hex"
    }
  }

  return value
}

export const formatAge = (dateString: string): string => {
  const now = new Date()
  const createdAt = new Date(dateString)
  const diffInSeconds = Math.floor((now.getTime() - createdAt.getTime()) / 1000)

  if (diffInSeconds < 60) return `${diffInSeconds} secs ago`
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} mins ago`
  if (diffInSeconds < 86400)
    return `${Math.floor(diffInSeconds / 3600)} hours ago`
  return `${Math.floor(diffInSeconds / 86400)} days ago`
}

export const formatNumberWithNotation = (
  number: BigNumber.Value | null,
  decimalPlace: DECIMAL | number = DECIMAL.DEFAULT,
  minimumNumberPrefix: string = "~",
  roundingMode: BigNumber.RoundingMode = BigNumber.ROUND_HALF_EVEN,
): string | null => {
  const bn = new BigNumber(number || "").absoluteValue()
  const isNegative = new BigNumber(number || "").isNegative()
  const negativeSymbol = isNegative ? "-" : ""

  const units = [
    { v: 1, s: "" },
    { v: Math.pow(10, 3), s: "K" },
    { v: Math.pow(10, 6), s: "M" },
    { v: Math.pow(10, 9), s: "B" },
    { v: Math.pow(10, 12), s: "T" },
  ]

  if (bn.isNaN()) return null
  if (bn.eq(0)) return "0"

  let unit = units[0]
  for (let i = units.length - 1; i >= 0; i--) {
    if (bn.gte(units[i].v)) {
      unit = units[i]
      break
    }
  }

  const minimumNumber = new BigNumber(1).div(Math.pow(10, decimalPlace))
  if (bn.div(unit.v).lt(minimumNumber)) {
    return `${minimumNumberPrefix} ${negativeSymbol}${minimumNumber.toString(
      10,
    )}`
  }

  const shortenedNumberByUnit = bn.div(unit.v)
  return `${negativeSymbol}${shortenedNumberByUnit
    .dp(decimalPlace, roundingMode)
    .toString(10)}${unit.s}`
}

export const normalizeAddress = (address: string): string | null => {
  try {
    // Remove '0x' if present
    const cleaned = address.toLowerCase().replace(/^0x/, "")

    // Check zero address
    if (/^0{40}$/.test(cleaned)) {
      return "******************************************"
    }

    // Check if valid hex and exactly 40 chars
    if (!/^([0-9a-f]{40})$/.test(cleaned)) {
      return null // Invalid input
    }

    // Apply checksum
    return getAddress("0x" + cleaned)
  } catch {
    return null // getAddress failed (invalid address)
  }
}
export const formatEtherValue = (value: string | null | undefined): string => {
  if (!value) return "0"

  try {
    // Format the value in ether units
    const etherValue = formatUnits(value, "ether")

    // Parse as float and format with 4 decimal places
    const formattedValue = Number.parseFloat(etherValue).toLocaleString(
      undefined,
      {
        maximumFractionDigits: 18,
        minimumFractionDigits: 0,
      },
    )

    return formattedValue
  } catch (error) {
    console.error("Error formatting ether value:", error)
    return "0"
  }
}

// Helper function to format gwei values
export const formatGweiValue = (value: string | null | undefined): string => {
  if (!value) return "0"

  try {
    // Format the value in gwei units
    const gweiValue = formatUnits(value, "gwei")

    // Parse as float and format with 2 decimal places
    const formattedValue = Number.parseFloat(gweiValue).toLocaleString(
      undefined,
      {
        maximumFractionDigits: 2,
        minimumFractionDigits: 0,
      },
    )

    return formattedValue
  } catch (error) {
    console.error("Error formatting gwei value:", error)
    return "0"
  }
}

export const weiToGwei = (wei: string): string => {
  const weiValue = wei.startsWith("0x") ? BigInt(wei) : BigInt(wei)
  const gweiValue = Number(weiValue) / 1e9
  return gweiValue.toFixed(2)
}

export const formatTransactionType = (t: number | string): string => {
  const typeN = Number(t)

  switch (typeN) {
    case 1:
      return "EIP-2930"
    case 2:
      return "EIP-1559"
    case 3:
      return "EIP-4844"
  }
  return ""
}

export const truncateAddress = (address: string) => {
  if (!address) return ""
  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`
}
