"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search } from "lucide-react"
import { useAccount, useConnect, useDisconnect } from "wagmi"
import type { ContractMethod, Source } from "@/types/contract"
import { ContractMethodCard } from "@/components/contract/contract-method-card"

interface WriteTabProps {
  source?: Source
  address: string
}

export function WriteTab({ source, address }: WriteTabProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const { address: walletAddress, isConnected } = useAccount()
  const { connect, connectors, isPending } = useConnect()
  const { disconnect } = useDisconnect()

  // Parse ABI from source if available
  const parsedMethods = source
    ? JSON.parse(source.abi)
        ?.filter(
          (item: ContractMethod) =>
            item.type === "function" && item.stateMutability !== "view" && item.stateMutability !== "pure",
        )
        //eslint-disable-next-line @typescript-eslint/no-explicit-any
        ?.map((item: any) => ({
          name: item.name,
          signature: item.name.substring(0, 8),
          inputs: item.inputs,
          outputs: item.outputs,
          stateMutability: item.stateMutability,
          type: item.type,
        }))
    : []

  const filteredMethods = parsedMethods.filter((method: ContractMethod) =>
    method.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleConnect = () => {
    const injectedConnector = connectors.find((c) => c.id === "injected")
    if (injectedConnector) {
      connect({ connector: injectedConnector })
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center justify-between">
          <span>Write Contract</span>
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by method name"
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2 mb-6">
          {!isConnected ? (
            <>
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                <span className="mr-1 h-2 w-2 rounded-full bg-red-500"></span>
                Not Connected
              </Badge>
              <span className="text-sm text-muted-foreground mr-2">
                You need to connect to Web3 to interact with the contract
              </span>
              <Button size="sm" onClick={handleConnect} disabled={isPending}>
                {isPending ? "Connecting..." : "Connect Wallet"}
              </Button>
            </>
          ) : (
            <>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <span className="mr-1 h-2 w-2 rounded-full bg-green-500"></span>
                Connected
              </Badge>
              <span className="text-sm text-muted-foreground mr-2">
                {walletAddress?.slice(0, 6)}...{walletAddress?.slice(-4)}
              </span>
              <Button size="sm" variant="outline" onClick={() => disconnect()}>
                Disconnect
              </Button>
            </>
          )}
        </div>

        <div className="space-y-4">
          {filteredMethods.length > 0 ? (
            filteredMethods.map((method: ContractMethod, index: number) => (
              <ContractMethodCard
                key={`${method.name}-${index}`}
                method={method}
                contractAddress={address}
                index={index}
                isWrite={true}
              />
            ))
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              {searchTerm ? "No methods found matching your search" : "No write methods available"}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
