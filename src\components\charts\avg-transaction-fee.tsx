"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ChartTypeEnum, getTimeRanges, use<PERSON>hart } from "@/hooks/useChart";
import { Info } from "lucide-react";
import { useMemo, useState } from "react";
import {
    Line,
    LineChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from "recharts";
import TimeRangeSelector from "./time-range-seletor";

interface TransactionFeeTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// Convert wei to HLS (1 HLS = 10^18 wei)
const weiToHls = (weiValue: string | number): number => {
  if (typeof weiValue === "string") {
    try {
      const parts = weiValue.split(".");
      const wholePart = parts[0];
      const fractionalPart = parts[1] || "";

      const wholeHls = BigInt(wholePart) / BigInt(10 ** 18);

      let fractionalHls = 0;
      if (fractionalPart) {
        const paddedFractional = fractionalPart
          .padEnd(18, "0")
          .substring(0, 18);
        fractionalHls = Number("0." + paddedFractional);
      }

      return Number(wholeHls) + fractionalHls;
    } catch (e) {
      console.error("Error converting wei to HLS:", e);
      return 0;
    }
  } else if (typeof weiValue === "number") {
    return weiValue / 10 ** 18;
  }
  return 0;
};

const TransactionFeeTooltip = ({
  active,
  payload,
  label,
}: TransactionFeeTooltipProps) => {
  if (!active || !payload || !payload.length) return null;

  const data = payload[0].payload;
  let formattedDate = "";
  try {
    if (label) {
      const date = new Date(label);
      formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  } catch (e) {
    console.error("Error formatting date:", e);
    formattedDate = String(label);
  }

  const feeInHls = data.feeInHls;

  const formatFee = (value: number | undefined) => {
    if (value === undefined || value === null || value === 0) return "0";

    if (value < 0.000001 && value > 0) {
      return value.toExponential(6);
    } else if (value > 1000000) {
      return value.toExponential(2);
    }

    return value.toFixed(6);
  };

  return (
    <div className="rounded-lg border bg-background/95 p-3 shadow-md">
      <div className="mb-2 flex items-center gap-2">
        <div className="h-3 w-3 rounded-full bg-primary" />
        <span className="font-medium">{formattedDate}</span>
      </div>
      <div className="space-y-1 text-sm">
        <p>
          <span className="text-muted-foreground">Average Fee: </span>
          <span className="font-medium">{formatFee(feeInHls)} HLS</span>
        </p>
        {data.usdPrice && data.usdPrice !== "0" && (
          <p>
            <span className="text-muted-foreground">USD Price: </span>
            <span className="font-medium">
              ${Number.parseFloat(data.usdPrice).toFixed(2)}
            </span>
          </p>
        )}
      </div>
    </div>
  );
};

export default function TransactionFeeChart() {
  const [timeRange, setTimeRange] = useState<string>("1m");

  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1d":
        return getTimeRanges("1d");
      case "1w":
        return getTimeRanges("7d");
      case "1m":
        return getTimeRanges("30d");
      case "3m":
        return getTimeRanges("90d");
      case "6m":
        return getTimeRanges("180d");
      case "1y":
        return getTimeRanges("1y");
      case "all":
        return getTimeRanges("all");
      default:
        return getTimeRanges("30d");
    }
  }, [timeRange]);

  const { data, isLoading, error } = useChart(
    startTime,
    endTime,
    ChartTypeEnum.AVG_TXN_FEE,
  );

  if (error) {
    return (
      <div className="text-red-500 text-center">Error loading chart data</div>
    );
  }

  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.map((item: any) => {
      const feeInHls = weiToHls(item.avgTxnFee);

      return {
        date: new Date(item.timestamp).toISOString().split("T")[0],
        avgTxnFee: item.avgTxnFee,
        feeInHls: feeInHls,
        usdPrice: item.usdPrice,
      };
    });
  }, [data]);

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Average Transaction Fee</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <TimeRangeSelector timeRange={timeRange} onChange={setTimeRange} />
        </CardHeader>
        <CardContent>
          <div className="h-[500px]">
            {isLoading ? (
              <Skeleton className="h-[450px] w-full" />
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  <XAxis dataKey="date" />
                  <YAxis
                    label={{
                      value: "Average Fee (HLS)",
                      angle: -90,
                      position: "insideLeft",
                    }}
                  />
                  <Tooltip content={<TransactionFeeTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="feeInHls"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
