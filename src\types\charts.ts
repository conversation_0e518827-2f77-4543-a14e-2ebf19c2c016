export interface TopGasUser {
  rank: number;
  value: string;
  address: string;
  percentage: string;
}

export interface TopActiveAddress {
  rank: number;
  count: number;
  address: string;
  percentage: string;
}

export interface TokenData {
  name: string;
  rank: number;
  count: number;
  symbol: string;
  address: string;
  percentage?: string;
}

export enum StatsTopMetric {
  ONE_DAY = "ONE_DAY",
  THREE_DAYS = "THREE_DAYS",
  ONE_WEEK = "ONE_WEEK",
}

export enum StatsTopType {
  TRANSACTION = "TRANSACTION",
  NETWORK = "NETWORK",
  TOKEN = "TOKEN",
  HOT_CONTRACT = "HOT_CONTRACT",
  OVERVIEW = "OVERVIEW",
}

// entries per type
export interface StatsTopTransactionEntries {
  topEthSenders: TopGasUser[];
  topTxnSenders: TopActiveAddress[];
  topEthReceivers: TopGasUser[];
  topTxnReceivers: TopActiveAddress[];
}

export interface StatsTopNetworkEntries {
  topGasUsers: TopGasUser[];
  topActiveAddresses: TopActiveAddress[];
}

export interface StatsTopTokenEntries {
  topTokensByTxCount: TokenData[];
  topTokensByUniqueTotal: TokenData[];
  topTokensByUniqueSenders: TokenData[];
  topTokensByUniqueReceivers: TokenData[];
}

export interface StatsTopOverviewEntries {
  transaction: StatsTopTransactionEntries | null;
  network: StatsTopNetworkEntries | null;
  token: unknown | null;
}

export type StatsTopEntriesMap = {
  [StatsTopType.TRANSACTION]: StatsTopTransactionEntries;
  [StatsTopType.NETWORK]: StatsTopNetworkEntries;
  [StatsTopType.TOKEN]: StatsTopTokenEntries;
  [StatsTopType.HOT_CONTRACT]: StatsTopNetworkEntries;
  [StatsTopType.OVERVIEW]: StatsTopOverviewEntries;
};

export interface StatsTopResult<T extends keyof StatsTopEntriesMap> {
  id: number;
  createdAt: string;
  updatedAt: string;
  metric: string;
  type: T;
  snapshotStart: string;
  snapshotEnd: string;
  entries: StatsTopEntriesMap[T];
}

export interface StatsTopResponse<T extends keyof StatsTopEntriesMap> {
  jsonrpc: string;
  id: number;
  result: StatsTopResult<T>;
}
