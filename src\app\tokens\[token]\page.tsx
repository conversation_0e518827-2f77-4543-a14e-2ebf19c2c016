"use client"

import { ContractTab } from "@/components/contract/contract-tab"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { PATH_ROUTER } from "@/constants/routers"
import { useGetTokenDetail } from "@/hooks/useTokens"
import {
  useGetAbi,
  useGetAccountInfo,
  useGetCode,
} from "@/hooks/useVerifyContract"
import { AlertCircle, ArrowLeft, Check } from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { TokenHolders } from "./components/token-hoders"
import { TokenMarket } from "./components/token-market"
import { TokenOverview } from "./components/token-overview"
import { TokenTransactions } from "./components/token-transactions"

const TokenPage = () => {
  const params = useParams()
  const token = params.token as string
  const [isContractVerified, setIsContractVerified] = useState<boolean>(false)
  const { data: code, isLoading: isLoadingCode } = useGetCode(token)
  const { data: abi } = useGetAbi(token)
  const {
    data: tokenDetail,
    isLoading: isLoadingToken,
    isError,
    error,
  } = useGetTokenDetail(token)

  const { data: accountInfo } = useGetAccountInfo(token)

  const isContract = code !== "0x"
  const isLoading = isLoadingCode || isLoadingToken

  useEffect(() => {
    if (abi && abi !== "0x" && code !== "0x") {
      try {
        JSON.parse(abi)
        setIsContractVerified(true)
      } catch (error) {
        console.error("Invalid ABI JSON:", error)
        setIsContractVerified(false)
      }
    } else {
      setIsContractVerified(false)
    }
  }, [abi, code])

  if (isLoading) {
    return <TokenPageSkeleton />
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error instanceof Error
              ? error.message
              : "Failed to load token details"}
          </AlertDescription>
        </Alert>
        <Button asChild variant="outline" size="sm" className="mb-6">
          <Link href={PATH_ROUTER.TOKENS}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tokens
          </Link>
        </Button>
      </div>
    )
  }

  if (!tokenDetail) {
    return (
      <div className="container mx-auto p-4">
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Token Not Found</AlertTitle>
          <AlertDescription>
            The token with address {token} could not be found.
          </AlertDescription>
        </Alert>
        <Button asChild variant="outline" size="sm" className="mb-6">
          <Link href={PATH_ROUTER.TOKENS}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tokens
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex md:flex-row flex-col md:items-center items-start justify-between mb-4 gap-2">
        <h1 className="text-2xl font-bold">
          {tokenDetail.name} ({tokenDetail.symbol})
        </h1>
        <Button asChild variant="outline" size="sm">
          <Link href={PATH_ROUTER.TOKENS}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tokens
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <TokenOverview tokenDetail={tokenDetail} />
        <TokenMarket tokenDetail={tokenDetail} />
      </div>

      <Tabs defaultValue="transfers" className="w-full">
        <TabsList>
          <TabsTrigger value="transfers">Transfers</TabsTrigger>
          <TabsTrigger value="holders">Holders</TabsTrigger>
          {isContract && (
            <TabsTrigger value="contract" className="relative">
              Contract
              {isContractVerified && (
                <div className="absolute -top-1.5 -right-1">
                  <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-2.5 w-2.5 text-white" />
                  </div>
                </div>
              )}
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="transfers" className="mt-4">
          <TokenTransactions tokenAddress={token} tokenDetail={tokenDetail} />
        </TabsContent>

        <TabsContent value="holders" className="mt-4">
          <TokenHolders tokenAddress={token} tokenDetail={tokenDetail} />
        </TabsContent>

        {isContract && (
          <TabsContent value="contract" className="mt-4">
            <ContractTab
              address={token}
              isProxy={accountInfo?.isProxy}
              isVerified={isContractVerified}
            />
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}

export default TokenPage

function TokenPageSkeleton() {
  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex items-center justify-between mb-4">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-32" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-5 w-24 mb-4" />
            <div className="space-y-4">
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-6 w-48" />
              </div>
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-6 w-48" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-5 w-24 mb-4" />
            <div className="space-y-4">
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-6 w-48" />
              </div>
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-6 w-48" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div>
        <Skeleton className="h-10 w-64 mb-4" />
        <Skeleton className="h-64 w-full rounded-md" />
      </div>
    </div>
  )
}
