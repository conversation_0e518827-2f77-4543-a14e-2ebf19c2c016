import apiClient from "@/lib/api/apiClient";
import { EpochDetailsResponse, ListEpochResponse } from "@/types/epoch";
import { useQuery } from "@tanstack/react-query";

export const useEpochDetails = (epochNumber: number) => {
  return useQuery({
    queryKey: ["epochDetail", epochNumber],
    queryFn: async () => {
      const response = await apiClient.post<EpochDetailsResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getEpochByNumber",
          params: [epochNumber],
          id: 1,
        },
      );

      return response?.result;
    },
    staleTime: 5 * 60 * 1000,
  });
};

export const useListEpoch = (page: number, limit: number) => {
  return useQuery({
    queryKey: ["tokens", page, limit],
    queryFn: async () => {
      const response = await apiClient.post<
        ListEpochResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_listEpochs",
        params: [page, limit],
        id: 1,
      });
      console.log("response", response);
      return response?.result;
    },
  });
};
