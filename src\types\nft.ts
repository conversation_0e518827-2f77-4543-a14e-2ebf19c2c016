import type { TokenDetails } from "./transactions"

export enum StatsTopMintCollectionMetric {
  ONE_MINUTE = "one_minute",
  THREE_MINUTES = "three_minutes",
  FIVE_MINUTES = "five_minutes",
  FIFTEEN_MINUTES = "fifteen_minutes",
  TWENTY_MINUTES = "twenty_minutes",
  ONE_HOUR = "one_hour",
}

export enum StatsTopCollectionMetric {
  ONE_HOUR = "one_hour",
  SIX_HOURS = "six_hours",
  TWELVE_HOURS = "twelve_hours",
  ONE_DAY = "one_day",
  SEVEN_DAYS = "seven_days",
  THIRTY_DAYS = "thirty_days",
}

export interface ListNftResponse {
  jsonrpc: string
  id: number
  result: Result
}

export interface Result {
  data: Daum[]
  metadata: Metadata
}

export interface Daum {
  id: number
  createdAt: string
  updatedAt: string
  metric: string
  tokenAddress: string
  address: string
  name: string
  symbol?: string
  type: string
  nftType?: string
  transfersCount: number
  ownersCount: number
  totalAssets: number
  totalSupply?: string
  tokenDetails?: TokenDetails
  logo?: string
  volume24h?: number
  volumeChange24h?: number
  floorPrice?: number
  floorPriceChange24h?: number
  sales24h?: number
  salesChange24h?: number
}

export interface Metadata {
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface TokenDetailResponse {
  jsonrpc: string
  id: number
  result: {
    address: string
    name: string
    symbol: string
    decimals: number
    totalSupply: string
    logo?: string
    type: string
    holders?: number
    transactions?: number
    price?: {
      usd?: number
      eth?: number
      change24h?: number
    }
  }
}

export interface NftTokenDetailResponse {
  jsonrpc: string
  id: number
  result: NftDetails
}

export interface NftsOfAddressResponse {
  jsonrpc: string
  id: number
  result: {
    data: NftDetails
  }
}

export interface NftDetails {
  id: number
  createdAt: string
  updatedAt: string
  address: string
  creatorAddress: string
  tokenId: string
  standard: string
  tokenUri: string
  quantity: number
  name: string
  description: string
  image: string
  externalUrl: any
  attributes: Attribute[]
  needsTokenUriFetch: boolean
  needsMetadataFetch: boolean
  lastUriFetchAttemptAt: any
  lastMetadataFetchAttemptAt: any
  uriFetchAttempts: number
  metadataFetchAttempts: number
  ownerAddresses: string[]
}

export interface Attribute {
  value: string
  trait_type: string
}

// Metric display labels
export const METRIC_LABELS: Record<StatsTopCollectionMetric, string> = {
  [StatsTopCollectionMetric.ONE_HOUR]: "1 Hour",
  [StatsTopCollectionMetric.SIX_HOURS]: "6 Hours",
  [StatsTopCollectionMetric.TWELVE_HOURS]: "12 Hours",
  [StatsTopCollectionMetric.ONE_DAY]: "1 Day",
  [StatsTopCollectionMetric.SEVEN_DAYS]: "7 Days",
  [StatsTopCollectionMetric.THIRTY_DAYS]: "30 Days",
}

export const MINT_METRIC_LABELS: Record<StatsTopMintCollectionMetric, string> =
  {
    [StatsTopMintCollectionMetric.ONE_MINUTE]: "1 Minute",
    [StatsTopMintCollectionMetric.THREE_MINUTES]: "3 Minutes",
    [StatsTopMintCollectionMetric.FIVE_MINUTES]: "5 Minutes",
    [StatsTopMintCollectionMetric.FIFTEEN_MINUTES]: "15 Minutes",
    [StatsTopMintCollectionMetric.TWENTY_MINUTES]: "20 Minutes",
    [StatsTopMintCollectionMetric.ONE_HOUR]: "1 Hour",
  }
