"use client";

import { TokenHoldingsControls } from "@/components/token-holdings/TokenHoldingsControls";
import { TokenHoldingsOverview } from "@/components/token-holdings/TokenHoldingsOverview";
import { TokenHoldingsTable } from "@/components/token-holdings/TokenHoldingsTable";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { PATH_ROUTER } from "@/constants/routers";
import { useTokenBalance } from "@/hooks/useTokens";
import { AlertCircle } from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import CopyTooltip from "../components/copy-tooltip";

export default function TokenHoldingsContent() {
  const searchParams = useSearchParams();
  const address = searchParams.get("a");
  const [hideZeroBalance, setHideZeroBalance] = useState(false);
  const [showEthValue, setShowEthValue] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const limit = 100;

  const {
    data: tokenBalances,
    isLoading: isTokenBalancesLoading,
    isError: isTokenBalancesError,
    error: tokenBalancesError,
  } = useTokenBalance(address ?? "", page, limit);

  const overview = useMemo(() => {
    let totalUsdValue = 0;
    let totalEthValue = 0;

    if (tokenBalances && tokenBalances?.TotalCount > 0) {
      tokenBalances?.Balances?.forEach((token) => {
        const usdValue = Number.parseFloat(token.balance) || 0;
        totalUsdValue += usdValue;
      });
    }

    return {
      netWorthUsd: totalUsdValue,
      netWorthEth: totalEthValue,
      totalBalanceChange: -0.12,
      assetsInWallet: {
        count: tokenBalances?.TotalCount || 0,
        value: totalUsdValue,
      },
      nftAssets: {
        count: 0,
        value: 0,
      },
      liquidityPoolAssets: {
        count: 0,
        value: 0,
      },
    };
  }, [tokenBalances]);

  // Filter tokens based on user preferences
  const filteredTokens = useMemo(() => {
    if (!tokenBalances) return [];

    let filtered = [...tokenBalances.Balances];

    // Filter zero balances if requested
    if (hideZeroBalance) {
      filtered = filtered.filter((token) => {
        const balance = Number.parseFloat(token.balance);
        return balance > 0;
      });
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (token) =>
          token.symbol.toLowerCase().includes(query) ||
          token.address.toLowerCase().includes(query) ||
          token.description.toLowerCase().includes(query),
      );
    }

    return filtered;
  }, [tokenBalances, hideZeroBalance, searchQuery]);

  const errorMessage =
    tokenBalancesError instanceof Error
      ? tokenBalancesError.message
      : "An error occurred while fetching token data";

  if (isTokenBalancesError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Token Holdings
            </h3>
            <p className="text-sm text-muted-foreground mt-2">{errorMessage}</p>
            <Button asChild className="mt-4">
              <Link href={`${PATH_ROUTER.TOKEN_HOLDINGS}?a=${address}`}>
                Try Again
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-8">
        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
          <span className="text-lg">🪙</span>
        </div>
        <div className="flex-1">
          <h1 className="text-2xl font-bold">Token Holdings</h1>
          <div className="flex items-center gap-2">
            <code className="text-sm">{address}</code>
            <CopyTooltip content={address ?? ""} />
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {isTokenBalancesLoading ? (
          <TokenHoldingsOverviewSkeleton />
        ) : (
          <TokenHoldingsOverview overview={overview} />
        )}

        <div className="space-y-4">
          <TokenHoldingsControls
            hideZeroBalance={hideZeroBalance}
            setHideZeroBalance={setHideZeroBalance}
            showEthValue={showEthValue}
            setShowEthValue={setShowEthValue}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />

          {filteredTokens.length === 0 && !isTokenBalancesLoading ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No tokens found.{" "}
                {searchQuery ? "Try adjusting your search criteria." : ""}
              </AlertDescription>
            </Alert>
          ) : (
            <div className="border rounded-lg">
              {isTokenBalancesLoading ? (
                <TokenHoldingsTableSkeleton />
              ) : (
                <TokenHoldingsTable
                  holdings={filteredTokens}
                  showEthValue={showEthValue}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Skeleton for TokenHoldingsOverview
function TokenHoldingsOverviewSkeleton() {
  return (
    <div className="grid gap-6">
      <Card>
        <CardContent className="pt-6">
          <h2 className="text-lg font-semibold mb-4">Overview</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div>
              <div className="text-sm text-muted-foreground">
                NET WORTH IN USD
              </div>
              <Skeleton className="h-8 w-40 mt-1" />
            </div>
            <div>
              <div className="text-sm text-muted-foreground">
                NET WORTH IN ETH
              </div>
              <Skeleton className="h-8 w-40 mt-1" />
            </div>
            <div>
              <div className="text-sm text-muted-foreground">
                TOTAL BALANCE CHANGE (24H)
              </div>
              <Skeleton className="h-8 w-40 mt-1" />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">
              Assets in Wallet
            </div>
            <Skeleton className="h-8 w-32 mt-1" />
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">NFT Assets</div>
            <Skeleton className="h-8 w-32 mt-1" />
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">
              Liquidity Pool Assets in Wallet
            </div>
            <Skeleton className="h-8 w-32 mt-1" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Skeleton for TokenHoldingsTable
function TokenHoldingsTableSkeleton() {
  return (
    <div className="w-full overflow-auto">
      <table className="w-full caption-bottom text-sm">
        <thead className="[&_tr]:border-b">
          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Asset
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Symbol
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Contract Address
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              Quantity
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              Price
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              Value
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground"></th>
          </tr>
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {Array.from({ length: 10 }).map((_, index) => (
            <tr
              key={index}
              className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
            >
              <td className="p-4 align-middle">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-6 w-6 rounded-full" />
                  <Skeleton className="h-5 w-24" />
                </div>
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-16" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-40" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-5 w-20 ml-auto" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-5 w-20 ml-auto" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-5 w-24 ml-auto" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-8 w-16 ml-auto" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
