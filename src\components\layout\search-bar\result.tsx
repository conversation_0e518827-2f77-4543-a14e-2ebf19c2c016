import s from "./search-bar.module.scss"

interface ResultProps {
  title: string
  children: React.ReactNode
}

export const Result = ({ title, children }: ResultProps) => {
  return (
    <div className={s.result}>
      <h3 className={s.title}>{title}</h3>
      <ul className={s.list}>{children}</ul>
    </div>
  )
}

interface ResultItemProps {
  children: React.ReactNode
  onClick?: () => void
}

export const ResultItem = ({ children, onClick }: ResultItemProps) => {
  return (
    <li className={s.item} onClick={onClick}>
      {children}
    </li>
  )
}
