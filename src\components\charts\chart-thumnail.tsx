import {
  Area,
  Area<PERSON>hart,
  Cell,
  Line,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";

interface ChartThumbnailProps {
  chartId: string;
  chartType: string;
  color: string;
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

const pieData = [
  { name: "Group A", value: 400 },
  { name: "Group B", value: 300 },
  { name: "Group C", value: 300 },
  { name: "Group D", value: 200 },
];

// Update the generateAreaData function to include a specific pattern for node sync charts
const generateAreaData = (
  length = 30,
  min = 100,
  max = 1000,
  chartType = "default",
) => {
  // Create different patterns based on chart type
  if (chartType === "eth-price") {
    return Array.from({ length }, (_, i) => {
      const volatility = Math.sin(i / 3) * 200 + Math.random() * 100;
      return {
        date: new Date(Date.now() - (length - i) * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0],
        value: 1800 + volatility,
      };
    });
  }

  // Node sync charts with exponential growth pattern
  if (chartType === "node-sync-default" || chartType === "node-sync-archive") {
    return Array.from({ length }, (_, i) => {
      // Create an exponential growth curve with minimal noise
      const progress = Math.pow(i / length, 1.5) * max;
      // Add very small random noise to make it look more natural
      const noise = Math.random() * (max * 0.02);
      return {
        date: new Date(Date.now() - (length - i) * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0],
        value: progress + noise,
      };
    });
  }

  // Default random pattern
  return Array.from({ length }, (_, i) => ({
    date: new Date(Date.now() - (length - i) * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    value: min + Math.random() * (max - min),
  }));
};

export default function ChartThumbnail({
  chartId,
  chartType,
  color,
}: ChartThumbnailProps) {
  const data = generateAreaData(30, 100, 1000, chartId);

  // Special handling for node sync charts
  if (chartId === "node-sync-default" || chartId === "node-sync-archive") {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <Line
            type="monotone"
            dataKey="value"
            stroke={color}
            strokeWidth={1.2}
            dot={false}
            activeDot={{ r: 3 }}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  }

  if (chartType === "pie") {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={pieData}
            cx="50%"
            cy="50%"
            innerRadius={0}
            outerRadius={60}
            paddingAngle={0}
            dataKey="value"
          >
            {pieData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
    );
  }

  if (chartType === "line") {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <Line
            type="monotone"
            dataKey="value"
            stroke={color}
            strokeWidth={1.5}
            dot={false}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  }

  // Default to area chart
  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data}>
        <defs>
          <linearGradient id={`color${chartId}`} x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={color} stopOpacity={0.8} />
            <stop offset="95%" stopColor={color} stopOpacity={0} />
          </linearGradient>
        </defs>
        <Area
          type="monotone"
          dataKey="value"
          stroke={color}
          fillOpacity={1}
          fill={`url(#color${chartId})`}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}
