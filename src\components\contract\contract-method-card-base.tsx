"use client";

import type React from "react";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Copy, ExternalLink } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ZERO_ADDRESS } from "@/constants";
import {
  type ContractInput,
  type ContractMethod,
  getSchemaForType,
} from "@/types/contract";

interface ContractMethodCardBaseProps {
  method: ContractMethod;
  contractAddress: string;
  index: number;
  isWrite?: boolean;
  renderActions: (args: unknown[], shouldFetch: boolean) => React.ReactNode;
  renderResults: () => React.ReactNode;
}

export function ContractMethodCardBase({
  method,
  contractAddress,
  index,
  isWrite = false,
  renderActions,
  renderResults,
}: ContractMethodCardBaseProps) {
  const [expanded, setExpanded] = useState(false);
  const [args, setArgs] = useState<unknown[]>([]);
  const [customError, setCustomError] = useState<string | null>(null);
  const [shouldFetch, setShouldFetch] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  const hasInputs = method.inputs && method.inputs.length > 0;

  // Create dynamic schema based on method inputs
  const dynamicSchema = z.object(
    Object.fromEntries(
      (method.inputs || []).map((input: ContractInput) => [
        input.name || `param${input.type}`,
        getSchemaForType(input.type),
      ]),
    ),
  );

  type FormValues = z.infer<typeof dynamicSchema>;

  const form = useForm<FormValues>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: {},
    mode: "onSubmit", // Only validate on submit, not on change
  });

  // For methods without inputs, trigger a fetch when the card is expanded
  useEffect(() => {
    if (expanded && !hasInputs && !isWrite) {
      setShouldFetch(true);
    }
  }, [expanded, hasInputs, isWrite]);

  const onSubmit = async (data: FormValues): Promise<void> => {
    setFormSubmitted(true);
    try {
      // Prepare arguments from form data
      const formattedArgs = (method.inputs || []).map(
        (input: ContractInput) => {
          const value = data[input.name || `param${input.type}`];

          if (input.type === "bool") return value === "true";
          if (input.type.startsWith("uint") || input.type.startsWith("int")) {
            return value ? BigInt(value as string) : BigInt(0);
          }
          if (input.type === "address" && !value) {
            return ZERO_ADDRESS; // Zero address as default
          }
          if (input.type.startsWith("bytes") && !value) {
            return "0x"; // Empty bytes as default
          }

          // For any other type, provide a default empty string if no value
          return value || "";
        },
      );

      setArgs(formattedArgs);
      setCustomError(null);
      setShouldFetch(true); // Enable fetching after args are set
    } catch (error) {
      setCustomError(
        error instanceof Error ? error.message : "An error occurred",
      );
    }
  };

  const copyToClipboard = (text: string): void => {
    navigator.clipboard.writeText(text);
  };

  const methodSignature = `${method.name}(${(method.inputs || [])
    .map((i) => `${i.type} ${i.name}`)
    .join(", ")})`;

  // Determine if we should show the form
  const showForm = hasInputs || isWrite;

  return (
    <Card className="mb-4">
      <CardHeader
        className="pb-2 cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <CardTitle className="text-base flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span>
              {index + 1}. {method.name}
            </span>
            <span className="text-xs text-muted-foreground">
              ({method.signature || method.name.substring(0, 8)})
            </span>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                copyToClipboard(methodSignature);
              }}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
              <a
                href={`https://explorer.helioschainlabs.org/address/${contractAddress}#code`}
                target="_blank"
                rel="noopener noreferrer"
                onClick={(e) => e.stopPropagation()}
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent>
          {showForm ? (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                {hasInputs && (
                  <div className="space-y-3">
                    {method?.inputs?.map((input, i) => (
                      <FormField
                        key={i}
                        control={form.control}
                        name={input.name || `param${input.type}`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <span>{input.name || `Parameter ${i + 1}`}</span>
                              <span className="text-xs text-muted-foreground">
                                ({input.type})
                              </span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder={`Enter ${input.type}`}
                              />
                            </FormControl>
                            {/* Only show error messages after form submission */}
                            {formSubmitted && <FormMessage />}
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                )}

                {renderActions(args, shouldFetch)}
              </form>
            </Form>
          ) : null}

          {customError && formSubmitted && (
            <div className="text-sm text-red-500 mt-2">{customError}</div>
          )}

          {renderResults()}
        </CardContent>
      )}
    </Card>
  );
}
