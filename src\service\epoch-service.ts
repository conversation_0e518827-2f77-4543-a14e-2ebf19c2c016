import { envs } from "@/constants/envs";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

interface EpochInfoResponse {
  current_epoch: string;
  epoch_length: string;
  last_epoch_height: string;
  validators_per_epoch: string;
  epoch_enabled: boolean;
  current_height: string;
  blocks_until_next_epoch: string;
}

interface HistoricalInfoResponse {
  hist: {
    header: Header;
    valset: Valset[];
  };
}

export interface Header {
  version: Version;
  chain_id: string;
  height: string;
  time: string;
  last_block_id: LastBlockId;
  last_commit_hash: any;
  data_hash: any;
  validators_hash: any;
  next_validators_hash: string;
  consensus_hash: any;
  app_hash: string;
  last_results_hash: any;
  evidence_hash: any;
  proposer_address: string;
}

export interface Version {
  block: string;
  app: string;
}

export interface LastBlockId {
  hash: any;
  part_set_header: PartSetHeader;
}

export interface PartSetHeader {
  total: number;
  hash: any;
}

export interface Valset {
  operator_address: string;
  consensus_pubkey: ConsensusPubkey;
  jailed: boolean;
  status: string;
  tokens: string;
  delegator_shares: string;
  description: Description;
  unbonding_height: string;
  unbonding_time: string;
  commission: Commission;
  min_self_delegation: string;
  unbonding_on_hold_ref_count: string;
  unbonding_ids: any[];
  min_delegation: string;
  delegate_authorization: boolean;
}

export interface ConsensusPubkey {
  "@type": string;
  key: string;
}

export interface Description {
  moniker: string;
  identity: string;
  website: string;
  security_contact: string;
  details: string;
}

export interface Commission {
  commission_rates: CommissionRates;
  update_time: string;
}

export interface CommissionRates {
  rate: string;
  max_rate: string;
  max_change_rate: string;
}

export async function fetchEpochInfo(): Promise<EpochInfoResponse> {
  const response = await axios.get<EpochInfoResponse>(
    `${envs.COSMOS_API_URL}/cosmos/staking/v1beta1/epoch_info`,
  );
  return response.data;
}

export async function fetchHistoricalInfo(
  height: string,
): Promise<HistoricalInfoResponse> {
  const response = await axios.get<HistoricalInfoResponse>(
    `${envs.COSMOS_API_URL}/cosmos/staking/v1beta1/historical_info/${height}`,
  );
  return response.data;
}

export function useEpochInfo() {
  return useQuery({
    queryKey: ["epochInfo"],
    queryFn: fetchEpochInfo,
  });
}

export function useHistoricalInfo(height: string) {
  return useQuery({
    queryKey: ["historicalInfo", height],
    queryFn: () => fetchHistoricalInfo(height),
    staleTime: 5 * 60 * 1000,
    enabled: !!height,
  });
}

export function calculateEpochFromHeight(height: number): number {
  return Math.floor(height / 100);
}

// Helper function to calculate start height from epoch number
export function calculateHeightFromEpoch(epoch: number): number {
  return epoch * 100;
}
