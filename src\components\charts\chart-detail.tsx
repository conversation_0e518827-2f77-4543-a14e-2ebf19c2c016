"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip } from "@/components/ui/chart"
import { Skeleton } from "@/components/ui/skeleton"
import { type ChartType, getTimeRanges, useChart } from "@/hooks/useChart"
import { Download, Info } from "lucide-react"
import { useMemo, useState } from "react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts"
import type { ChartVisualType } from "./chart-config"
import TimeRangeSelector, { type TimeRangeOption } from "./time-range-seletor"
import UnifiedChartTooltip from "./unified-chart-tooltip"

interface ChartDetailProps {
  title: string
  chartType: ChartType
  visualType: ChartVisualType
  color: string
  description?: string
  yAxisLabel?: string
  yAxisFormatter?: (value: number) => string
}

const timeRangeOptions: TimeRangeOption[] = [
  { value: "1m", label: "1m" },
  { value: "3m", label: "3m" },
  { value: "6m", label: "6m" },
  { value: "1y", label: "1y" },
  { value: "all", label: "All" },
]

export default function ChartDetail({
  title,
  chartType,
  visualType,
  color,
  description,
  yAxisLabel = "Value",
  yAxisFormatter = (value: number) => value.toLocaleString(),
}: ChartDetailProps) {
  const [timeRange, setTimeRange] = useState<string>("1m")

  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d")
      case "3m":
        return getTimeRanges("90d")
      case "6m":
        return getTimeRanges("180d")
      case "1y":
        return getTimeRanges("1y")
      case "all":
        return getTimeRanges("all")
      default:
        return getTimeRanges("30d")
    }
  }, [timeRange])

  const { data, isLoading, error } = useChart(startTime, endTime, chartType)

  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    return data.map((item: any) => ({
      date: new Date(item.timestamp).toISOString().split("T")[0],
      value: item.value || 0,
      ...item, // Include all original properties for tooltip
    }))
  }, [data])

  // Function to render the appropriate chart based on visualType
  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 10, right: 30, left: 20, bottom: 0 },
    }

    const commonAxisProps = {
      xAxis: (
        <XAxis
          dataKey="date"
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => {
            const date = new Date(value)
            return `${date.getMonth() + 1}/${date.getDate()}`
          }}
        />
      ),
      yAxis: (
        <YAxis
          tickLine={false}
          axisLine={false}
          tickFormatter={yAxisFormatter}
          label={{
            value: yAxisLabel,
            angle: -90,
            position: "insideLeft",
            style: { textAnchor: "middle" },
          }}
        />
      ),
      cartesianGrid: <CartesianGrid strokeDasharray="3 3" vertical={false} />,
    }

    const tooltipContent = (
      <ChartTooltip
        content={<UnifiedChartTooltip chartType={chartType} />}
        cursor={false}
      />
    )

    switch (visualType) {
      case "area":
        return (
          <AreaChart {...commonProps}>
            {commonAxisProps.cartesianGrid}
            {commonAxisProps.xAxis}
            {commonAxisProps.yAxis}
            {tooltipContent}
            <Area
              type="monotone"
              dataKey="value"
              stroke={color}
              fill={color}
              fillOpacity={0.2}
              strokeWidth={2}
              activeDot={{ r: 6, fill: color }}
            />
          </AreaChart>
        )
      case "bar":
        return (
          <BarChart {...commonProps}>
            {commonAxisProps.cartesianGrid}
            {commonAxisProps.xAxis}
            {commonAxisProps.yAxis}
            {tooltipContent}
            <Bar
              dataKey="value"
              fill={color}
              radius={[4, 4, 0, 0]}
              barSize={20}
            />
          </BarChart>
        )
      case "line":
      default:
        return (
          <LineChart {...commonProps}>
            {commonAxisProps.cartesianGrid}
            {commonAxisProps.xAxis}
            {commonAxisProps.yAxis}
            {tooltipContent}
            <Line
              type="monotone"
              dataKey="value"
              stroke={color}
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 6, fill: color }}
            />
          </LineChart>
        )
    }
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>{title}</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <TimeRangeSelector
            timeRange={timeRange}
            onChange={setTimeRange}
            options={timeRangeOptions}
          />
        </CardHeader>
        <CardContent>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Skeleton className="h-[450px] w-full" />
              </div>
            ) : (
              <ChartContainer
                config={{
                  [chartType]: {
                    label: title,
                    color: color,
                  },
                }}
                className="h-full w-full"
              >
                <ResponsiveContainer width="100%" height="100%">
                  {renderChart()}
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" className="flex items-center">
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {description && (
        <Card>
          <CardHeader>
            <CardTitle>About</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{description}</p>
            <p className="mt-2 text-sm text-muted-foreground">
              Click and drag in the plot area to zoom in. You can also use the
              time range buttons to view different time periods.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
