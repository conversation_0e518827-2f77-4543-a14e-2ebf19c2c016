.footer {
  --footer-spacing: var(--spacing);
  --footer-padding: calc(16px + 8 * (100vw - 320px) / 1080);
  position: relative;
  padding-top: var(--footer-padding);

  &::before {
    content: "";
    position: absolute;
    inset: var(--footer-spacing);
    background: linear-gradient(
      to bottom,
      hsl(var(--secondary)),
      hsl(var(--secondary) / 0)
    );
    z-index: -1;
    border-radius: var(--radius);

    @media screen and (max-width: 830px) {
      left: 0;
      right: 0;
    }
  }
}

.bottom {
  padding-top: var(--footer-padding);
  margin-top: var(--footer-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  font-size: 0.85rem;
  color: hsl(var(--muted-foreground));
  border-top: 1px solid hsl(var(--border));

  img {
    width: 150px;
  }
}
