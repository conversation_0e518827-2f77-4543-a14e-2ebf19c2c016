"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useGasData } from "@/hooks/useGasData"
import { useAuth } from "@/lib/auth"
import clsx from "clsx"
import { Menu } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { Nav } from "../nav"
import SearchBar from "../search-bar"
import Bar from "./bar"
import s from "./header.module.scss"
import { Overview } from "./overview"

export const Header = () => {
  const { user, login, logout } = useAuth()
  const { data: gasDataRaw, isLoading } = useGasData()
  const pathname = usePathname()
  const [isNavOpen, setIsNavOpen] = useState(false)

  const gasData = {
    gasPriceInGwei: gasDataRaw?.gasPriceInGwei || "0",
    maxFeePerGas: gasDataRaw?.maxFeePerGas
      ? (Number(gasDataRaw.maxFeePerGas) / 1e9).toFixed(2)
      : "0",
    maxPriorityFeePerGas: gasDataRaw?.maxPriorityFeePerGas
      ? (Number(gasDataRaw.maxPriorityFeePerGas) / 1e9).toFixed(2)
      : "0",
    baseFee: gasDataRaw?.baseFeeInGwei || "0",
  }

  return (
    <header className={clsx(s.header, pathname === "/" && s.home)}>
      <Bar gasData={gasData} />
      <div className={s.content}>
        <div className={s.top}>
          <Link href="/" className={s.logo}>
            <Image
              src="/assets/images/logo_testnet.svg"
              alt="Logo"
              width={204}
              height={32}
            />
          </Link>
          <Nav isOpen={isNavOpen} setIsOpen={setIsNavOpen} />
          {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
          {/* @ts-ignore */}
          <appkit-button />
          <Button
            className={s.navButton}
            onClick={() => setIsNavOpen(!isNavOpen)}
            variant="ghost"
            size="icon"
          >
            <Menu className={s.icon} />
          </Button>
        </div>
        <div className={s.middle}>
          <div className={s.heading}>
            <div className={s.small}>
              <Image
                src="/assets/images/logo-helios.svg"
                alt="Logo"
                width={22}
                height={23}
              />{" "}
              Helios Explorer
            </div>
            <h1 className="flex items-center md:flex-row flex-col">
              Welcome to
              <Image
                src="/assets/images/logo_testnet.svg"
                alt="Logo"
                width={204}
                height={35}
              />
            </h1>
          </div>
          <SearchBar />
          <Overview />
        </div>
      </div>
    </header>
  )
}
