"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { ContractMethod, Source } from "@/types/contract";
import { Info, Search } from "lucide-react";
import { useState } from "react";
import { ContractMethodCard } from "./contract-method-card";

interface ContractReadProxyProps {
  address: string;
  implementationAddress?: string;
  proxyPattern?: string;
  previousImplementation?: string;
  methods?: ContractMethod[];
  source?: Source;
}

export function ContractReadProxy({
  address,
  implementationAddress = "******************************************",
  proxyPattern = "OpenZeppelin's Unstructured Storage proxy pattern",
  previousImplementation = "******************************************",
  methods = [],
  source,
}: ContractReadProxyProps) {
  const [searchTerm, setSearchTerm] = useState("");

  // Parse ABI from source if available
  const parsedMethods = source
    ? JSON.parse(source.abi)
        ?.filter(
          (item: ContractMethod) =>
            item.type === "function" &&
            (item.stateMutability === "view" ||
              item.stateMutability === "pure"),
        )
        //eslint-disable-next-line @typescript-eslint/no-explicit-any
        ?.map((item: any) => ({
          ...item,
          // Add proxy metadata to the method object
          _proxy: {
            isProxy: true,
            implementationAddress: implementationAddress,
          },
        }))
    : methods.map((method) => ({
        ...method,
        _proxy: {
          isProxy: true,
          implementationAddress: implementationAddress,
        },
      }));

  const filteredMethods = parsedMethods.filter((method: ContractMethod) =>
    method.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const handleReset = () => {
    setSearchTerm("");
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-2">
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            <Info className="mr-1 h-3 w-3" />
            ABI for the implementation contract at{" "}
            <a
              href={`/address/${implementationAddress}`}
              className="text-primary underline"
            >
              {implementationAddress}
            </a>
            , using {proxyPattern}.
          </Badge>
        </div>

        <div className="text-sm">
          Previously recorded to be on{" "}
          <a
            href={`/address/${previousImplementation}`}
            className="text-primary underline"
          >
            {previousImplementation}
          </a>
          .
          <div className="mt-1">
            Learn more about proxy contracts in our{" "}
            <a href="#" className="text-primary underline">
              Knowledge Base
            </a>
          </div>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription className="flex items-center text-sm">
            Descriptions included below are taken from the contract source code.
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger className="ml-1 text-primary underline">
                  NatSpec
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs text-xs">
                    Helios does not provide any guarantees on their safety or
                    accuracy.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </AlertDescription>
        </Alert>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Read Contract Information</h3>
          <div className="flex gap-2">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by method name"
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" size="sm" onClick={handleReset}>
              Reset
            </Button>
          </div>
        </div>

        {filteredMethods.length > 0 ? (
          filteredMethods.map((method: ContractMethod, index: number) => (
            <ContractMethodCard
              key={`${method.name}-${index}`}
              method={method}
              contractAddress={address}
              index={index}
            />
          ))
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm
              ? "No methods found matching your search"
              : "No read methods available"}
          </div>
        )}
      </div>
    </div>
  );
}
