import type { Transaction } from "@/types/transactions";
import { Interface } from "ethers";

enum TransactionType {
  TRANSFER_METHOD_ID = "0xa9059cbb",
  APPROVE = "0x095ea7b3",
  TRANSFER_FROM = "0x23b872dd",
  SWAP = "0xd78ad95f",
  DEPOSIT = "0xd0e30db0",
  WITHDRAW = "0x2e1a7d4d",
  BRIDGE = "0x4f467038",
  STAKE = "0xa694fc3a",
  CLAIM = "0x6ef25c3a",
  MINT = "0x40c10f19",
  BURN = "0x42966c68",
  REGISTER_NODE = "0xf7cd5516",
}

type MethodInfo = {
  name: string;
  type?: string;
  action?: string;
  functionName?: string;
  params?: any;
};

const methodMap: Record<string, MethodInfo> = {
  [TransactionType.TRANSFER_METHOD_ID]: { name: "Transfer", type: "token" },
  [TransactionType.APPROVE]: { name: "Approve", type: "token", action: "approved" },
  [TransactionType.TRANSFER_FROM]: { name: "TransferFrom", type: "token" },
  [TransactionType.SWAP]: { name: "Swap", type: "dex" },
  [TransactionType.DEPOSIT]: { name: "Deposit", type: "deposit" },
  [TransactionType.WITHDRAW]: { name: "Withdraw", type: "withdraw" },
  [TransactionType.BRIDGE]: { name: "Bridge", type: "bridge" },
  [TransactionType.STAKE]: { name: "Stake", type: "stake" },
  [TransactionType.CLAIM]: { name: "Claim", type: "claim" },
  [TransactionType.MINT]: { name: "Mint", type: "mint" },
  [TransactionType.BURN]: { name: "Burn", type: "burn" },
  [TransactionType.REGISTER_NODE]: { name: "Register Node", type: "register" },
  "0x42842e0e": { name: "SafeTransferFrom", type: "nft" },
  "0xb88d4fde": { name: "SafeTransferFrom", type: "nft" },
  "0x7ff36ab5": { name: "SwapExactETHForTokens", type: "dex" },
  "0x38ed1739": { name: "SwapExactTokensForTokens", type: "dex" },
  "0x18cbafe5": { name: "SwapExactTokensForETH", type: "dex" },
  "0x4a25d94a": { name: "SwapTokensForExactETH", type: "dex" },
  "0x5c11d795": { name: "SwapExactTokensForTokensSupportingFeeOnTransferTokens", type: "dex" },
  "0xfb3bdb41": { name: "SwapETHForExactTokens", type: "dex" },
  "0x791ac947": { name: "SwapExactTokensForETHSupportingFeeOnTransferTokens", type: "dex" },
  "0x02751cec": { name: "RemoveLiquidityETH", type: "liquidity" },
  "0xaf2979eb": { name: "RemoveLiquidityETHSupportingFeeOnTransferTokens", type: "liquidity" },
  "0xded9382a": { name: "RemoveLiquidityETHWithPermit", type: "liquidity" },
  "0x2195995c": { name: "RemoveLiquidityWithPermit", type: "liquidity" },
  "0xe8e33700": { name: "AddLiquidity", type: "liquidity" },
  "0xf305d719": { name: "AddLiquidityETH", type: "liquidity" },
  "0x0fda1d9b": { name: "CreateProxyWithNonce", type: "proxy" },
  "0x1688f0b9": { name: "CreateProxy", type: "proxy" },
  "0x88a7ca5c": { name: "Clone", type: "clone", action: "Create Clone" },
  "0x6a761202": { name: "Execute", type: "execute" },
  "0xb61d27f6": { name: "Execute", type: "execute" },
  "0x1cff79cd": { name: "Execute", type: "execute" },
  "0x3593564c": { name: "Execute", type: "execute" },
  "0xa0712d68": { name: "Mint", type: "mint" },
  "0x3a4b66f1": { name: "Stake", type: "stake" },
  "0x9dc29fac": { name: "Burn", type: "burn" },
};

export const detectMethod = (tx: Transaction, contractAbi?: any): MethodInfo => {
  if (!tx.input || tx.input === "0x") {
    return { name: "Transfer", type: "native" };
  }

  if (!tx.to) {
    return { name: "Contract Creation", type: "create" };
  }

  const methodId = tx.input.substring(0, 10).toLowerCase();

  if (contractAbi) {
    try {
      const iface = new Interface(contractAbi);
      const functionFragment = iface.getFunction(methodId);
      if (functionFragment) {
        return {
          name: functionFragment.name,
          type: "call",
          functionName: functionFragment.name,
          params: iface.decodeFunctionData(functionFragment, tx.input),
        };
      }
    } catch (error) {
      console.warn("Error decoding method using ABI:", error);
    }
  }

  return methodMap[methodId] || { name: methodId, type: "unknown" };
};
