import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { TokenResult } from "@/types/tokens"

interface TokenMarketProps {
  tokenDetail: TokenResult
}

export function TokenMarket({ tokenDetail }: TokenMarketProps) {
  const price: number = 1.0 // Placeholder
  const priceChangePercentage: number = -0.03 // Placeholder

  // Calculate market cap based on total supply and price
  const marketCap = calculateMarketCap(
    tokenDetail.totalSupply,
    tokenDetail.decimals,
    price,
  )

  return (
    <div className="card">
      <Card className="bg-card">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Market</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground">PRICE</p>
            <div className="flex items-center gap-2">
              <p className="text-lg font-semibold">${price.toFixed(2)}</p>
              {priceChangePercentage !== 0 && (
                <Badge
                  variant="outline"
                  className={
                    priceChangePercentage > 0
                      ? "text-emerald-500"
                      : "text-red-500"
                  }
                >
                  {priceChangePercentage > 0 ? "+" : ""}
                  {priceChangePercentage.toFixed(2)}%
                </Badge>
              )}
            </div>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">ONCHAIN MARKET CAP</p>
            <p className="text-lg font-semibold">${marketCap}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Helper function to calculate market cap
function calculateMarketCap(
  totalSupply: string,
  decimals: number,
  price: number,
): string {
  try {
    const supply = Number(totalSupply) / Math.pow(10, decimals)
    const marketCap = supply * price
    return marketCap.toLocaleString(undefined, { maximumFractionDigits: 2 })
  } catch (error) {
    return "N/A"
  }
}
