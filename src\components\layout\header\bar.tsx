"use client"

import { NATIVE_SYMBOL } from "@/constants"
import dynamic from "next/dynamic"
import s from "./header.module.scss"

const ThemeToggle = dynamic(() => import("./theme-toggle"), {
  ssr: false,
})

interface GasData {
  gasPriceInGwei: string
  maxFeePerGas: string
  maxPriorityFeePerGas: string
  baseFee: string
}

interface BarProps {
  gasData: GasData
}

export default function Bar({ gasData }: BarProps) {
  return (
    <div className={s.bar}>
      <div className={s.left}>
        <div className={s.price}>
          {NATIVE_SYMBOL} Price: <strong>${"0.00"}</strong>
        </div>
        <div className={s.gas}>
          Gas: <strong>{gasData.gasPriceInGwei} Gwei</strong>
          <ul className={s.fee}>
            <li>
              Base Fee: <span>{gasData.baseFee} Gwei</span>
            </li>
            <li>
              Priority Fee:
              <span>{gasData.maxPriorityFeePerGas} Gwei</span>
            </li>
          </ul>
        </div>
      </div>
      <div className={s.right}>
        <ThemeToggle />
      </div>
    </div>
  )
}
