import { Block } from "./blocks";
import { Token } from "./tokens";
import { Transaction } from "./transactions";

export interface SearchResponse {
  result: {
    data: {
      accounts: [
        {
          address: string;
          isContract: boolean;
          txCount: number;
        },
      ];
      tokens: Token[];
      transactions: Transaction[];
      blocks: Block[];
    };
    metadata: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  };
}
