import { DiscIcon as Discord, <PERSON><PERSON><PERSON>, Twitter } from "lucide-react"
import Link from "next/link"

export function SocialLinks() {
  return (
    <div className="flex space-x-4">
      <Link
        href="https://x.com/helios_layer1"
        className="text-muted-foreground hover:text-blue-700"
        target="_blank"
        rel="noopener noreferrer"
      >
        <Twitter className="h-5 w-5" />
      </Link>
      <Link
        href="https://github.com/helios-network"
        className="text-muted-foreground hover:text-blue-700"
        target="_blank"
        rel="noopener noreferrer"
      >
        <Github className="h-5 w-5" />
      </Link>
      <Link
        href="https://discord.gg/AjpJnJxt5e"
        className="text-muted-foreground hover:text-blue-700"
        target="_blank"
        rel="noopener noreferrer"
      >
        <Discord className="h-5 w-5" />
      </Link>
    </div>
  )
}
