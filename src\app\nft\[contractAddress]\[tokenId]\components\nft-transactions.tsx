"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, formatTimestamp, normalizeAddress } from "@/helpers/format"
import type { TransferTokenEvent } from "@/types/tokens"
import { AlertCircle } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"

interface NFTTransactionsProps {
  contractAddress: string
  tokenId: string
  transactions: TransferTokenEvent[]
  isLoading: boolean
  isError: boolean
  totalCount: number
}

export function NFTTransactions({
  contractAddress,
  tokenId,
  transactions,
  isLoading,
  isError,
  totalCount,
}: NFTTransactionsProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const limit = 25

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Transactions
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              Failed to load transaction history.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Item Activity</h2>

      <Card>
        <CardContent className="p-0 overflow-x-auto">
          {isLoading ? (
            <TransactionsSkeleton />
          ) : transactions.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <h3 className="text-lg font-semibold">No Transactions Found</h3>
              <p className="text-sm text-muted-foreground mt-2">
                No transaction history available for this NFT.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-10"></TableHead>
                  <TableHead>Transaction Hash</TableHead>
                  <TableHead>Block</TableHead>
                  <TableHead>Age</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>From</TableHead>
                  <TableHead>To</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((tx, index) => (
                  <TableRow key={tx.id}>
                    <TableCell className="text-center">
                      <div className="inline-flex items-center justify-center h-6 w-6 rounded-full border">
                        <span className="text-xs">{index + 1}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span
                          className="text-primary hover:underline cursor-pointer"
                          onClick={() =>
                            router.push(
                              PATH_ROUTER.TRANSACTION_DETAIL(
                                tx.transactionHash,
                              ),
                            )
                          }
                        >
                          {formatHash(tx.transactionHash, 10, 8)}
                        </span>
                        <div className="ml-2">
                          <CopyTooltip content={tx.transactionHash} />
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span
                        className="text-primary hover:underline cursor-pointer"
                        onClick={() =>
                          router.push(PATH_ROUTER.BLOCK_DETAIL(tx.blockNumber))
                        }
                      >
                        {tx.blockNumber}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            {formatTimestamp(tx.timestamp)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {new Date(tx.timestamp).toLocaleString()}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {tx.functionSignature || "Transfer"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              className="text-blue-700 hover:underline"
                              onClick={() => {
                                router.push(PATH_ROUTER.ADDRESS_DETAIL(tx.from))
                              }}
                            >
                              {formatHash(normalizeAddress(tx.from), 10, 8)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {normalizeAddress(tx.from)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <div className="ml-2">
                          <CopyTooltip
                            content={normalizeAddress(tx.from) ?? ""}
                          />
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              className="text-blue-700 hover:underline"
                              onClick={() => {
                                router.push(PATH_ROUTER.ADDRESS_DETAIL(tx.to))
                              }}
                            >
                              {formatHash(normalizeAddress(tx.to), 10, 8)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {normalizeAddress(tx.to)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <div className="ml-2">
                          <CopyTooltip
                            content={normalizeAddress(tx.to) ?? ""}
                          />
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {!isLoading && transactions.length > 0 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            A total of {totalCount} records found
          </div>
          <PaginationWithLinks
            page={page}
            pageSize={limit}
            totalCount={totalCount}
            baseUrl={`/nft/${contractAddress}/${tokenId}`}
          />
        </div>
      )}
    </div>
  )
}

function TransactionsSkeleton() {
  return (
    <div className="w-full">
      <table className="w-full caption-bottom text-sm">
        <thead className="[&_tr]:border-b">
          <tr className="border-b transition-colors">
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-10"></th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Transaction Hash
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Block
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Age
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Action
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              From
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              To
            </th>
          </tr>
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {Array.from({ length: 5 }).map((_, index) => (
            <tr key={index} className="border-b transition-colors">
              <td className="p-4 align-middle">
                <Skeleton className="h-6 w-6 rounded-full" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-32" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-16" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-20" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-6 w-16 rounded-full" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-24" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-24" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
