import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { NATIVE_SYMBOL } from "@/constants"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface BurntFeesTableProps {
  isLoading: boolean
  chartData: Array<{
    date: string
    burntFees: number
    burntFeesUsd: number
  }>
}

export default function BurntFeesTable({ isLoading, chartData }: BurntFeesTableProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
            <span className="text-blue-600 text-xs">Ξ</span>
          </div>
          <h3 className="text-base font-medium">Total Burnt Fees</h3>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="rounded-md border max-h-[350px] overflow-y-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Burnt Fees ({NATIVE_SYMBOL})</TableHead>
                <TableHead>Total Burnt Fees (USD)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: 7 }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell>
                      <Skeleton className="h-4 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-16" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-20" />
                    </TableCell>
                  </TableRow>
                ))
              ) : chartData.length > 0 ? (
                chartData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium text-blue-500">{item.date}</TableCell>
                    <TableCell>{item.burntFees}</TableCell>
                    <TableCell>{item.burntFeesUsd}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className="text-center py-4 text-muted-foreground">
                    No data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
