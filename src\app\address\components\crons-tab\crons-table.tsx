"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, normalizeAddress } from "@/helpers/format"
import { useGetAccountCrons } from "@/hooks/useTransactions"
import { Clock, ExternalLink, FileCode } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import type React from "react"

interface CronsTableProps {
  address: string
  loadingFallback?: React.ReactNode
}

export function CronsTable({ address, loadingFallback }: CronsTableProps) {
  const router = useRouter()
  const { data: dataCron, isLoading } = useGetAccountCrons(address, 1, 25)

  if (isLoading && loadingFallback) {
    return <>{loadingFallback}</>
  }

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp

    if (diff < 60000) return "just now"
    if (diff < 3600000) return `${Math.floor(diff / 60000)} minutes ago`
    if (diff < ********) return `${Math.floor(diff / 3600000)} hours ago`
    if (diff < **********) return `${Math.floor(diff / ********)} days ago`
    return `${Math.floor(diff / **********)} months ago`
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            Active
          </Badge>
        )
      case "ARCHIVED":
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">
            Archived
          </Badge>
        )
      case "EXPIRED":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
            Expired
          </Badge>
        )
      default:
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            {status}
          </Badge>
        )
    }
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Cron Address</TableHead>
          <TableHead>ID</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Target Contract</TableHead>
          <TableHead>Last Execution</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {dataCron &&
          dataCron?.map((cron: any) => (
            <TableRow key={cron.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  <FileCode className="h-4 w-4 text-muted-foreground" />
                  <CopyTooltip content={normalizeAddress(cron.address) ?? ""} />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger
                        className="text-blue-700 hover:underline"
                        onClick={() => {
                          router.push(PATH_ROUTER.ADDRESS_DETAIL(cron.address))
                        }}
                      >
                        {formatHash(normalizeAddress(cron.address), 6, 6)}
                      </TooltipTrigger>
                      <TooltipContent>
                        {normalizeAddress(cron.address)}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5"
                    asChild
                  >
                    <Link href={PATH_ROUTER.ADDRESS_DETAIL(cron.address)}>
                      <ExternalLink className="h-3.5 w-3.5" />
                    </Link>
                  </Button>
                </div>
              </TableCell>
              <TableCell>{cron.id}</TableCell>
              <TableCell>{getStatusBadge(cron.cronType)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <CopyTooltip
                    content={normalizeAddress(cron.contractAddress) ?? ""}
                  />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger
                        className="text-blue-700 hover:underline"
                        onClick={() => {
                          router.push(
                            PATH_ROUTER.ADDRESS_DETAIL(cron.contractAddress),
                          )
                        }}
                      >
                        {formatHash(
                          normalizeAddress(cron.contractAddress),
                          6,
                          6,
                        )}
                      </TooltipTrigger>
                      <TooltipContent>
                        {normalizeAddress(cron.contractAddress)}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5"
                    asChild
                  >
                    <Link
                      href={PATH_ROUTER.ADDRESS_DETAIL(cron.contractAddress)}
                    >
                      <ExternalLink className="h-3.5 w-3.5" />
                    </Link>
                  </Button>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {cron.lastExecution
                      ? formatTimeAgo(cron.lastExecution)
                      : "Never executed"}
                  </span>
                </div>
              </TableCell>
            </TableRow>
          ))}

        {(!dataCron || dataCron.length === 0) && (
          <TableRow>
            <TableCell
              colSpan={5}
              className="text-center py-6 text-muted-foreground"
            >
              No cron jobs found for this address
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  )
}
