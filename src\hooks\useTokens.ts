import apiClient from "@/lib/api/apiClient"
import type {
  BalanceResponse,
  ChainStatsResponse,
  TokenBalanceListResponse,
  TokenDetailResponse,
  TokenHolderListResponse,
  TokenListResponse,
  TransferTokenEventListResponse,
} from "@/types/tokens"
import { useQuery, UseQueryOptions } from "@tanstack/react-query"
import { numberToHex } from "viem"

export const useGetBalance = (address: string) => {
  return useQuery({
    queryKey: ["balance", address],
    queryFn: async () => {
      const response = await apiClient.post<
        BalanceResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getBalance",
        params: [address, "latest"],
        id: 1,
      })
      return response?.result
    },
  })
}

export const useTokens = (page: number, limit: number) => {
  const isHex = (value: any) => /^0x[0-9a-fA-F]+$/.test(value)

  const pageHex = isHex(page) ? page : numberToHex(page)
  const limitHex = isHex(limit) ? limit : numberToHex(limit)

  return useQuery({
    queryKey: ["tokens", pageHex, limitHex],
    queryFn: async () => {
      const response = await apiClient.post<
        TokenListResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTokensByPageAndSize",
        params: [pageHex, limitHex],
        id: 1,
      })
      console.log("response", response)
      return response?.result
    },
  })
}

export const useTokenBalance = (
  address: string,
  page: number = 1,
  size: number = 100,
) => {
  const pageHex = numberToHex(page)
  const sizeHex = numberToHex(size)

  return useQuery({
    queryKey: ["account-token-balances", address, pageHex, sizeHex],
    queryFn: async () => {
      const response = await apiClient.post<
        TokenBalanceListResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getAccountTokensBalanceByPageAndSize",
        params: [address, pageHex, sizeHex],
        id: 1,
      })
      return response?.result
    },
    enabled: !!address,
  })
}

export const useGetAccountTokenBalance = (
  userAddress: string,
  tokenAddress: string,
) => {
  return useQuery({
    queryKey: ["account-token-balances", userAddress, tokenAddress],
    queryFn: async () => {
      const response = await apiClient.post<
        TokenBalanceListResponse | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getAccountTokenBalance",
        params: [userAddress, tokenAddress],
        id: 1,
      })
      return response?.result
    },
    enabled: !!tokenAddress && !!userAddress,
  })
}

export const useGetTransferTokenEventByAddress = (
  tokenAddress: string | null,
  addressAccount: string | null,
  page: number,
  limit: number,
  tokenId?: number | null,
  options?: Omit<
    UseQueryOptions<TransferTokenEventListResponse | undefined, Error>,
    "queryKey" | "queryFn"
  >,
) => {
  return useQuery({
    queryKey: [
      "getTransferTokenEventByAddress",
      tokenAddress,
      addressAccount,
      page,
      limit,
      tokenId,
    ],
    queryFn: async () => {
      const params: (string | number | null | object)[] = [
        tokenAddress,
        addressAccount,
        page,
        limit,
      ]

      const lastParam: { tokenId?: number } = {}
      if (tokenId !== undefined && tokenId !== null) {
        lastParam.tokenId = tokenId
      }
      params.push(lastParam)

      const response = await apiClient.post<
        TransferTokenEventListResponse | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTransferTokenEventByAddress",
        params: params,
        id: 1,
      })
      return response?.result
    },
    ...options,
  })
}

export const useGetTopTokenHolders = (
  address: string,
  page: string,
  limit: string,
) => {
  return useQuery({
    queryKey: ["stats_top_token_holders", address, page, limit],
    queryFn: async () => {
      const response = await apiClient.post<
        TokenHolderListResponse | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "stats_top_token_holders",
        params: [address, page, limit],
        id: 1,
      })
      return response?.result
    },
  })
}

export const useGetChainStats = (refreshInterval?: number) => {
  return useQuery({
    queryKey: ["chain-stats"],
    queryFn: async () => {
      const response = await apiClient.post<ChainStatsResponse | undefined>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getChainStats",
          params: [],
          id: 1,
        },
      )
      return response?.result
    },
    refetchInterval: refreshInterval,
  })
}

export const useGetTokenDetail = (tokenAddress: string) => {
  return useQuery({
    queryKey: ["token-detail", tokenAddress],
    queryFn: async () => {
      const response = await apiClient.post<
        TokenDetailResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTokenDetail",
        params: [tokenAddress],
        id: 1,
      })
      return response?.result
    },
  })
}
