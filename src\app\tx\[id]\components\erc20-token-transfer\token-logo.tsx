"use client"

import { DEFAULT_TOKEN_IMAGE } from "@/constants"
import { TokenType } from "@/types/tokens"
import { Coins, ImageIcon } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

interface TokenDetails {
  address: string
  name: string
  symbol: string
  logo: string | null
  decimals?: number
}

interface TokenLogoProps {
  tokenDetails: TokenDetails | null | undefined
  tokenType: string
  size?: number
}

export function TokenLogo({
  tokenDetails,
  tokenType,
  size = 20,
}: TokenLogoProps) {
  const [imageError, setImageError] = useState(false)

  const getTokenLogoUrl = (tokenDetails: TokenDetails | null | undefined) => {
    if (tokenDetails?.logo) {
      return `https://testnet1-cdn.helioschainlabs.org/hash/${tokenDetails.logo}`
    }
    return DEFAULT_TOKEN_IMAGE
  }

  if (imageError) {
    if (tokenType === TokenType.ERC721 || tokenType === TokenType.ERC1155) {
      return <ImageIcon className="h-5 w-5 text-purple-500" />
    }
    return <Coins className="h-5 w-5 text-blue-500" />
  }

  return (
    <Image
      src={getTokenLogoUrl(tokenDetails) || "/placeholder.svg"}
      alt={tokenDetails?.symbol || "Token"}
      width={size}
      height={size}
      className="rounded-full"
      onError={() => setImageError(true)}
    />
  )
}
