import { Info } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";

interface ContractSecurityProps {
  hasAudit: boolean;
  auditUrl?: string;
}

export function ContractSecurity({ hasAudit }: ContractSecurityProps) {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Contract Security Audit</h3>

      {hasAudit ? (
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-green-500"></div>
          <span className="text-sm">Security Audit Verified</span>
          <Button variant="link" size="sm" className="h-auto p-0 text-primary">
            View Audit Report
          </Button>
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
          <span className="text-sm">No Contract Security Audit Submitted</span>
          <Button variant="link" size="sm" className="h-auto p-0 text-primary">
            Submit Audit Here
          </Button>
        </div>
      )}

      <Alert variant="default" className="mt-4">
        <Info className="h-4 w-4" />
        <AlertDescription className="text-sm">
          Security audits help identify vulnerabilities and ensure contract
          safety. A verified audit indicates the contract has been reviewed by
          security professionals.
        </AlertDescription>
      </Alert>
    </div>
  );
}
