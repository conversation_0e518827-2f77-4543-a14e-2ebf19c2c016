"use client"

import { CSVExportButton } from "@/components/csv-button-export"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatTimestamp } from "@/helpers/format"
import { useListEpoch } from "@/hooks/useEpoch"
import { formatEpochsForCSV } from "@/lib/utils/csv-export"
import { Tooltip } from "@radix-ui/react-tooltip"
import { AlertCircle } from "lucide-react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"

export default function EpochList() {
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page") || "1"))
  const pageSize = Number(searchParams.get("pageSize") || "10")

  const { data, isLoading, isError, error } = useListEpoch(page, pageSize)

  if (isError) {
    return (
      <div className="card">
        <Card className="w-full">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold">Error Loading Epochs</h3>
              <p className="text-sm text-muted-foreground mt-2">
                {error instanceof Error
                  ? error.message
                  : "An unexpected error occurred"}
              </p>
              <Button asChild className="mt-4">
                <Link href="/epochs?page=1">Try Again</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Epochs</h1>
        {!isLoading && data && data.data && data.data.length > 0 && (
          <CSVExportButton
            data={data.data}
            formatter={formatEpochsForCSV}
            filename={`epochs-page-${page}.csv`}
          />
        )}
      </div>
      <div>
        {isLoading ? (
          <LoadingSkeleton />
        ) : !data || !data.data || data.data.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <h3 className="text-lg font-semibold">No Epochs Found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              There are no epochs available to display.
            </p>
          </div>
        ) : (
          <div className="card">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Epoch Number</TableHead>
                    <TableHead>Epoch Length</TableHead>
                    <TableHead>Start Block</TableHead>
                    <TableHead>End Block</TableHead>
                    <TableHead>Age</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.data.map((epoch) => (
                    <TableRow
                      key={epoch.id}
                      className="cursor-pointer hover:bg-muted/50"
                    >
                      <TableCell className="font-medium">
                        <Link
                          href={PATH_ROUTER.EPOCH_DETAIL(epoch.epochNumber)}
                          className="text-blue-700 hover:underline"
                        >
                          {epoch.epochNumber}
                        </Link>
                      </TableCell>
                      <TableCell>{epoch.epochLength}</TableCell>
                      <TableCell>{epoch.startBlock}</TableCell>
                      <TableCell>{epoch.endBlock}</TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              {formatTimestamp(epoch.timestamp)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {new Date(epoch.timestamp).toLocaleString()}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </div>
      {!isLoading && data && data.metadata && (
        <div className="mt-4">
          <PaginationWithLinks
            page={page}
            pageSize={pageSize}
            totalCount={data.metadata.total}
            pageSearchParam="page"
            pageSizeSelectOptions={{
              pageSizeOptions: [10, 20, 50, 100],
            }}
          />
        </div>
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Epoch Number</TableHead>
            <TableHead>Epoch Length</TableHead>
            <TableHead>Start Block</TableHead>
            <TableHead>End Block</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Updated At</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 10 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-5 w-12" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-16" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-32" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
