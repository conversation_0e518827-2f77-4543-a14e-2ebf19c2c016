import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card"
import { NATIVE_SYMBOL } from "@/constants"

export default function AboutEIP1559() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>About EIP-1559</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          EIP-1559 is a significant upgrade to the Helios fee market that introduced a base fee that is burned, reducing
          the overall supply of
          {NATIVE_SYMBOL}. This chart shows key metrics related to EIP-1559:
        </p>
        <ul className="mt-2 space-y-1 text-sm text-muted-foreground list-disc pl-5">
          <li>
            <span className="font-medium">Base Fee</span> - The minimum fee required for a transaction to be included in
            a block. This fee is burned.
          </li>
          <li>
            <span className="font-medium">Priority Fee</span> - An optional tip paid to miners/validators to incentivize
            them to include a transaction.
          </li>
          <li>
            <span className="font-medium">Burnt Fees</span> - The total amount of {NATIVE_SYMBOL} that has been removed
            from circulation through the burning mechanism.
          </li>
        </ul>
      </CardContent>
    </Card>
  )
}
