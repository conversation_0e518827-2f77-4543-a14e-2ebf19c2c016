"use client"

import { <PERSON><PERSON>, <PERSON>ertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON>lt<PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useTransactionAndTransactionReceiptByTxHash } from "@/hooks/useTransactions"
import {
  useGetAccountInfo,
  useGetCode,
  useSearchCode,
} from "@/hooks/useVerifyContract"
import { BytecodeType, type Source } from "@/types/verify-contract"
import { getAddress } from "ethers"
import { Check, Info, X } from "lucide-react"
import { useEffect, useState } from "react"
import { CodeTab } from "./code-tab"
import { ContractReadProxy } from "./contract-read-proxy"
import { ContractWriteProxy } from "./contract-write-proxy"
import { ReadTab } from "./read-tab"
import { WriteTab } from "./write-tab"

export interface ContractMethod {
  name: string
  signature: string
  inputs?: { name: string; type: string }[]
  outputs?: { type: string }[]
  type: string
  stateMutability: string
}

interface ContractFile {
  name: string
  content: string
}

interface ContractTabProps {
  address: string
  isVerified?: boolean
  isProxy?: boolean
  contractName?: string
  compilerVersion?: string
  optimizationEnabled?: boolean
  methods?: ContractMethod[]
  sourceFiles?: ContractFile[]
  implementationAddress?: string
  proxyPattern?: string
  previousImplementation?: string
}

// Contract Info Skeleton Component
const ContractInfoSkeleton = () => (
  <div className="flex flex-col gap-4">
    <Alert>
      <Info className="h-4 w-4" />
      <AlertDescription className="flex items-center text-sm">
        Descriptions included below are taken from the contract source code.
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className="ml-1 text-primary underline">
              NatSpec
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs text-xs">
                Helios does not provide any guarantees on their safety or
                accuracy.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </AlertDescription>
    </Alert>

    <div className="flex items-center gap-2">
      <Skeleton className="h-6 w-40" />
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">
          Contract Name:
        </p>
        <Skeleton className="h-5 w-40" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">
          Optimization Enabled:
        </p>
        <Skeleton className="h-5 w-32" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">
          Compiler Version:
        </p>
        <Skeleton className="h-5 w-48" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">
          Implementation Address:
        </p>
        <Skeleton className="h-5 w-56" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">
          Other Settings:
        </p>
        <Skeleton className="h-5 w-36" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">
          Proxy Pattern:
        </p>
        <Skeleton className="h-5 w-40" />
      </div>
    </div>
  </div>
)

const CodeTabSkeleton = () => (
  <div className="space-y-4 p-4">
    <div className="flex items-center justify-between">
      <Skeleton className="h-6 w-48" />
      <Skeleton className="h-8 w-24" />
    </div>
    <Skeleton className="h-8 w-full" />
    <div className="space-y-2">
      {Array(15)
        .fill(0)
        .map((_, i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
    </div>
  </div>
)

const ContractMethodsSkeleton = () => (
  <div className="space-y-6 p-4">
    {Array(5)
      .fill(0)
      .map((_, i) => (
        <div key={i} className="space-y-2 border-b pb-4">
          <Skeleton className="h-6 w-48" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
          <Skeleton className="h-9 w-24 mt-2" />
        </div>
      ))}
  </div>
)

export function ContractTab({
  address,
  isVerified = false,
  isProxy = false,
  contractName = "-",
  compilerVersion = "-",
  implementationAddress = "-",
  proxyPattern = "-",
  previousImplementation = "-",
}: ContractTabProps) {
  const [activeTab, setActiveTab] = useState("code")
  const [sourceData, setSourceData] = useState<Source | undefined>(undefined)
  const [isLoadingSource, setIsLoadingSource] = useState(false)
  const [validatedImplAddress, setValidatedImplAddress] = useState<string>("-")

  const { data: accountInfo, isLoading: isLoadingAccountInfo } =
    useGetAccountInfo(address)

  const { data: transactionData, isLoading: isLoadingTransaction } =
    useTransactionAndTransactionReceiptByTxHash(accountInfo?.firstTxHash || "")

  // Search code API hooks for both proxy and implementation
  const { mutate: searchProxyCode, isPending: isLoadingProxyCode } =
    useSearchCode()
  const { mutate: searchImplCode, isPending: isLoadingImplCodeSearch } =
    useSearchCode()

  // Validate implementation address
  useEffect(() => {
    if (implementationAddress && implementationAddress !== "-") {
      try {
        // Validate and normalize the Ethereum address
        const normalizedAddress = getAddress(implementationAddress)
        setValidatedImplAddress(normalizedAddress)
      } catch (error) {
        console.error("Invalid implementation address:", error)
        setValidatedImplAddress("-")
      }
    } else {
      setValidatedImplAddress("-")
    }
  }, [implementationAddress])

  // Get bytecode for implementation if needed
  const { data: implCode, isLoading: isLoadingGetImplCode } = useGetCode(
    validatedImplAddress !== "-" ? validatedImplAddress : "",
  )

  const actualIsProxy = accountInfo?.isProxy || isProxy
  const actualImplAddress = validatedImplAddress

  useEffect(() => {
    if (!transactionData?.transaction?.input) return

    setIsLoadingSource(true)

    searchProxyCode(
      {
        bytecode: transactionData.transaction.input,
        bytecodeType: BytecodeType.CREATION_INPUT,
      },
      {
        onSuccess: (proxyData) => {
          const hasProxySource =
            proxyData?.result?.sources && proxyData.result.sources.length > 0

          // If this is a proxy and proxy code search returned empty results, search for implementation code
          if (
            actualIsProxy &&
            !hasProxySource &&
            actualImplAddress &&
            actualImplAddress !== "-" &&
            implCode &&
            implCode !== "0x"
          ) {
            searchImplCode(
              {
                bytecode: implCode,
                bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
              },
              {
                onSuccess: (implData) => {
                  console.log("Implementation code search completed", implData)

                  if (
                    implData?.result?.sources &&
                    implData.result.sources.length > 0
                  ) {
                    setSourceData(implData.result.sources[0])
                  }
                  setIsLoadingSource(false)
                },
                onError: () => {
                  setIsLoadingSource(false)
                },
              },
            )
          } else {
            if (hasProxySource) {
              setSourceData(proxyData.result.sources[0])
            }
            setIsLoadingSource(false)
          }
        },
        onError: () => {
          setIsLoadingSource(false)
        },
      },
    )
  }, [
    transactionData,
    accountInfo,
    actualImplAddress,
    implCode,
    searchProxyCode,
    searchImplCode,
    actualIsProxy,
  ])

  // Use source data if available, otherwise use props
  const displayName = sourceData?.contractName || contractName
  const displayCompilerVersion = sourceData?.compilerVersion || compilerVersion

  // Parse compiler settings
  let optimizationRuns = "200"
  if (sourceData?.compilerSettings) {
    try {
      const settings = JSON.parse(sourceData.compilerSettings)
      if (settings.optimizer && settings.optimizer.runs) {
        optimizationRuns = settings.optimizer.runs.toString()
      }
    } catch (e) {
      console.error("Error parsing compiler settings:", e)
    }
  }

  // Check if any data is loading
  const isLoading =
    isLoadingSource ||
    isLoadingAccountInfo ||
    isLoadingTransaction ||
    isLoadingProxyCode ||
    isLoadingImplCodeSearch ||
    isLoadingGetImplCode

  return (
    <div className="space-y-6">
      {isLoading ? (
        <ContractInfoSkeleton />
      ) : (
        <div className="flex flex-col gap-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription className="flex items-center text-sm">
              Descriptions included below are taken from the contract source
              code.
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="ml-1 text-primary underline">
                    NatSpec
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs text-xs">
                      Helios does not provide any guarantees on their safety or
                      accuracy.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </AlertDescription>
          </Alert>

          <div className="flex items-center gap-2">
            {isVerified || sourceData ? (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                <Check className="mr-1 h-3 w-3" />
                Contract Source Code Verified {isVerified && "(Exact Match)"}
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="bg-red-50 text-red-700 border-red-200"
              >
                <X className="mr-1 h-3 w-3" />
                Contract source code not verified
              </Badge>
            )}
            {actualIsProxy && (
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 border-blue-200"
              >
                Proxy Contract
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Contract Name:
              </p>
              <p>{displayName}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Optimization Enabled:
              </p>
              <p>{`Yes with ${optimizationRuns} runs`}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Compiler Version:
              </p>
              <p>{displayCompilerVersion}</p>
            </div>
            {actualIsProxy && (
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Implementation Address:
                </p>
                <p>{actualImplAddress}</p>
              </div>
            )}
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Other Settings:
              </p>
              <p>default evmVersion</p>
            </div>
            {actualIsProxy && (
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Proxy Pattern:
                </p>
                <p>{proxyPattern}</p>
              </div>
            )}
          </div>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full justify-start bg-muted/50 p-0">
          <TabsTrigger
            value="code"
            className="rounded-none data-[state=active]:bg-background flex items-center gap-1"
          >
            Code
            {(isVerified || sourceData) && (
              <Check className="h-3 w-3 text-green-600" />
            )}
          </TabsTrigger>
          <TabsTrigger
            value="read"
            className="rounded-none data-[state=active]:bg-background"
          >
            Read Contract
          </TabsTrigger>
          <TabsTrigger
            value="write"
            className="rounded-none data-[state=active]:bg-background"
          >
            Write Contract
          </TabsTrigger>
          {actualIsProxy && (
            <>
              <TabsTrigger
                value="read-proxy"
                className="rounded-none data-[state=active]:bg-background"
              >
                Read as Proxy
              </TabsTrigger>
              <TabsTrigger
                value="write-proxy"
                className="rounded-none data-[state=active]:bg-background"
              >
                Write as Proxy
              </TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="code">
          {isLoading ? <CodeTabSkeleton /> : <CodeTab source={sourceData} />}
        </TabsContent>

        <TabsContent value="read" className="mt-4">
          {isLoading ? (
            <ContractMethodsSkeleton />
          ) : (
            <ReadTab address={address} key={address} source={sourceData} />
          )}
        </TabsContent>

        <TabsContent value="write" className="mt-4">
          {isLoading ? (
            <ContractMethodsSkeleton />
          ) : (
            <WriteTab address={address} key={address} source={sourceData} />
          )}
        </TabsContent>

        {actualIsProxy && actualImplAddress !== "-" && (
          <>
            <TabsContent value="read-proxy" className="mt-4">
              {isLoading ? (
                <ContractMethodsSkeleton />
              ) : (
                <ContractReadProxy
                  address={address}
                  implementationAddress={actualImplAddress}
                  proxyPattern={proxyPattern}
                  previousImplementation={previousImplementation}
                />
              )}
            </TabsContent>

            <TabsContent value="write-proxy" className="mt-4">
              {isLoading ? (
                <ContractMethodsSkeleton />
              ) : (
                <ContractWriteProxy
                  address={address}
                  implementationAddress={actualImplAddress}
                  proxyPattern={proxyPattern}
                  previousImplementation={previousImplementation}
                />
              )}
            </TabsContent>
          </>
        )}
      </Tabs>

      <div className="mt-4">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {actualIsProxy
              ? "This is a proxy contract. The implementation logic is at a separate address but called through this contract."
              : "A contract address hosts a smart contract, which is a set of code stored on the blockchain that runs when predetermined conditions are met."}
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
