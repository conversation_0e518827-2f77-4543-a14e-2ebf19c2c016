"use client"

import { Button } from "@/components/ui/button"

export type TimeRangeOption = {
  value: string
  label: string
}

interface TimeRangeSelectorProps {
  timeRange: string
  onChange: (range: string) => void
  options?: TimeRangeOption[]
  source?: string
}

export default function TimeRangeSelector({
  timeRange,
  onChange,
  options = [
    { value: "1m", label: "1m" },
    { value: "6m", label: "6m" },
    { value: "1y", label: "1y" },
    { value: "all", label: "All" },
  ],
  source = "Helios.io",
}: TimeRangeSelectorProps) {
  return (
    <div className="flex items-center space-x-4">
      {source && <div className="text-sm text-muted-foreground">Source: {source}</div>}
      <div className="flex space-x-1">
        {options.map((option) => (
          <Button
            key={option.value}
            variant={timeRange === option.value ? "default" : "outline"}
            size="sm"
            onClick={() => onChange(option.value)}
          >
            {option.label}
          </Button>
        ))}
      </div>
    </div>
  )
}

