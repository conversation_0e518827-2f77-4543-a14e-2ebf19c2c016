"use client"

import { Skeleton } from "@/components/ui/skeleton"

interface NFTPriceStatsProps {
  contractAddress: string
  tokenId: string
}

export function NFTPriceStats({
  contractAddress,
  tokenId,
}: NFTPriceStatsProps) {
  const isLoading = false

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-20 w-full" />
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="text-sm text-gray-500 mb-1">Floor Price</div>
        <div className="font-bold">--</div>
        <div className="text-sm text-gray-500">--</div>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="text-sm text-gray-500 mb-1">Last Sale</div>
        <div className="font-bold">--</div>
        <div className="text-sm text-gray-500">--</div>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="text-sm text-gray-500 mb-1">Best Offer</div>
        <div className="font-bold">--</div>
        <div className="text-sm text-gray-500">--</div>
      </div>
    </div>
  )
}
