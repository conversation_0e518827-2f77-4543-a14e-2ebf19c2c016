"use client";

import type { ContractMethod } from "@/types/contract";
import { ReadMethodCard } from "./read-method-card";
import { WriteMethodCard } from "./write-method-card";

interface ContractMethodCardProps {
  method: ContractMethod;
  contractAddress: string;
  index: number;
  isWrite?: boolean;
}

export function ContractMethodCard({
  method,
  contractAddress,
  index,
  isWrite = false,
}: ContractMethodCardProps) {
  // This is now just a wrapper that delegates to the appropriate card type
  if (isWrite) {
    return (
      <WriteMethodCard
        method={method}
        contractAddress={contractAddress}
        index={index}
      />
    );
  }

  return (
    <ReadMethodCard
      method={method}
      contractAddress={contractAddress}
      index={index}
    />
  );
}
