import { clsx, type ClassValue } from "clsx"
import { format } from "date-fns"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(Math.round(num * 100) / 100)
}

// Update the hexToNumber function to handle invalid hex strings
export function hexToNumber(hex: string | number | null | undefined): number {
  if (hex === null || hex === undefined) {
    return 0
  }

  if (typeof hex === "number") {
    return hex
  }

  const hexString = String(hex)

  // Check if it's a valid hex string
  if (!hexString || !hexString.startsWith("0x")) {
    // Try to parse it as a regular number if it's not a hex string
    const num = Number(hexString)
    return isNaN(num) ? 0 : num
  }

  try {
    return Number.parseInt(hexString, 16)
  } catch (error) {
    console.error("Error converting hex to number:", error)
    return 0
  }
}
