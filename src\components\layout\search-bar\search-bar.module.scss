.search {
  transition: width var(--header-transition), max-width var(--header-transition);
  width: var(--search-width, calc(600px + 300 * (100vw - 320px) / 1080));
  max-width: var(--search-max-width, 90%);
  margin: auto;
  position: relative;
  margin-top: calc(var(--content-padding) * 0.5);
}

.form {
  --input-height: calc(56px + 8 * (100vw - 320px) / 1080);
  height: var(--input-height);
  position: relative;
  background: linear-gradient(
    to right,
    hsl(var(--white) / 0.05),
    hsl(var(--white) / 0.1),
    hsl(var(--white) / 0.05)
  );
  box-shadow: 0 0 0 1px hsl(var(--white) / 0.05) inset;
  border-radius: var(--radius);
  width: 100%;
  z-index: 1;

  .input {
    transition: outline-color 0.2s ease-in-out;
    box-shadow: 0 0 0 !important;
    outline: none;
    border: 0;
    position: absolute;
    inset: 0;
    height: 100%;
    font-weight: 400;
    font-size: calc(var(--input-height) / 4);
    padding-left: var(--input-height);
    padding-right: 1em;
    border-radius: inherit;
    outline-color: hsl(var(--primary) / 0) !important;

    &:focus {
      outline-color: hsl(var(--primary)) !important;
    }
  }
}

.icon {
  width: var(--input-height);
  height: var(--input-height);
  position: absolute;
  top: 0;
  left: 0;
  padding: calc(var(--input-height) / 3.5);
}

.results {
  --padding: calc(16px + 4 * (100vw - 320px) / 1080);
  position: absolute;
  background-color: hsl(var(--black) / 0.75);
  box-shadow: 0 0 0 1000px hsl(var(--white) / 0.1) inset,
    0 0 0 1px rgba(255, 255, 255, 0.05) inset, 0 16px 80px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(16px);
  z-index: 1000;
  width: 100%;
  border-radius: var(--radius);
  margin-top: 0.5em;
  max-height: 300px;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.result {
  padding: var(--padding);

  .title {
    font-size: 0.85em;
    opacity: 0.5;
  }
}

.item {
  transition: background-color 0.2s ease-in-out;
  font-size: 0.85em;
  padding: 1em;
  background-color: hsl(var(--white) / 0.05);
  border-radius: calc(var(--radius) - 4px);
  margin-top: calc(var(--padding) * 0.5);
  cursor: pointer;

  &:hover {
    background-color: hsl(var(--white) / 0.1);
  }

  small {
    font-size: 0.95em;
    opacity: 0.5;
  }

  .contract {
    font-size: 0.95em;
    background-color: hsl(var(--white) / 0.1);
    padding: 0.25em 0.5em;
    border-radius: 8px;
    align-self: flex-start;
  }
}

$dash-length: 4;
$dash-gap: 3;
$animation-duration: 0.5s;

.liner {
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
  position: absolute;
  width: 400px;
  top: 50%;
  z-index: 0;
  stroke-dasharray: $dash-length $dash-gap;
  animation: dash $animation-duration linear infinite;
  stroke: rgba(255, 255, 255, 0.2);
  stroke-dasharray: $dash-length $dash-gap;
  fill: none;
  mask: linear-gradient(to left, black, transparent);

  @keyframes dash {
    to {
      stroke-dashoffset: -7;
    }
  }

  &Left {
    right: 100%;
    transform: translateY(-71%);
  }

  &Right {
    left: 100%;
    transform: scaleX(-1) translateY(-71%);
  }
}
