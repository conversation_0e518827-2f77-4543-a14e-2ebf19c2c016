"use client"

import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { useState } from "react"

interface NFTImageProps {
  image?: string
  name?: string
  tokenId: string
}

export function NFTImage({ image, name, tokenId }: NFTImageProps) {
  const [imageError, setImageError] = useState(false)

  const displayImage =
    image && !imageError ? image : `/placeholder.svg?height=500&width=500`

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="aspect-square relative bg-gray-100 flex items-center justify-center">
          <Image
            src={displayImage || "/placeholder.svg"}
            alt={name || `Token #${tokenId}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            unoptimized={image?.startsWith("http")}
            onError={() => setImageError(true)}
          />
        </div>
      </CardContent>
    </Card>
  )
}
