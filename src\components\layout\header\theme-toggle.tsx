"use client"

import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import s from "./header.module.scss"

export default function ThemeToggle() {
  const { setTheme, theme } = useTheme()

  const toggleTheme = () => {
    if (theme === "dark") {
      setTheme("light")
    } else {
      setTheme("dark")
    }
  }

  return (
    <button className={s.theme} onClick={toggleTheme}>
      {theme === "dark" || !theme ? (
        <>
          <Sun />
          <span>Light</span>
        </>
      ) : (
        <>
          <Moon />
          <span>Dark</span>
        </>
      )}
    </button>
  )
}
