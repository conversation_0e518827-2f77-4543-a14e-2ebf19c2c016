@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-main);
  font-weight: 500;
  font-feature-settings: "salt" on;
  color: hsl(var(--text));
  /* connect button */
  --wui-font-family: var(--font-main);
  --wui-color-accent-100: hsl(var(--primary));
  --wui-color-gray-glass-010: hsl(var(--white) / 0);
  --wui-spacing-l: 1.5em;
  --wui-border-radius-m: 1em;
  overflow-x: hidden;
}

*::selection {
  background-color: hsl(var(--primary) / 0.75);
  color: hsl(var(--white));
  -webkit-text-fill-color: hsl(var(--white));
}

*::-moz-selection {
  background-color: hsl(var(--primary) / 0.75);
  color: hsl(var(--white));
  -webkit-text-fill-color: hsl(var(--white));
}

@layer base {
  :root {
    --spacing: 14px;
    --white: 220 86% 97%;
    --black: 231 94% 7%;
    --text: 229 100% 12%;
    --background: 220 86% 97%;
    --foreground: 229 100% 12%;
    --card: 228 100% 99%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 227 100% 40%;
    --primary-foreground: 210 40% 98%;
    --secondary: 223 72% 94%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: calc(16px + 4 * (100vw - 320px) / 1080);
    --cubic: cubic-bezier(0.62, 0.05, 0, 1);

    /* connect button */
    --wui-font-family: var(--font-main);
    --wui-color-accent-100: hsl(var(--primary));
    --wui-color-gray-glass-010: hsl(var(--white) / 0);
    --wui-spacing-l: 1.5em;
    --wui-border-radius-m: 1em;
  }

  .dark {
    --text: 220 86% 97%;
    --background: 231 94% 7%;
    --foreground: 210 40% 98%;
    --card: 231 84% 10%;
    --card-foreground: 210 40% 98%;
    --popover: 229 100% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 227 100% 50%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 231 84% 10%;
    --secondary-foreground: 210 40% 98%;
    --muted: 231 84% 10%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 231 84% 10%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 231 60% 16%;
    --input: 231 84% 10%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  .card {
    background-color: hsl(var(--card));
    border-radius: var(--radius);
    padding: 0.25rem;
    box-shadow: 0 16px 40px rgba(1, 8, 43, 0.05);
  }

  .top {
    overflow: visible;
  }

  @media screen and (max-width: 1084px) {
    .top {
      overflow: hidden;
    }
  }
}