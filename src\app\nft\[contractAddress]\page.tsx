import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

export default function NFTExplorerPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-3xl mx-auto text-center mb-12">
        <h1 className="text-3xl font-bold mb-4">NFT Explorer</h1>
        <p className="text-muted-foreground mb-8">
          Search and explore NFTs across the blockchain
        </p>

        <div className="flex w-full max-w-xl mx-auto items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search by NFT contract address, token ID, or collection name"
              className="pl-10"
            />
          </div>
          <Button type="submit">Search</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link
          href="/nft/******************************************/983"
          className="block"
        >
          <Card className="overflow-hidden transition-all hover:shadow-md">
            <div className="aspect-square bg-gray-100 relative">
              <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                NFT Preview
              </div>
            </div>
            <CardHeader className="p-4">
              <CardTitle className="text-lg">OnChainShiba #983</CardTitle>
              <CardDescription>
                ******************************************
              </CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Last Price
                </span>
                <span className="font-medium">23 ETH</span>
              </div>
            </CardContent>
          </Card>
        </Link>

        {Array.from({ length: 5 }).map((_, index) => (
          <Card
            key={index}
            className="overflow-hidden transition-all hover:shadow-md"
          >
            <div className="aspect-square bg-gray-100 relative">
              <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                NFT Preview
              </div>
            </div>
            <CardHeader className="p-4">
              <CardTitle className="text-lg">
                Example NFT #{index + 1}
              </CardTitle>
              <CardDescription>0x1234...5678</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Last Price
                </span>
                <span className="font-medium">{(index + 1) * 0.5} ETH</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
