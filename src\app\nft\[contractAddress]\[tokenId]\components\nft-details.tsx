"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash } from "@/helpers/format"
import type { NftTokenDetailResponse } from "@/types/nft"
import { ChevronDown, ChevronUp, Info } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"

interface NFTDetailsProps {
  nft: NftTokenDetailResponse["result"]
  contractAddress: string
  tokenId: string
}

export function NFTDetails({ nft, contractAddress, tokenId }: NFTDetailsProps) {
  const [isOpen, setIsOpen] = useState(true)
  const router = useRouter()

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="border rounded-lg overflow-hidden"
    >
      <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gray-50 hover:bg-gray-100">
        <div className="flex items-center">
          <Info className="h-4 w-4 mr-2" />
          <span className="font-medium">Details</span>
        </div>
        {isOpen ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {nft.ownerAddresses && nft.ownerAddresses.length > 0 && (
              <DetailRow
                label="Owner"
                value={formatHash(nft.ownerAddresses[0], 10, 8)}
                fullValue={nft.ownerAddresses[0]}
                isClickable={true}
                onClick={() =>
                  router.push(
                    PATH_ROUTER.ADDRESS_DETAIL(nft.ownerAddresses[0]!),
                  )
                }
              />
            )}

            <DetailRow
              label="Contract Address"
              value={formatHash(contractAddress, 10, 8)}
              fullValue={contractAddress}
              isClickable={true}
              onClick={() =>
                router.push(PATH_ROUTER.ADDRESS_DETAIL(contractAddress))
              }
            />

            {nft.creatorAddress && (
              <DetailRow
                label="Creator"
                value={formatHash(nft.creatorAddress, 10, 8)}
                fullValue={nft.creatorAddress}
                isClickable={true}
                onClick={() =>
                  router.push(
                    PATH_ROUTER.ADDRESS_DETAIL(nft.creatorAddress || ""),
                  )
                }
              />
            )}

            <DetailRow label="Token ID" value={tokenId} />

            <DetailRow label="Token Standard" value={nft?.standard} />
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}

interface DetailRowProps {
  label: string
  value: string
  fullValue?: string
  isClickable?: boolean
  onClick?: () => void
}

function DetailRow({
  label,
  value,
  fullValue,
  isClickable = false,
  onClick,
}: DetailRowProps) {
  return (
    <div className="flex items-center justify-between py-2 border-b">
      <div className="flex items-center">
        <Info className="h-4 w-4 text-gray-400 mr-2" />
        <span className="text-gray-600">{label}</span>
      </div>
      <div className="flex items-center">
        <span
          className={`font-medium truncate max-w-[200px] ${
            isClickable
              ? "text-primary hover:underline cursor-pointer"
              : "text-gray-900"
          }`}
          title={fullValue}
          onClick={onClick}
        >
          {value}
        </span>
        {fullValue && (
          <div className="ml-2">
            <CopyTooltip content={fullValue} />
          </div>
        )}
      </div>
    </div>
  )
}
