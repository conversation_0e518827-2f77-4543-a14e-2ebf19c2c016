"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useGetAccountCrons } from "@/hooks/useTransactions";
import { useSearchParams } from "next/navigation";
import { CronsTable } from "./crons-table";
import { CronsTableLoading } from "./crons-table-loading";

interface AddressCronsProps {
  address: string;
}

export function AddressCrons({ address }: AddressCronsProps) {
  const searchParams = useSearchParams();
  const page = Number(searchParams.get("page")) || 1;
  const pageSize = Number(searchParams.get("pageSize") || "25");

  const { isLoading } = useGetAccountCrons(address, page, pageSize);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cron Jobs</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? <CronsTableLoading /> : <CronsTable address={address} />}
      </CardContent>
    </Card>
  );
}
