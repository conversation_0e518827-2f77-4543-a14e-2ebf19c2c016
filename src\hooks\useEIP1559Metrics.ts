import apiClient from "@/lib/api/apiClient"
import { useQuery } from "@tanstack/react-query"

export interface EIP1559MetricsResponse {
  timestamp: string
  totalBurnFee: string
  totalBurnFeeUsd: string
}

export interface Block {
  number: string
  timestamp: string
  baseFeePerGas: string
  gasUsed: string
}

export const useEIP1559Metrics = (startTime: number, endTime: number) => {
  return useQuery({
    queryKey: ["stats_dailytxs", startTime, endTime, "EIP1559_METRICS"],
    queryFn: async () => {
      const response = await apiClient.post<{ result: EIP1559MetricsResponse[] }>("api/", {
        jsonrpc: "2.0",
        method: "stats_dailytxs",
        params: [startTime, endTime, "EIP1559_METRICS"],
        id: 83,
      })
      return response?.result || []
    },
    staleTime: 5 * 60 * 1000,
  })
}

export const useRecentBlocks = (limit = 100) => {
  return useQuery({
    queryKey: ["eth_listBlocks", limit],
    queryFn: async () => {
      const response = await apiClient.post<{ result: Block[] }>("api/", {
        jsonrpc: "2.0",
        method: "eth_listBlocks",
        params: [1, limit],
        id: 84,
      })
      return response?.result || []
    },
    staleTime: 60 * 1000, // 1 minute
    refetchInterval: 60 * 1000, // Refresh every minute
  })
}
