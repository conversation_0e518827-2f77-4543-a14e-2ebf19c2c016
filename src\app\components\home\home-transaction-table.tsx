"use client"

import { TableSkeleton } from "@/components/table/table-skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { NATIVE_SYMBOL, ZERO_ADDRESS } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatEtherValue, formatHash, formatTimestamp, normalizeAddress } from "@/helpers/format"
import { useTransactionList } from "@/hooks/useTransactions"
import { FileIcon } from "lucide-react"
import Link from "next/link"

export function HomeTransactionsTable() {
  const { data: transactions, isLoading } = useTransactionList({
    page: 1,
    limit: 5,
    refetchInterval: 5000,
  })

  if (isLoading) {
    return <TableSkeleton columns={5} rows={5} />
  }

  return (
    <>
      {/* Desktop table */}
      <Table className="hidden md:table w-full overflow-x-auto rounded-lg shadow-sm">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[150px]">Txn Hash</TableHead>
            <TableHead>From</TableHead>
            <TableHead>To</TableHead>
            <TableHead className="text-right">Value</TableHead>
            <TableHead className="text-right w-[150px]">Time</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions?.result?.data.map((tx) => (
            <TableRow key={tx.hash} className="h-14">
              <TableCell>
                <Link
                  href={PATH_ROUTER.TRANSACTION_DETAIL(tx.hash)}
                  className="flex items-center gap-2 text-primary hover:underline"
                >
                  <div className="bg-secondary text-primary p-3 rounded-md">
                    <FileIcon className="h-5 w-5" />
                  </div>
                  {formatHash(tx.hash)}
                </Link>
              </TableCell>
              <TableCell>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Link
                        href={PATH_ROUTER.ADDRESS_DETAIL(
                          tx.from || ZERO_ADDRESS,
                        )}
                        className="text-primary hover:underline"
                      >
                        {formatHash(normalizeAddress(tx.from) || ZERO_ADDRESS)}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>{normalizeAddress(tx.from) || ZERO_ADDRESS}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
              <TableCell>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Link
                        href={PATH_ROUTER.ADDRESS_DETAIL(tx.to || ZERO_ADDRESS)}
                        className="text-primary hover:underline"
                      >
                        {formatHash(normalizeAddress(tx.to) || ZERO_ADDRESS)}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>{normalizeAddress(tx.to) || ZERO_ADDRESS}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
              <TableCell className="text-right">
                {formatEtherValue(tx.value)} {NATIVE_SYMBOL}
              </TableCell>
              <TableCell className="text-right text-muted-foreground">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      {formatTimestamp(tx?.timestamp)}
                    </TooltipTrigger>
                    <TooltipContent>
                      {new Date(tx?.timestamp).toLocaleString()}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Mobile card layout */}
      <div className="block md:hidden space-y-4">
        {transactions?.result?.data.map((tx) => (
          <div
            key={tx.hash}
            className="rounded-lg border p-4 flex flex-col gap-2 bg-white shadow-sm"
          >
            <div className="flex items-center gap-2">
              <div className="bg-secondary p-2 rounded-md text-primary">
                <FileIcon className="h-4 w-4" />
              </div>
              <Link
                href={PATH_ROUTER.TRANSACTION_DETAIL(tx.hash)}
                className="text-sm text-primary hover:underline break-all"
              >
                {formatHash(tx.hash)}
              </Link>
            </div>

            <div className="text-sm">
              <span className="text-muted-foreground">From:</span>{" "}
              <Link
                href={PATH_ROUTER.ADDRESS_DETAIL(tx.from || ZERO_ADDRESS)}
                className="text-primary hover:underline break-all"
              >
                {formatHash(tx.from || ZERO_ADDRESS)}
              </Link>
            </div>

            <div className="text-sm">
              <span className="text-muted-foreground">To:</span>{" "}
              <Link
                href={PATH_ROUTER.ADDRESS_DETAIL(tx.to || ZERO_ADDRESS)}
                className="text-primary hover:underline break-all"
              >
                {formatHash(tx.to || ZERO_ADDRESS)}
              </Link>
            </div>

            <div className="flex flex-col items-start text-sm">
              <div className="font-medium">
                {formatEtherValue(tx.value)} {NATIVE_SYMBOL}
              </div>
              <div className="text-muted-foreground text-xs">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      {formatTimestamp(tx?.timestamp)}
                    </TooltipTrigger>
                    <TooltipContent>
                      {new Date(tx?.timestamp).toLocaleString()}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  )
}
