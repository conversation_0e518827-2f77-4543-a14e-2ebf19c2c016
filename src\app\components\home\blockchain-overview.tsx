"use client"

import { DECIMAL } from "@/constants"
import { formatNumberWithNotation } from "@/helpers/format"
import { useGetChainStats } from "@/hooks/useTokens"
import { HeliosLogo } from "@public/assets"
import { ArrowUpRight } from "lucide-react"
import Image from "next/image"
import { TransactionChart } from "./transaction-chart"

export default function CryptoStats() {
  const { data: chainStats } = useGetChainStats(5000)

  return (
    <div className="card mb-4">
      <div className="border border-border rounded-lg p-6 text-card-foreground">
        <div className="grid grid-cols-1 lg:grid-cols-3 lg:divide-x lg:divide-border">
          {/* Left Column */}
          <div className="space-y-6 lg:pr-6 flex flex-col justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-secondary text-primary">
                <Image src={HeliosLogo} alt="Helios" width={24} height={24} />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">HELIOS PRICE</div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-bold text-foreground">$ {chainStats?.price.toLocaleString()}</span>
                  {/* <span className="text-xs text-emerald-500 flex items-center">
                    (+4.48%) <ArrowUpRight className="h-4 w-4" />
                  </span> */}
                </div>
              </div>
            </div>

            <hr className="border-border my-5" />

            {/* Market Cap */}
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-secondary text-primary">
                <svg className="w-6 h-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <div>
                <div className="text-xs text-muted-foreground">MARKET CAP</div>
                <div className="text-sm font-bold text-foreground">
                  ${chainStats?.marketCap ? formatNumberWithNotation(chainStats?.marketCap) : "0"}
                </div>
              </div>
            </div>
          </div>

          {/* Middle Column */}
          <div className="space-y-6 lg:px-6 flex flex-col justify-between">
            {/* Transactions */}
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-secondary text-primary">
                <svg className="w-6 h-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">TRANSACTIONS</p>
                <div className="flex items-center gap-2">
                  <div className="text-sm font-bold text-foreground">
                    {chainStats?.totalTransactions
                      ? formatNumberWithNotation(chainStats?.totalTransactions, DECIMAL.DEFAULT)
                      : "0"}
                  </div>
                  <div className="text-xs text-muted-foreground">({chainStats?.tps} TPS)</div>
                </div>
              </div>
            </div>

            <hr className="border-border my-5" />

            {/* Last Finalized Block */}
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-secondary text-primary">
                <svg className="w-6 h-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                  />
                </svg>
              </div>
              <div>
                <div className="text-xs text-muted-foreground">LAST FINALIZED BLOCK</div>
                <div className="text-sm font-bold text-foreground">{chainStats?.latestBlock}</div>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-between lg:pl-6 lg:h-full">
            <div className="text-xs text-muted-foreground">TRANSACTION HISTORY IN 14 DAYS</div>
            <TransactionChart transactionHistory={chainStats?.transactionHistory} price={chainStats?.price} />
          </div>
        </div>
      </div>
    </div>
  )
}
