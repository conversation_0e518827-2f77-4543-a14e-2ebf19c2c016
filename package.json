{"name": "helios-explorer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@reown/appkit-adapter-wagmi": "1.6.2", "@reown/appkit-siwe": "^1.6.8", "@tanstack/react-query": "^5.66.9", "@wagmi/core": "^2.16.4", "axios": "^1.7.9", "bignumber.js": "^9.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "ethers": "^6.13.5", "lucide-react": "^0.469.0", "next": "15.1.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-number-format": "^5.4.3", "react-toastify": "^11.0.3", "recharts": "^2.15.0", "sass": "^1.88.0", "socket.io-client": "4.7.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-clipboard-copy": "^0.2.0", "viem": "^2.23.3", "wagmi": "^2.14.11", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}