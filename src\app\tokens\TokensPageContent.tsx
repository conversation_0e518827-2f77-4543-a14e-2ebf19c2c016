"use client"

import Copy<PERSON>ooltip from "@/app/components/copy-tooltip"
import { CSVExportButton } from "@/components/csv-button-export"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { DEFAULT_TOKEN_IMAGE, ZERO_ADDRESS } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash } from "@/helpers/format"
import { useTokens } from "@/hooks/useTokens"
import { formatTokensForCSV } from "@/lib/utils/csv-export"
import { AlertCircle } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"

export default function TokensPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const pageSize = Number(searchParams.get("pageSize") || "25")
  const limit = pageSize

  const { data: tokensData, isLoading, isError, error } = useTokens(page, limit)

  const navigateToTokenDetail = (tokenAddress: string) => {
    router.push(PATH_ROUTER.TOKEN_DETAIL(tokenAddress))
  }

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">Error Loading Tokens</h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link href={`${PATH_ROUTER.TOKENS}?page=1`}>Try Again</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">HRC-20 Token List</h1>
        {!isLoading && tokensData && Array.isArray(tokensData) && tokensData.length > 0 && (
          <CSVExportButton
            data={tokensData}
            formatter={formatTokensForCSV}
            filename={`tokens-page-${page}.csv`}
          />
        )}
      </div>

      {isLoading ? (
        <LoadingSkeleton />
      ) : !tokensData ||
        !Array.isArray(tokensData) ||
        tokensData.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-6 text-center">
          <h3 className="text-lg font-semibold">No Tokens Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            There are no tokens available to display.
          </p>
        </div>
      ) : (
        <div className="card mb-4">
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[60px]">#</TableHead>
                  <TableHead>Token</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead className="text-right">Symbol</TableHead>
                  <TableHead className="text-right hidden md:table-cell">
                    Decimals
                  </TableHead>
                  <TableHead className="text-right hidden lg:table-cell">
                    Total Supply
                  </TableHead>
                  <TableHead className="text-right hidden 2xl:table-cell">
                    Holders Count
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tokensData.map((token, index) => {
                  const metadata = token.metadata
                  const tokenAddress =
                    metadata.contract_address || metadata.base
                  const logoUrl = metadata.logo
                    ? `https://testnet1-cdn.helioschainlabs.org/hash/${metadata.logo}`
                    : DEFAULT_TOKEN_IMAGE

                  const formattedTotalSupply =
                    token.total_supply && metadata.decimals
                      ? (
                          Number(token.total_supply) /
                          Math.pow(10, metadata.decimals)
                        ).toLocaleString(undefined, {
                          maximumFractionDigits: 4,
                        })
                      : "-"

                  return (
                    <TableRow key={index}>
                      <TableCell>{index + 1 + (page - 1) * limit}</TableCell>
                      <TableCell
                        className="flex items-center gap-2 cursor-pointer hover:text-primary"
                        onClick={() => navigateToTokenDetail(tokenAddress)}
                      >
                        <Image
                          src={logoUrl || "/placeholder.svg"}
                          alt={metadata.name || "token logo"}
                          width={24}
                          height={24}
                          className="rounded-full"
                        />
                        <div className="font-medium">
                          {metadata.name || "-"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div
                          className="flex items-center gap-2 cursor-pointer hover:text-primary"
                          onClick={() => navigateToTokenDetail(tokenAddress)}
                        >
                          <CopyTooltip content={tokenAddress || ZERO_ADDRESS} />
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger className="hover:underline">
                                {tokenAddress
                                  ? formatHash(tokenAddress, 6, 6)
                                  : formatHash(ZERO_ADDRESS, 6, 6)}
                              </TooltipTrigger>
                              <TooltipContent>
                                {tokenAddress || ZERO_ADDRESS}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {metadata.symbol || "-"}
                      </TableCell>
                      <TableCell className="text-right hidden md:table-cell">
                        {metadata.decimals ?? "-"}
                      </TableCell>
                      <TableCell className="text-right hidden lg:table-cell">
                        {formattedTotalSupply}
                      </TableCell>
                      <TableCell className="text-right hidden 2xl:table-cell">
                        {token.holdersCount || "-"}
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {!isLoading && tokensData && (
        <PaginationWithLinks
          page={page}
          pageSize={pageSize}
          totalCount={tokensData.length || 0}
          pageSearchParam="page"
          pageSizeSelectOptions={{
            pageSizeOptions: [10, 25, 50, 100],
          }}
        />
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="card mb-4">
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[60px]">#</TableHead>
              <TableHead>Token</TableHead>
              <TableHead>Address</TableHead>
              <TableHead className="text-right">Symbol</TableHead>
              <TableHead className="text-right hidden md:table-cell">
                Decimals
              </TableHead>
              <TableHead className="text-right hidden lg:table-cell">
                Total Supply
              </TableHead>
              <TableHead className="text-right hidden 2xl:table-cell">
                Holders Count
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-5 w-8" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-6 w-6 rounded-full" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-32" />
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-12 ml-auto" />
                </TableCell>
                <TableCell className="text-right hidden md:table-cell">
                  <Skeleton className="h-5 w-8 ml-auto" />
                </TableCell>
                <TableCell className="text-right hidden lg:table-cell">
                  <Skeleton className="h-5 w-20 ml-auto" />
                </TableCell>
                <TableCell className="text-right hidden 2xl:table-cell">
                  <Skeleton className="h-5 w-12 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
