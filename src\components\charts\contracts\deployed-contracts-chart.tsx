"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip } from "@/components/ui/chart"
import { Skeleton } from "@/components/ui/skeleton"
import { ChartTypeEnum, getTimeRanges, use<PERSON>hart } from "@/hooks/useChart"
import { Download, Info } from "lucide-react"
import { useMemo, useState } from "react"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, XAxis, YAxis } from "recharts"
import TimeRangeSelector, { TimeRangeOption } from "../time-range-seletor"
import UnifiedChartTooltip from "../unified-chart-tooltip"

const timeRangeOptions: TimeRangeOption[] = [
  { value: "1m", label: "1m" },
  { value: "3m", label: "3m" },
  { value: "6m", label: "6m" },
  { value: "1y", label: "1y" },
  { value: "all", label: "All" },
]

export default function DeployedContractsChart() {
  const [timeRange, setTimeRange] = useState<string>("1m")

  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d")
      case "3m":
        return getTimeRanges("90d")
      case "6m":
        return getTimeRanges("180d")
      case "1y":
        return getTimeRanges("1y")
      case "all":
        return getTimeRanges("all")
      default:
        return getTimeRanges("30d")
    }
  }, [timeRange])

  const { data, isLoading, error } = useChart(startTime, endTime, ChartTypeEnum.DAILY_DEPLOYED_CONTRACT)

  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    return data.map((item: any) => ({
      date: new Date(item.timestamp).toISOString().split("T")[0],
      value: item.deployedCount || 0,
      totalDeployed: item.totalDeployed || 0,
      deploymentGas: item.avgDeploymentGas || 0,
    }))
  }, [data])

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Daily Deployed Contracts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Daily Deployed Contracts</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <TimeRangeSelector timeRange={timeRange} onChange={setTimeRange} options={timeRangeOptions} />
        </CardHeader>
        <CardContent>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Skeleton className="h-[450px] w-full" />
              </div>
            ) : (
              <ChartContainer
                config={{
                  deployedContracts: {
                    label: "Deployed Contracts",
                    color: "#2ecc71",
                  },
                }}
                className="h-full w-full"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="date"
                      tickLine={false}
                      axisLine={false}
                      tickFormatter={(value) => {
                        const date = new Date(value)
                        return `${date.getMonth() + 1}/${date.getDate()}`
                      }}
                    />
                    <YAxis
                      tickLine={false}
                      axisLine={false}
                      tickFormatter={(value) => {
                        if (value >= 1000) {
                          return `${(value / 1000).toFixed(0)}k`
                        }
                        return value.toString()
                      }}
                      label={{
                        value: "Deployed Contracts",
                        angle: -90,
                        position: "insideLeft",
                        style: { textAnchor: "middle" },
                      }}
                    />
                    <ChartTooltip
                      content={<UnifiedChartTooltip chartType={ChartTypeEnum.DAILY_DEPLOYED_CONTRACT} />}
                      cursor={false}
                    />
                    <Area
                      type="monotone"
                      dataKey="value"
                      name="Deployed Contracts"
                      stroke="#2ecc71"
                      fill="#2ecc71"
                      fillOpacity={0.2}
                      strokeWidth={2}
                      activeDot={{ r: 6, fill: "#2ecc71" }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" className="flex items-center">
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Daily Deployed Contracts chart shows the number of smart contracts deployed daily on the Helios
            blockchain. This metric provides insights into development activity and platform adoption.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Click and drag in the plot area to zoom in. You can also use the time range buttons to view different time
            periods.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
