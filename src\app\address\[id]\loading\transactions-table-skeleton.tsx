import { Skeleton } from "@/components/ui/skeleton";

export const TransactionsTableSkeleton = () => (
  <div className="border rounded-lg p-4">
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-40" />
        <Skeleton className="h-8 w-32" />
      </div>
      {Array(5)
        .fill(0)
        .map((_, i) => (
          <div key={i} className="flex items-center justify-between py-2">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-5 w-24" />
          </div>
        ))}
    </div>
  </div>
);
