import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { NATIVE_DECIMALS, NATIVE_SYMBOL } from "@/constants"
import { formatHash, formatWeiToEther } from "@/helpers/format"
import Link from "next/link"
import { StatsTable } from "./stats-table"
interface TransactionStatsProps {
  data: any
}

export function TransactionStats({ data }: TransactionStatsProps) {
  if (!data || !data.entries) return <div>No transaction data available</div>

  const { entries } = data

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {entries?.topEthSenders && (
        <div className="card">
          <Card>
            <CardHeader>
              <CardTitle>Top {NATIVE_SYMBOL} Senders</CardTitle>
            </CardHeader>
            <CardContent>
              <StatsTable
                columns={[
                  { header: "Rank", accessor: "rank" },
                  {
                    header: "Address",
                    accessor: "address",
                    cell: (value) => (
                      <Link
                        href={`/address/${value}`}
                        className="text-blue-600 hover:underline"
                      >
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>{formatHash(value)}</TooltipTrigger>
                            <TooltipContent>{value}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Link>
                    ),
                  },
                  {
                    header: `Total ${NATIVE_SYMBOL}`,
                    accessor: "value",
                    align: "right",
                    cell: (value) =>
                      formatWeiToEther(value, 6, NATIVE_DECIMALS),
                  },
                  {
                    header: "Percentage",
                    accessor: "percentage",
                    align: "right",
                    cell: (value) => `${Number.parseFloat(value).toFixed(2)}%`,
                  },
                ]}
                data={entries?.topEthSenders}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {entries.topEthReceivers && (
        <div className="card">
          <Card>
            <CardHeader>
              <CardTitle>Top {NATIVE_SYMBOL} Receivers</CardTitle>
            </CardHeader>
            <CardContent>
              <StatsTable
                columns={[
                  { header: "Rank", accessor: "rank" },
                  {
                    header: "Address",
                    accessor: "address",
                    cell: (value) => (
                      <Link
                        href={`/address/${value}`}
                        className="text-blue-600 hover:underline"
                      >
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>{formatHash(value)}</TooltipTrigger>
                            <TooltipContent>{value}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Link>
                    ),
                  },
                  {
                    header: `Total ${NATIVE_SYMBOL}`,
                    accessor: "value",
                    align: "right",
                    cell: (value) =>
                      formatWeiToEther(value, 6, NATIVE_DECIMALS),
                  },
                  {
                    header: "Percentage",
                    accessor: "percentage",
                    align: "right",
                    cell: (value) => `${Number.parseFloat(value).toFixed(2)}%`,
                  },
                ]}
                data={entries.topEthReceivers}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {entries.topTxnSenders && (
        <div className="card">
          <Card>
            <CardHeader>
              <CardTitle>Top Transaction Senders</CardTitle>
            </CardHeader>
            <CardContent>
              <StatsTable
                columns={[
                  { header: "Rank", accessor: "rank" },
                  {
                    header: "Address",
                    accessor: "address",
                    cell: (value) => (
                      <Link
                        href={`/address/${value}`}
                        className="text-blue-600 hover:underline"
                      >
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>{formatHash(value)}</TooltipTrigger>
                            <TooltipContent>{value}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Link>
                    ),
                  },
                  {
                    header: "Count",
                    accessor: "count",
                    align: "right",
                    cell: (value) => value.toLocaleString(),
                  },
                  {
                    header: "Percentage",
                    accessor: "percentage",
                    align: "right",
                    cell: (value) => `${Number.parseFloat(value).toFixed(2)}%`,
                  },
                ]}
                data={entries.topTxnSenders}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {entries.topTxnReceivers && (
        <div className="card">
          <Card>
            <CardHeader>
              <CardTitle>Top Transaction Receivers</CardTitle>
            </CardHeader>
            <CardContent>
              <StatsTable
                columns={[
                  { header: "Rank", accessor: "rank" },
                  {
                    header: "Address",
                    accessor: "address",
                    cell: (value) => (
                      <Link
                        href={`/address/${value}`}
                        className="text-blue-600 hover:underline"
                      >
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>{formatHash(value)}</TooltipTrigger>
                            <TooltipContent>{value}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Link>
                    ),
                  },
                  {
                    header: "Count",
                    accessor: "count",
                    align: "right",
                    cell: (value) => value.toLocaleString(),
                  },
                  {
                    header: "Percentage",
                    accessor: "percentage",
                    align: "right",
                    cell: (value) => `${Number.parseFloat(value).toFixed(2)}%`,
                  },
                ]}
                data={entries.topTxnReceivers}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
