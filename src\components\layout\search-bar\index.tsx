"use client"

import { Input } from "@/components/ui/input"
import { PATH_ROUTER } from "@/constants/routers"
import { useSearch } from "@/hooks/useSearch"
import clsx from "clsx"
import { Search } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useRef, useState } from "react"
import { Result, ResultItem } from "./result"
import s from "./search-bar.module.scss"

export default function SearchBar() {
  const [value, setValue] = useState("")
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const { data, isLoading } = useSearch(value, "0x1", "0xF")

  const isEmptyResponse =
    !data?.data?.accounts?.length &&
    !data?.data?.blocks?.length &&
    !data?.data?.tokens?.length &&
    !data?.data?.transactions?.length

  const handleItemClick = (path: string) => {
    router.push(path)
    setValue("")
    setIsOpen(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    setIsOpen(newValue.trim().length > 0)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <>
      <div className={s.search} ref={dropdownRef}>
        <div className={s.form}>
          <Search className={s.icon} />
          <Input
            ref={inputRef}
            type="text"
            placeholder="Search by Address / Txn Hash / Block / Token / Domain Name"
            className={s.input}
            value={value}
            onChange={handleInputChange}
            onFocus={() => setIsOpen(true)}
          />
        </div>
        {isOpen && (
          <div className={s.results}>
            {isLoading && (
              <div className="py-6 text-center">
                <div className="flex items-center justify-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                  <span>Searching...</span>
                </div>
              </div>
            )}
            {!isLoading && isEmptyResponse && value && (
              <Result title="Address">
                <ResultItem
                  onClick={() =>
                    handleItemClick(PATH_ROUTER.ADDRESS_DETAIL(value))
                  }
                >
                  {value}
                </ResultItem>
              </Result>
            )}
            {!isEmptyResponse && (
              <>
                {data?.data?.tokens?.length > 0 && (
                  <Result title="Tokens (HRC-20)">
                    {data.data.tokens.map((token) => (
                      <ResultItem
                        key={token.address}
                        onClick={() =>
                          handleItemClick(
                            PATH_ROUTER.TOKEN_DETAIL(token.address),
                          )
                        }
                      >
                        <div className="flex flex-col gap-1">
                          <span className="font-medium">{token.name}</span>
                          <small>{token.address}</small>
                        </div>
                      </ResultItem>
                    ))}
                  </Result>
                )}
                {data?.data?.accounts?.length > 0 && (
                  <Result title="Addresses">
                    {data.data.accounts.map((account) => (
                      <ResultItem
                        key={account.address}
                        onClick={() =>
                          handleItemClick(
                            PATH_ROUTER.ADDRESS_DETAIL(account.address),
                          )
                        }
                      >
                        <div className="flex flex-col gap-1">
                          <span className="font-mono">{account.address}</span>
                          {account.isContract && (
                            <span className={s.contract}>Contract</span>
                          )}
                          <small>{account.txCount === 1 ? 'Transaction' : 'Transactions'}: {account.txCount}</small>
                        </div>
                      </ResultItem>
                    ))}
                  </Result>
                )}
                {data?.data?.blocks?.length > 0 && (
                  <Result title="Blocks">
                    {data.data.blocks.map((block) => (
                      <ResultItem
                        key={block.number}
                        onClick={() =>
                          handleItemClick(
                            `${PATH_ROUTER.BLOCK_DETAIL(block.number)}`,
                          )
                        }
                      >
                        <div className="flex flex-col gap-1">
                          <span className="font-medium">
                            Block {block.number}
                          </span>
                          <small>Timestamp: {block.timestamp}</small>
                        </div>
                      </ResultItem>
                    ))}
                  </Result>
                )}
                {data?.data?.transactions?.length > 0 && (
                  <Result title="Transactions">
                    {data.data.transactions.map((txn) => (
                      <ResultItem
                        key={txn.hash}
                        onClick={() =>
                          handleItemClick(
                            `${PATH_ROUTER.TRANSACTION_DETAIL(txn.hash)}`,
                          )
                        }
                      >
                        <div className="flex flex-col gap-1">
                          <span className="font-mono">{txn.hash}</span>
                          <small>
                            From: {txn.from} → To: {txn.to}
                          </small>
                        </div>
                      </ResultItem>
                    ))}
                  </Result>
                )}
              </>
            )}
          </div>
        )}
        <svg
          viewBox="0 0 306 394"
          className={clsx(s.liner, s.linerLeft)}
          data-line
        >
          <path d="M2 113C25.3333 168.667 118.8 280 306 280M306 280C136.4 280 32 93.6667 1 1M306 280C118.8 280 24.6667 303.333 1 315M1 393C20.3333 355.667 108.2 281 305 281M1 209C41.6667 233 159.2 281 304 281" />
        </svg>
        <svg
          viewBox="0 0 306 394"
          className={clsx(s.liner, s.linerRight)}
          data-line
        >
          <path d="M2 113C25.3333 168.667 118.8 280 306 280M306 280C136.4 280 32 93.6667 1 1M306 280C118.8 280 24.6667 303.333 1 315M1 393C20.3333 355.667 108.2 281 305 281M1 209C41.6667 233 159.2 281 304 281" />
        </svg>
      </div>
    </>
  )
}
