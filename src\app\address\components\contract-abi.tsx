"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface ContractABIProps {
  abi: string
}

export function ContractABI({ abi }: ContractABIProps) {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = () => {
    navigator.clipboard.writeText(abi)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-base">Contract ABI</CardTitle>
        <Button variant="outline" size="sm" onClick={copyToClipboard} className="h-8">
          {copied ? (
            <>
              <Check className="mr-2 h-3.5 w-3.5" />
              Copied
            </>
          ) : (
            <>
              <Copy className="mr-2 h-3.5 w-3.5" />
              Copy ABI
            </>
          )}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="max-h-[300px] overflow-auto rounded-md bg-muted p-4">
          <pre className="text-xs">
            <code>{abi}</code>
          </pre>
        </div>
      </CardContent>
    </Card>
  )
}

