"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTit<PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search } from "lucide-react"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, XAxis, YAxis } from "recharts"

interface BaseFeeProps {
  isLoading: boolean
  blocksChartData: Array<{
    block: number
    timestamp: string
    baseFee: number
    gasUsed: number
    time: string
    date: string
  }>
  filteredBlocksData: Array<{
    block: number
    timestamp: string
    baseFee: number
    gasUsed: number
    time: string
    date: string
  }>
  searchBlock: string
  setSearchBlock: (value: string) => void
}

export default function BaseFeeSection({
  isLoading,
  blocksChartData,
  filteredBlocksData,
  searchBlock,
  setSearchBlock,
}: BaseFeeProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 text-xs">Ξ</span>
            </div>
            <CardTitle className="text-base">Base Fee Per Block</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[250px]">
            {isLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Skeleton className="h-[200px] w-full" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={blocksChartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="time" tickLine={false} axisLine={false} />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => `${value.toFixed(2)}`}
                    domain={["auto", "auto"]}
                  />
                  <Area
                    type="monotone"
                    dataKey="baseFee"
                    name="Base Fee (Gwei)"
                    stroke="#3498db"
                    fill="#3498db"
                    fillOpacity={0.1}
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 text-xs">Ξ</span>
            </div>
            <CardTitle className="text-base">Base Fee Per Block</CardTitle>
          </div>
          <div className="relative w-[200px]">
            <Input
              placeholder="Search Block..."
              value={searchBlock}
              onChange={(e) => setSearchBlock(e.target.value)}
              className="pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="rounded-md border max-h-[250px] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Block</TableHead>
                  <TableHead>Date Time</TableHead>
                  <TableHead className="text-right">Base Fee</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-12 ml-auto" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredBlocksData.length > 0 ? (
                  filteredBlocksData.map((block, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium text-blue-500">{block.block}</TableCell>
                      <TableCell>{`${block.date} ${block.time}`}</TableCell>
                      <TableCell className="text-right">{block.baseFee.toFixed(2)}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4 text-muted-foreground">
                      No blocks found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
