import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { NATIVE_SYMBOL, ZERO_ADDRESS } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatEtherValue, formatHash, formatTimestamp, normalizeAddress } from "@/helpers/format"
import { detectMethod } from "@/helpers/transaction"
import { formatTransactionsForCSV } from "@/lib/utils/csv-export"
import type { Transaction } from "@/types/transactions"
import { formatUnits } from "ethers"
import { FileIcon } from "lucide-react"
import Link from "next/link"
import { CSVExportButton } from "./csv-button-export"
import { renderAddressWithTag } from "@/helpers/nameTags"

interface TransactionsTableProps {
  transactions: Transaction[]
  isLoading?: boolean
  showExport?: boolean
  exportFilename?: string
  mapNameTag?: { [address: string]: string }
  isShowTagName?: boolean
}

export function TransactionsTable({
  transactions,
  showExport = false,
  exportFilename = "transactions.csv",
  mapNameTag,
  isShowTagName = true,
}: TransactionsTableProps) {
  const getNameTag = (address: string): string | undefined => {
    if (!mapNameTag || !address || !isShowTagName) return undefined
    return mapNameTag[address.toLowerCase()] || mapNameTag[address]
  }

  return (
    <div className="overflow-x-auto">
      {showExport && (
        <div className="flex justify-end mb-4">
          <CSVExportButton
            data={transactions}
            formatter={formatTransactionsForCSV}
            filename={exportFilename}
          />
        </div>
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Txn Hash</TableHead>
            <TableHead>Method</TableHead>
            <TableHead>Block</TableHead>
            <TableHead>Age</TableHead>
            <TableHead>From</TableHead>
            <TableHead>To</TableHead>
            <TableHead className="text-right">Value</TableHead>
            <TableHead className="text-right hidden md:table-cell">
              Txn Fee
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions?.map((tx) => {
            const method = detectMethod(tx)
            const formattedTransactionFee =
              tx?.gasPrice && tx?.cumulativeGasUsed
                ? formatEtherValue(
                    (
                      Number.parseFloat(tx?.gasPrice) *
                      Number.parseFloat(tx?.cumulativeGasUsed)
                    ).toFixed(0),
                  )
                : "N/A"

            const fromNameTag = getNameTag(tx.from || ZERO_ADDRESS)
            const toNameTag = getNameTag(tx.to || ZERO_ADDRESS)

            return (
              <TableRow key={tx.hash}>
                <TableCell>
                  <Link
                    href={`${PATH_ROUTER.TRANSACTION_DETAIL(tx.hash)}`}
                    className="flex items-center gap-2 text-blue-700 hover:underline"
                  >
                    <FileIcon className="h-4 w-4" />
                    {formatHash(tx.hash)}
                  </Link>
                </TableCell>
                <TableCell>
                  <span className="px-2 py-1 bg-muted rounded text-xs">
                    {method.name}
                  </span>
                </TableCell>
                <TableCell>
                  <Link
                    href={`${PATH_ROUTER.BLOCK_DETAIL(tx.blockNumber)}`}
                    className="text-blue-700 hover:underline"
                  >
                    {tx.blockNumber}
                  </Link>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {formatTimestamp(tx?.timestamp)}
                      </TooltipTrigger>
                      <TooltipContent>
                        {new Date(tx?.timestamp).toLocaleString()}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Link
                          href={`${PATH_ROUTER.ADDRESS_DETAIL(
                            tx.from || ZERO_ADDRESS,
                          )}`}
                          className="text-blue-700 hover:underline"
                        >
                          {renderAddressWithTag(normalizeAddress(tx.from), mapNameTag, true)}
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="flex flex-col items-center">
                          {fromNameTag && (
                            <div className="font-medium">{fromNameTag}</div>
                          )}
                          <div>{normalizeAddress(tx.from) || ZERO_ADDRESS}</div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Link
                          href={`/address/${tx.to || ZERO_ADDRESS}`}
                          className="text-blue-700 hover:underline"
                        >
                          {renderAddressWithTag(normalizeAddress(tx.to), mapNameTag, true)}
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="flex flex-col items-center">
                          {toNameTag && (
                            <div className="font-medium">{toNameTag}</div>
                          )}
                          <div>{normalizeAddress(tx.to) || ZERO_ADDRESS}</div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className="text-right">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {formatEtherValue(tx.value)} {NATIVE_SYMBOL}
                      </TooltipTrigger>
                      <TooltipContent>
                        {formatUnits(tx.value || "0", "ether")}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className="text-right hidden md:table-cell">
                  {formattedTransactionFee}
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
