"use client";

import { NATIVE_SYMBOL } from "@/constants";
import { formatNumberWithNotation } from "@/helpers/format";
import {
  type ChartType,
  ChartTypeEnum,
  getLabelForChartType,
} from "@/hooks/useChart";
import BigNumber from "bignumber.js";
import { format } from "date-fns";

interface EnhancedTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
  chartType: ChartType;
  data?: any;
}

export default function EnhancedTooltip({
  active,
  payload,
  label,
  chartType,
  data,
}: EnhancedTooltipProps) {
  if (!active || !payload || !payload.length || !label) {
    return null;
  }

  const dataPoint = payload[0].payload;
  const date = new Date(label);
  const formattedDate = format(date, "EEEE, MMMM d, yyyy");
  const chartLabel = getLabelForChartType(chartType);
  const value = payload[0].value;

  // Format value based on chart type
  const formattedValue = (() => {
    switch (chartType) {
      case ChartTypeEnum.AVG_BLOCK_TIME:
        return `${value.toFixed(2)}`;
      case ChartTypeEnum.AVG_BLOCK_SIZE:
        return typeof value === "string" ? value : value.toLocaleString();
      case ChartTypeEnum.DAILY_TXN:
        return value.toLocaleString();
      case ChartTypeEnum.ERC20_TOKEN_TRANSFER:
        return value.toLocaleString();
      case ChartTypeEnum.AVG_GAS_PRICE:
        return `${value.toFixed(2)} Gwei`;
      case ChartTypeEnum.BLOCK_COUNT_AND_REWARD:
        return value.toLocaleString();
      default:
        return value.toLocaleString();
    }
  })();

  const formatBlockRewards = (rewardsValue: string): string => {
    try {
      const bn = new BigNumber(rewardsValue);
      const ethValue = bn.dividedBy(new BigNumber(10).pow(18));
      return ethValue.toFixed(4);
    } catch (error) {
      console.error("Error formatting block rewards:", error);

      if (typeof formatNumberWithNotation === "function") {
        const formatted = formatNumberWithNotation(rewardsValue, 4);
        return formatted || "0";
      }

      return "0";
    }
  };
  console.log('chartType',chartType)
  return (
    <div className="bg-background/95 border border-border rounded-md shadow-md p-3 text-sm">
      <div className="font-medium text-foreground mb-1">{formattedDate}</div>
      <div className="text-primary font-medium">
        [ {chartLabel}: {formattedValue} ]
      </div>

      {(chartType === ChartTypeEnum.DAILY_TXN ||
        chartType === ChartTypeEnum.ERC20_TOKEN_TRANSFER) &&
        dataPoint && (
          <div className="mt-2 space-y-1 text-xs text-muted-foreground">
            {dataPoint.avgBlockTime && (
              <div>Avg Block Time: {dataPoint.avgBlockTime.toFixed(2)}</div>
            )}
            {dataPoint.avgBlockSize && (
              <div>
                Avg Block Size:{" "}
                {Number(dataPoint.avgBlockSize).toLocaleString()}
              </div>
            )}
            {dataPoint.blockCount && (
              <div>
                Total Block Count: {dataPoint.blockCount.toLocaleString()}
              </div>
            )}
            {dataPoint.newAddressSeen !== undefined && (
              <div>
                New Address Seen: {dataPoint.newAddressSeen.toLocaleString()}
              </div>
            )}
          </div>
        )}

      {chartType === ChartTypeEnum.BLOCK_COUNT_AND_REWARD && dataPoint && (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          <div>Block Count: {dataPoint.blockCount.toLocaleString()}</div>
          {dataPoint.blockRewards && (
            <div>
              Block Rewards: {formatBlockRewards(dataPoint.blockRewards)}{" "}
              {NATIVE_SYMBOL}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
