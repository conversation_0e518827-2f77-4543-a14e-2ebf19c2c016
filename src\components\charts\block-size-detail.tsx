"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChartType,
  getDataFieldForChartType,
  getTimeRanges,
  useChart,
} from "@/hooks/useChart";
import { Download, Info } from "lucide-react";
import { useMemo, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import EnhancedTooltip from "./enhanced-tooltip";

interface BlockSizeDetailProps {
  chartType: ChartType;
  color: string;
}

export default function BlockSizeDetail({
  chartType,
  color,
}: BlockSizeDetailProps) {
  const [timeRange, setTimeRange] = useState<"1m" | "6m" | "1y" | "all">("1m");
  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d");
      case "6m":
        return getTimeRanges("180d");
      case "1y":
        return getTimeRanges("1y");
      case "all":
        return getTimeRanges("all");
      default:
        return getTimeRanges("30d");
    }
  }, [timeRange]);

  const { data, isLoading, error } = useChart(startTime, endTime, chartType);
  const dataField = getDataFieldForChartType(chartType);

  // Format data for charts
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.map((item: any) => {
      const value = item[dataField];
      return {
        date: new Date(item.timestamp).toISOString().split("T")[0],
        value: typeof value === "string" ? Number.parseFloat(value) : value,
        ...item,
      };
    });
  }, [data, dataField]);

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Average Block Size Chart</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Helios Average Block Size Chart</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex space-x-1">
              <Button
                variant={timeRange === "1m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1m")}
              >
                1m
              </Button>
              <Button
                variant={timeRange === "6m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("6m")}
              >
                6m
              </Button>
              <Button
                variant={timeRange === "1y" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1y")}
              >
                1y
              </Button>
              <Button
                variant={timeRange === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("all")}
              >
                All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="w-full h-full flex items-center justify-center">
                <Skeleton className="w-full h-[450px]" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getMonth() + 1}/${date.getDate()}`;
                    }}
                  />
                  <YAxis
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                    label={{
                      value: "Block Size in Bytes",
                      angle: -90,
                      position: "insideLeft",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <Tooltip
                    content={<EnhancedTooltip chartType={chartType} />}
                  />
                  <Bar dataKey="value" fill={color} />
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" className="flex items-center">
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Helios Average Block Size Chart indicates the historical average
            block size in bytes of the Helios blockchain. Block size is an
            important metric for blockchain performance and scalability, as it
            determines how many transactions can be included in each block.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Click and drag in the plot area to zoom in. You can also use the
            time range buttons to view different time periods.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
