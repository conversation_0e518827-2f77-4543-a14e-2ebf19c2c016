"use client"

import { CSVExportButton } from "@/components/csv-button-export"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { PATH_ROUTER } from "@/constants/routers"
import { formatWeiToEther } from "@/helpers/format"
import { useGetTopTokenHolders } from "@/hooks/useTokens"
import { formatTokenHoldersForCSV } from "@/lib/utils/csv-export"
import type { TokenResult } from "@/types/tokens"
import { AlertCircle, Download, Filter } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { numericFormatter } from "react-number-format"

interface TokenHoldersProps {
  tokenAddress: string
  tokenDetail: TokenResult | undefined
}

export function TokenHolders({ tokenAddress, tokenDetail }: TokenHoldersProps) {
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const limit = 25
  const router = useRouter()

  const {
    data: holdersResponse,
    isLoading,
    isError,
    error,
  } = useGetTopTokenHolders(tokenAddress, page.toString(), limit.toString())

  const holders = holdersResponse?.data || []
  const totalHolders = holdersResponse?.metadata?.total || 0

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Token Holders
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link href={`${PATH_ROUTER.TOKEN_DETAIL(tokenAddress)}?page=1`}>
                Try Again
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <p className="text-sm text-muted-foreground">
          {!isLoading ? (
            <>
              More than{" "}
              {numericFormatter(`${totalHolders}`, {
                thousandSeparator: true,
                decimalScale: 0,
              })}{" "}
              {totalHolders === 1 ? 'holder' : 'holders'} found
            </>
          ) : (
            <Skeleton className="h-5 w-40" />
          )}
        </p>
        <div className="flex gap-2">
          <CSVExportButton
            data={holders}
            formatter={formatTokenHoldersForCSV}
            filename={`token-${tokenAddress}-holders-page-${page}.csv`}
            disabled={isLoading || holders.length === 0}
          />
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="rounded-md border">
          <LoadingHoldersTable />
        </div>
      ) : holders.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg">
          <h3 className="text-lg font-semibold">No Holders Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            There are no token holders available to display.
          </p>
        </div>
      ) : (
        <div className="rounded-md border bg-white">
          <Table>
            <TableHeader>
              <TableRow className="border-b bg-gray-50/50">
                <TableHead className="w-[80px] text-left font-medium">
                  Rank
                </TableHead>
                <TableHead className="min-w-[400px] text-left font-medium">
                  Address
                </TableHead>
                <TableHead className="w-[300px] text-left font-medium">
                  Quantity
                </TableHead>
                <TableHead className="w-[200px] text-right font-medium">
                  Percentage
                </TableHead>
                <TableHead className="w-[200px] text-right font-medium">
                  Txn Count
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {holders.map((holder, index) => (
                <TableRow key={holder.id} className="hover:bg-gray-50/50">
                  <TableCell className="w-[80px] py-4 text-left font-medium text-gray-900">
                    {(page - 1) * limit + index + 1}
                  </TableCell>
                  <TableCell className="min-w-[400px] py-4">
                    <span
                      className="font-mono text-sm text-blue-600 hover:text-blue-800 cursor-pointer transition-colors"
                      onClick={() => {
                        router.push(PATH_ROUTER.ADDRESS_DETAIL(holder.address))
                      }}
                    >
                      {holder.address}
                    </span>
                  </TableCell>
                  <TableCell className="w-[300px] py-4 text-left font-mono text-sm">
                    {tokenDetail
                      ? numericFormatter(
                          formatWeiToEther(
                            holder?.balance,
                            tokenDetail.decimals,
                          ),
                          {
                            thousandSeparator: true,
                            decimalScale: 6,
                            fixedDecimalScale: false,
                          },
                        )
                      : numericFormatter(formatWeiToEther(holder?.balance), {
                          thousandSeparator: true,
                          decimalScale: 6,
                          fixedDecimalScale: false,
                        })}
                  </TableCell>
                  <TableCell className="w-[200px] py-4">
                    <div className="flex flex-col items-start gap-1">
                      <span className="text-xs font-medium text-blue-600">
                        {numericFormatter(holder.percentage, {
                          thousandSeparator: true,
                          decimalScale: 4,
                          fixedDecimalScale: false,
                          suffix: "%",
                        })}
                      </span>
                      <Progress
                        value={Math.min(
                          Number.parseFloat(holder.percentage || "0"),
                          100,
                        )}
                        className="h-1 w-full max-w-[180px] bg-gray-200"
                        indicatorClassName="bg-blue-500"
                      />
                    </div>
                  </TableCell>
                  <TableCell className="w-[200px] py-4 text-right font-mono text-sm">
                    {holder.txnCount.toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {!isLoading && (
        <PaginationWithLinks
          page={page}
          pageSize={limit}
          totalCount={totalHolders}
          pageSearchParam="page"
          baseUrl={PATH_ROUTER.TOKEN_DETAIL(tokenAddress)}
        />
      )}
    </div>
  )
}

function LoadingHoldersTable() {
  return (
    <div className="w-full overflow-auto bg-white">
      <table className="w-full caption-bottom text-sm">
        <thead className="[&_tr]:border-b">
          <tr className="border-b transition-colors bg-gray-50/50">
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-[80px]">
              Rank
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[400px]">
              Address
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground w-[300px]">
              Quantity
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground w-[200px]">
              Percentage
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground w-[200px]">
              Txn Count
            </th>
          </tr>
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {Array.from({ length: 10 }).map((_, index) => (
            <tr
              key={index}
              className="border-b transition-colors hover:bg-gray-50/50"
            >
              <td className="p-4 align-middle w-[80px]">
                <Skeleton className="h-5 w-8" />
              </td>
              <td className="p-4 align-middle min-w-[400px]">
                <Skeleton className="h-5 w-80" />
              </td>
              <td className="p-4 align-middle text-left w-[200px]">
                <Skeleton className="h-5 w-24 ml-auto" />
              </td>
              <td className="p-4 align-middle w-[300px]">
                <div className="flex flex-col items-start gap-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-1 w-[180px]" />
                </div>
              </td>
              <td className="p-4 align-middle text-right w-[200px]">
                <Skeleton className="h-5 w-24 ml-auto" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
