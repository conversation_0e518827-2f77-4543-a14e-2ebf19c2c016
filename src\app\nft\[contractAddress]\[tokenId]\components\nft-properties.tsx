"use client"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, Info } from "lucide-react"
import { useState } from "react"

interface NFTPropertiesProps {
  attributes?: Array<{
    trait_type: string
    value: string
  }>
}

export function NFTProperties({ attributes = [] }: NFTPropertiesProps) {
  const [isOpen, setIsOpen] = useState(true)

  if (attributes?.length === 0) {
    return null
  }

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="border rounded-lg overflow-hidden"
    >
      <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gray-50 hover:bg-gray-100">
        <div className="flex items-center">
          <Info className="h-4 w-4 mr-2" />
          <span className="font-medium">Properties ({attributes?.length})</span>
        </div>
        {isOpen ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="p-4">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {attributes?.map((attribute, index) => (
              <div
                key={index}
                className="border rounded-lg p-3 text-center hover:bg-gray-50"
              >
                <div className="text-xs text-gray-500 uppercase mb-1">
                  {attribute.trait_type}
                </div>
                <div className="font-medium text-sm">{attribute.value}</div>
              </div>
            ))}
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
