import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Info } from "lucide-react";

export const ContractTabSkeleton = () => (
  <div className="space-y-6">
    <div className="flex flex-col gap-4">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription className="flex items-center text-sm">
          Descriptions included below are taken from the contract source code.
        </AlertDescription>
      </Alert>

      <div className="flex items-center gap-2">
        <Skeleton className="h-6 w-40" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">
            Contract Name:
          </p>
          <Skeleton className="h-5 w-40" />
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">
            Optimization Enabled:
          </p>
          <Skeleton className="h-5 w-32" />
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">
            Compiler Version:
          </p>
          <Skeleton className="h-5 w-48" />
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">
            Implementation Address:
          </p>
          <Skeleton className="h-5 w-56" />
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">
            Other Settings:
          </p>
          <Skeleton className="h-5 w-36" />
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">
            Proxy Pattern:
          </p>
          <Skeleton className="h-5 w-40" />
        </div>
      </div>
    </div>

    <div>
      <div className="flex border-b">
        <Skeleton className="h-10 w-24 m-1" />
        <Skeleton className="h-10 w-28 m-1" />
        <Skeleton className="h-10 w-28 m-1" />
      </div>
      <div className="p-4 space-y-4">
        <Skeleton className="h-8 w-full" />
        <div className="space-y-2">
          {Array(10)
            .fill(0)
            .map((_, i) => (
              <Skeleton key={i} className="h-4 w-full" />
            ))}
        </div>
      </div>
    </div>
  </div>
);
