"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/charts/active-address-chart";
import ActiveERC20Address<PERSON>hart from "@/components/charts/active-erc20-address-chart";
import TransactionFee<PERSON>hart from "@/components/charts/avg-transaction-fee";
import {
  chartConfig,
  getYAxisFormatter,
} from "@/components/charts/chart-config";
import ChartDetail from "@/components/charts/chart-detail";
import DeployedContractsChart from "@/components/charts/contracts/deployed-contracts-chart";
import VerifiedContractsChart from "@/components/charts/contracts/verified-contracts-chart";
import DailyBurntChart from "@/components/charts/daily-burn";
import ContractStatsChart from "@/components/charts/dashboard/contract-stats-chart";
import EIP1559MetricsChart from "@/components/charts/dashboard/eip1559-metrics-chart";
import PendingTransactionsChart from "@/components/charts/dashboard/pending-transactions-chart";
import NetworkTransactionFee<PERSON>hart from "@/components/charts/network/network-transaction-fee";
import NodeSync<PERSON><PERSON> from "@/components/charts/node-sync-chart";
import PriceDetail from "@/components/charts/price-detail";
import SupplyDistribution from "@/components/charts/supply-distribution";
import DexActivityChart from "@/components/charts/top-statistic/dex-activity";
import TransactionsDetail from "@/components/charts/transactions-detail";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

export default function ChartPage() {
  const params = useParams();
  const chartId = params.chartId as string;

  const chart = chartConfig[chartId] || {
    title: "Chart",
    chartType: "DAILY_TXN",
    visualType: "area",
    color: "#3498db",
  };

  // Render the appropriate detail component based on chart type
  const renderDetailComponent = () => {
    console.log("chart.detailComponent", chart.detailComponent);
    switch (chart.detailComponent) {
      case "supply":
        return <SupplyDistribution />;
      case "transactions":
        return (
          <TransactionsDetail chartType={chart.chartType} color={chart.color} />
        );
      case "price":
        return <PriceDetail chartType={chart.chartType} color={chart.color} />;
      case "node-sync":
        return <NodeSyncChart />;
      case "active-address":
        return <ActiveAddressChart />;
      case "active-erc20-address":
        return <ActiveERC20AddressChart />;
      case "transaction-fee":
        return <TransactionFeeChart />;
      case "burnt-chart":
        return <DailyBurntChart />;
      case "pending-transactions":
        return <PendingTransactionsChart />;
      case "eip1559-metrics":
        return <EIP1559MetricsChart />;
      case "contract-stats":
        return <ContractStatsChart />;
      case "dextracker":
        return <DexActivityChart />;
      case "network-transaction-fee":
        return <NetworkTransactionFeeChart />;
      default:
        if (chartId === "verified-contracts") {
          return <VerifiedContractsChart />;
        }
        if (chartId === "deployed-contracts") {
          return <DeployedContractsChart />;
        }

        return (
          <ChartDetail
            title={chart.title}
            chartType={chart.chartType}
            visualType={chart.visualType === "pie" ? "area" : chart.visualType}
            color={chart.color}
            description={chart.description}
            yAxisLabel={chart.yAxisLabel || "Value"}
            yAxisFormatter={getYAxisFormatter(chart.chartType)}
          />
        );
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/charts">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Charts
              </Link>
            </Button>
          </div>

          <div className="mb-6">
            <h1 className="text-2xl font-bold">{chart.title}</h1>
            {chart.description && (
              <p className="mt-2 text-muted-foreground">{chart.description}</p>
            )}
          </div>

          {renderDetailComponent()}
        </div>
      </main>
    </div>
  );
}
