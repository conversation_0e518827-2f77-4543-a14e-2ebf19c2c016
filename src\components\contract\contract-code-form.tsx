"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { Check, ChevronDown, InfoIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { normalizeAddress } from "@/helpers/format";
import { useGetCode } from "@/hooks/useVerifyContract";
import {
  contractCodeSchema,
  type ContractCodeFormValues,
} from "@/lib/validations/contract-schema";
import { CompilerType } from "@/types/verify-contract";

interface ContractCodeFormProps {
  onSubmit: (data: ContractCodeFormValues) => void
  onReset: () => void
  contractDetails: {
    contractAddress: string
    compilerType: string
    compilerVersion: string
  }
  isVerified: boolean
  defaultValues?: Partial<ContractCodeFormValues>
  isSubmitting?: boolean
}

export function ContractCodeForm({
  onSubmit,
  onReset,
  contractDetails,
  isVerified,
  defaultValues,
  isSubmitting = false,
}: ContractCodeFormProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [isConstructorOpen, setIsConstructorOpen] = useState(false)
  const [isLibraryOpen, setIsLibraryOpen] = useState(false)
  const [sourceFiles, setSourceFiles] = useState<Record<string, string>>({})
  const [fileError, setFileError] = useState<string | null>(null)

  // Use the hook to fetch bytecode
  const { data: bytecode } = useGetCode(contractDetails.contractAddress)

  const form = useForm<ContractCodeFormValues>({
    resolver: zodResolver(contractCodeSchema),
    defaultValues: {
      contractCode: defaultValues?.contractCode || "",
      optimization: defaultValues?.optimization || "No",
      runs: defaultValues?.runs || "200",
      evmVersion: defaultValues?.evmVersion || "default",
      constructorArguments: defaultValues?.constructorArguments || "",
      bytecode: defaultValues?.bytecode || "",
    },
  })

  // Update bytecode field when it's fetched
  useEffect(() => {
    if (bytecode) {
      form.setValue("bytecode", bytecode as string)
    }
  }, [bytecode, form])

  const validateFiles = (files: File[]): boolean => {
    setFileError(null)

    // Check if compiler type requires multiple files
    if (
      contractDetails.compilerType === CompilerType.SOLIDITY_MULTI ||
      contractDetails.compilerType === CompilerType.VYPER_MULTI
    ) {
      // For multi-part files, require at least 2 files
      if (files.length < 2) {
        setFileError(
          "Please select at least 2 files for multi-part verification",
        )
        return false
      }

      // Check file extensions for Solidity
      if (contractDetails.compilerType === CompilerType.SOLIDITY_MULTI) {
        const invalidFiles = files.filter((file) => !file.name.endsWith(".sol"))
        if (invalidFiles.length > 0) {
          setFileError("All files must have .sol extension")
          return false
        }
      }

      // Check file extensions for Vyper
      if (contractDetails.compilerType === CompilerType.VYPER_MULTI) {
        const invalidFiles = files.filter((file) => !file.name.endsWith(".vy"))
        if (invalidFiles.length > 0) {
          setFileError("All files must have .vy extension")
          return false
        }
      }
    }
    // Check if compiler type requires JSON input
    else if (
      contractDetails.compilerType === CompilerType.SOLIDITY_JSON ||
      contractDetails.compilerType === CompilerType.VYPER_JSON
    ) {
      // For JSON input, allow only 1 file
      if (files.length !== 1) {
        setFileError("Please select exactly 1 JSON file")
        return false
      }

      // Check file extension
      if (!files[0].name.endsWith(".json")) {
        setFileError("File must have .json extension")
        return false
      }
    }

    return true
  }

  const handleFormSubmit = (data: ContractCodeFormValues) => {
    // Add the source files and bytecode to the form data
    const enhancedData = {
      ...data,
      sourceFiles,
      bytecode: bytecode || data.bytecode,
    }
    onSubmit(enhancedData as ContractCodeFormValues)
  }

  // Determine file extension based on compiler type
  const getFileExtension = () => {
    if (contractDetails.compilerType === CompilerType.SOLIDITY_MULTI) {
      return ".sol"
    } else if (contractDetails.compilerType === CompilerType.VYPER_MULTI) {
      return ".vy"
    } else if (
      contractDetails.compilerType === CompilerType.SOLIDITY_JSON ||
      contractDetails.compilerType === CompilerType.VYPER_JSON
    ) {
      return ".json"
    }
    return ""
  }

  // Determine if multiple files are allowed
  const isMultipleFilesAllowed = () => {
    return (
      contractDetails.compilerType === CompilerType.SOLIDITY_MULTI ||
      contractDetails.compilerType === CompilerType.VYPER_MULTI
    )
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className="space-y-6"
      >
        <div className="space-y-4">
          <ol className="list-decimal pl-5 space-y-2 text-sm">
            <li>
              If the contract compiles correctly at Remix, it should also
              compile correctly here.
            </li>
            <li>
              We have limited support for verifying contracts created by another
              contract and there is a timeout of up to 45 seconds for each
              contract compiled.
            </li>
            <li>
              For programmatic contract verification, check out the{" "}
              <a href="#" className="text-primary hover:underline">
                Contract API Endpoint
              </a>
              .
            </li>
          </ol>
        </div>

        <div className="grid grid-cols-[120px_1fr] gap-2 items-center text-sm">
          <div>Contract Address:</div>
          <div className="font-mono">
            {normalizeAddress(contractDetails.contractAddress)}
          </div>

          <div>Compiler Type:</div>
          <div>{contractDetails.compilerType}</div>

          <div>Compiler Version:</div>
          <div>{contractDetails.compilerVersion}</div>
        </div>

        <FormField
          control={form.control}
          name="contractCode"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <div className="flex items-center justify-between">
                <FormLabel className="text-sm font-medium">
                  {isMultipleFilesAllowed()
                    ? `Please select the ${getFileExtension().substring(
                        1,
                      )} files for upload *`
                    : contractDetails.compilerType.includes("json")
                    ? "Please select the JSON input file *"
                    : "Enter the Contract Code below *"}
                </FormLabel>
              </div>
              <FormControl>
                {contractDetails.compilerType ? (
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-md p-8 text-center flex flex-col items-center justify-center">
                    <div className="mb-4">
                      <div className="h-12 w-12 mx-auto mb-2 text-muted-foreground">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                          <polyline points="14 2 14 8 20 8" />
                        </svg>
                      </div>
                      {isMultipleFilesAllowed() ? (
                        <p className="text-sm text-muted-foreground">
                          CTRL click to select Multiple files
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          Select a single file
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        Supported file types: {getFileExtension()}
                      </p>
                      {fileError && (
                        <p className="text-xs text-red-500 mt-1">{fileError}</p>
                      )}
                    </div>

                    {Object.keys(sourceFiles).length > 0 && (
                      <div className="w-full mb-4">
                        <p className="text-sm font-medium mb-2">
                          Selected files:
                        </p>
                        <ul className="text-xs text-left">
                          {Object.keys(sourceFiles).map((fileName, index) => (
                            <li
                              key={index}
                              className="py-1 px-2 bg-muted/50 rounded mb-1"
                            >
                              {fileName}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <Input
                      type="file"
                      multiple={isMultipleFilesAllowed()}
                      accept={getFileExtension()}
                      className="hidden"
                      id="file-upload"
                      onChange={(e) => {
                        const files = Array.from(e.target.files || [])

                        if (files.length === 0) return

                        // Validate files based on compiler type
                        if (!validateFiles(files)) return

                        if (files.length > 0) {
                          const newSourceFiles: Record<string, string> = {}
                          let processedFiles = 0

                          files.forEach((file) => {
                            const reader = new FileReader()
                            reader.onload = (event) => {
                              if (event.target?.result) {
                                newSourceFiles[file.name] = event.target
                                  .result as string
                              }

                              processedFiles++
                              if (processedFiles === files.length) {
                                setSourceFiles(newSourceFiles)
                                // Set the first file content to the form field for backward compatibility
                                const firstFileName =
                                  Object.keys(newSourceFiles)[0]
                                field.onChange(newSourceFiles[firstFileName])
                              }
                            }
                            reader.readAsText(file)
                          })
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      onClick={() =>
                        document.getElementById("file-upload")?.click()
                      }
                    >
                      Upload File
                    </Button>
                  </div>
                ) : (
                  <Textarea
                    {...field}
                    className="min-h-[200px] font-mono text-xs"
                  />
                )}
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Collapsible
          className="border rounded-md"
          open={isAdvancedOpen}
          onOpenChange={setIsAdvancedOpen}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-sm font-medium">
            <span>Advanced Configuration</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${
                isAdvancedOpen ? "transform rotate-180" : ""
              }`}
            />
          </CollapsibleTrigger>
          <CollapsibleContent className="p-4 border-t">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="optimization"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <div className="flex items-center space-x-1">
                      <FormLabel className="text-sm">Optimization</FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <InfoIcon className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Enable optimization for contract compilation</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="No" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="No">No</SelectItem>
                        <SelectItem value="Yes">Yes</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="runs"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <div className="flex items-center space-x-1">
                      <FormLabel className="text-sm">
                        Runs (Optimizes)
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <InfoIcon className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Number of optimization runs</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="200" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="200">200</SelectItem>
                        <SelectItem value="1">1</SelectItem>
                        <SelectItem value="10000">10000</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="evmVersion"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <div className="flex items-center space-x-1">
                      <FormLabel className="text-sm">
                        EVM Version to target
                      </FormLabel>
                    </div>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="default (compiler defaults)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="default">
                          default (compiler defaults)
                        </SelectItem>
                        <SelectItem value="london">london</SelectItem>
                        <SelectItem value="berlin">berlin</SelectItem>
                        <SelectItem value="istanbul">istanbul</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible
          className="border rounded-md"
          open={isConstructorOpen}
          onOpenChange={setIsConstructorOpen}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-sm font-medium">
            <span>Constructor Arguments ABI-encoded</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${
                isConstructorOpen ? "transform rotate-180" : ""
              }`}
            />
          </CollapsibleTrigger>
          <CollapsibleContent className="p-4 border-t">
            <p className="text-xs text-muted-foreground mb-2">
              For contracts that were created with constructor parameters
            </p>
            <FormField
              control={form.control}
              name="constructorArguments"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      {...field}
                      className="min-h-[100px] font-mono text-xs"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <p className="text-xs text-muted-foreground mt-2">
              For additional information on Constructor Arguments, see our{" "}
              <a href="#" className="text-primary hover:underline">
                KB entry
              </a>
            </p>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible
          className="border rounded-md"
          open={isLibraryOpen}
          onOpenChange={setIsLibraryOpen}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-sm font-medium">
            <span>
              Contract Library Address (for contracts that use libraries,
              supports up to 10 libraries)
            </span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${
                isLibraryOpen ? "transform rotate-180" : ""
              }`}
            />
          </CollapsibleTrigger>
          <CollapsibleContent className="p-4 border-t">
            <p className="text-xs text-muted-foreground">
              Enter library addresses if your contract uses libraries
            </p>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <label className="text-xs font-medium">Library Name 1</label>
                <Input placeholder="e.g. SafeMath" />
              </div>
              <div className="space-y-2">
                <label className="text-xs font-medium">Library Address 1</label>
                <Input placeholder="0x..." />
              </div>
            </div>
            <Button variant="outline" size="sm" className="mt-4">
              <span>+ Add Library</span>
            </Button>
          </CollapsibleContent>
        </Collapsible>

        {isVerified && (
          <div className="flex items-center justify-center p-4 border rounded-md bg-green-50 dark:bg-green-900/20">
            <div className="flex items-center space-x-2">
              <div className="bg-green-500 rounded-full p-1">
                <Check className="h-4 w-4 text-white" />
              </div>
              <span className="font-medium text-green-700 dark:text-green-400">
                Success! Contract verified and published.
              </span>
            </div>
          </div>
        )}

        <div className="flex space-x-4 pt-4">
          <Button type="submit" className="px-8" disabled={isSubmitting}>
            {isSubmitting ? "Verifying..." : "Verify and Publish"}
          </Button>
          <Button type="button" variant="outline" onClick={onReset}>
            Reset
          </Button>
        </div>
      </form>
    </Form>
  )
}
