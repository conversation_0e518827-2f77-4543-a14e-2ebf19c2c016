"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AlertCircle } from "lucide-react"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

import { useStatsTop } from "@/hooks/useChart"
import {
  type StatsTopEntriesMap,
  StatsTopMetric,
  type StatsTopResult,
  StatsTopType,
} from "@/types/charts"
import { MetricSelector } from "./components/metric-selector"
import { NetworkStats } from "./components/network-stats"
import { OverviewStats } from "./components/overview-stats"
import { StatsTopSkeleton } from "./components/stats-top-skeleton"
import { TokenStats } from "./components/token-stats"
import { TransactionStats } from "./components/transaction-stats"

const isValidStatsTopType = (
  type: StatsTopType,
): type is keyof StatsTopEntriesMap => {
  return (
    type === StatsTopType.OVERVIEW ||
    type === StatsTopType.TRANSACTION ||
    type === StatsTopType.NETWORK ||
    type === StatsTopType.HOT_CONTRACT ||
    type === StatsTopType.TOKEN
  )
}

export default function StatsTopPage() {
  const searchParams = useSearchParams()

  const getInitialTab = (): StatsTopType => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash.replace("#", "").toUpperCase()
      if (hash && Object.values(StatsTopType).includes(hash as StatsTopType)) {
        return hash as StatsTopType
      }
    }

    const tabParam = searchParams.get("tab")?.toUpperCase()
    if (
      tabParam &&
      Object.values(StatsTopType).includes(tabParam as StatsTopType)
    ) {
      return tabParam as StatsTopType
    }

    return StatsTopType.OVERVIEW
  }

  const getInitialMetric = (): StatsTopMetric => {
    const metricParam = searchParams.get("metric")?.toUpperCase()
    if (
      metricParam &&
      Object.values(StatsTopMetric).includes(metricParam as StatsTopMetric)
    ) {
      return metricParam as StatsTopMetric
    }
    return StatsTopMetric.ONE_DAY
  }

  const [metric, setMetric] = useState<StatsTopMetric>(getInitialMetric())
  const [activeTab, setActiveTab] = useState<StatsTopType>(getInitialTab())

  // Update URL when tab or metric changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href)

      // Update search params with lowercase values
      url.searchParams.set("metric", metric.toLowerCase())

      // Only use hash for tab, remove tab from search params
      url.searchParams.delete("tab")

      url.hash = activeTab.toLowerCase()

      window.history.replaceState({}, "", url.toString())
    }
  }, [activeTab, metric])

  const isCurrentTabSupported = isValidStatsTopType(activeTab)
  const safeType = isCurrentTabSupported ? activeTab : StatsTopType.TRANSACTION

  const statsQuery = useStatsTop(metric, safeType as keyof StatsTopEntriesMap, {
    enabled: isCurrentTabSupported,
  })

  const isNetworkStats = (
    data: StatsTopResult<keyof StatsTopEntriesMap>,
  ): data is StatsTopResult<StatsTopType.NETWORK> => {
    return (
      data &&
      "topGasUsers" in data.entries &&
      "topActiveAddresses" in data.entries
    )
  }

  const isTokenStats = (
    data: StatsTopResult<keyof StatsTopEntriesMap>,
  ): data is StatsTopResult<StatsTopType.TOKEN> => {
    return (
      data &&
      "topTokensByTxCount" in data.entries &&
      "topTokensByUniqueSenders" in data.entries
    )
  }

  const isLoading = statsQuery.isLoading
  const isError = statsQuery.isError
  const statsData = statsQuery.data

  const isValidNetworkStats =
    activeTab === StatsTopType.NETWORK &&
    statsData !== undefined &&
    isNetworkStats(statsData)

  // Add a new variable to check if the data is valid token stats
  const isValidTokenStats =
    activeTab === StatsTopType.TOKEN &&
    statsData !== undefined &&
    isTokenStats(statsData)

  const handleTabChange = (value: string) => {
    setActiveTab(value as StatsTopType)
  }

  const handleMetricChange = (value: StatsTopMetric) => {
    setMetric(value)
  }

  // Function to navigate to a specific tab
  const navigateToTab = (tabType: StatsTopType) => {
    setActiveTab(tabType)
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4">
        <div className="card">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center p-6 text-center">
                <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
                <h3 className="text-lg font-semibold">
                  Error Loading Statistics
                </h3>
                <p className="text-sm text-muted-foreground mt-2">
                  An unexpected error occurred while loading the statistics
                  data.
                </p>
                <Button
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Top Statistics</h1>

      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="mb-4">
          <TabsTrigger value={StatsTopType.OVERVIEW}>Overview</TabsTrigger>
          <TabsTrigger value={StatsTopType.TRANSACTION}>
            Transactions
          </TabsTrigger>
          <TabsTrigger value={StatsTopType.TOKEN}>Tokens</TabsTrigger>
          <TabsTrigger value={StatsTopType.NETWORK}>Network</TabsTrigger>
          <TabsTrigger value={StatsTopType.HOT_CONTRACT}>
            Hot Contract
          </TabsTrigger>
        </TabsList>

        <MetricSelector
          currentMetric={metric}
          onMetricChange={handleMetricChange}
        />

        {isLoading && isCurrentTabSupported ? (
          <StatsTopSkeleton type={activeTab} />
        ) : (
          <>
            <TabsContent value={StatsTopType.TRANSACTION}>
              {activeTab === StatsTopType.TRANSACTION && statsData && (
                <TransactionStats data={statsData} />
              )}
            </TabsContent>

            <TabsContent value={StatsTopType.TOKEN}>
              <Card>
                <CardContent className="p-6">
                  {isValidTokenStats ? (
                    <TokenStats data={statsData} />
                  ) : (
                    <p className="text-center text-gray-500">
                      Token statistics are not available at this time.
                    </p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value={StatsTopType.NETWORK}>
              {isValidNetworkStats && <NetworkStats data={statsData} />}
            </TabsContent>

            <TabsContent value={StatsTopType.HOT_CONTRACT}>
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-gray-500">
                    Hot Contract statistics are not available at this time.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value={StatsTopType.OVERVIEW}>
              {activeTab === StatsTopType.OVERVIEW && statsData && (
                <OverviewStats data={statsData} onViewMore={navigateToTab} />
              )}
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  )
}
