"use client";

import { Download, Info } from "lucide-react";
import { useMemo, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Responsive<PERSON><PERSON>r, Toolt<PERSON> } from "recharts";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer } from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatHash, formatNumberWithNotation } from "@/helpers/format";
import { useStatsTop } from "@/hooks/useChart";
import { StatsTopMetric, StatsTopType } from "@/types/charts";

// Color palette for the pie chart
const COLORS = [
  "#3498db",
  "#2c3e50",
  "#27ae60",
  "#e67e22",
  "#8e44ad",
  "#f39c12",
  "#16a085",
  "#d35400",
];

// Define chart data item type
interface ChartDataItem {
  name: string;
  value: number;
  percentage: number;
  address: string;
  rank: number;
}

// Custom tooltip component
const CustomTooltip = ({
  active,
  payload,
}: {
  active?: boolean;
  payload?: any[];
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-md shadow-md p-3">
        <p className="font-medium">{payload[0].name}</p>
        <p className="text-sm">
          Transactions: {formatNumberWithNotation(payload[0].value)}
        </p>
        <p className="text-sm">
          Percentage: {payload[0].payload.percentage.toFixed(2)}%
        </p>
      </div>
    );
  }
  return null;
};

export default function DexActivityChart() {
  const [timeRange, setTimeRange] = useState<string>("1d");
  const [dataType, setDataType] = useState<string>("txnSenders");

  // Map time range to StatsTopMetric
  const getMetricFromTimeRange = (range: string): StatsTopMetric => {
    switch (range) {
      case "1d":
        return StatsTopMetric.ONE_DAY;
      case "3d":
        return StatsTopMetric.THREE_DAYS;
      case "all":
        return StatsTopMetric.ONE_WEEK;
      default:
        return StatsTopMetric.ONE_WEEK;
    }
  };

  const metric = getMetricFromTimeRange(timeRange);
  const { data: statsData, isLoading } = useStatsTop(
    metric,
    StatsTopType.TRANSACTION,
  );

  const chartData = useMemo<ChartDataItem[]>(() => {
    if (!statsData || !statsData?.entries) return [];

    const entries = statsData.entries;

    let dataEntries: any[] = [];

    if (dataType === "txnSenders" && entries?.topTxnSenders) {
      dataEntries = entries?.topTxnSenders;
    } else if (dataType === "txnReceivers" && entries?.topTxnReceivers) {
      dataEntries = entries?.topTxnReceivers;
    } else if (dataType === "ethSenders" && entries?.topEthSenders) {
      dataEntries = entries?.topEthSenders;
    } else if (dataType === "ethReceivers" && entries?.topEthReceivers) {
      dataEntries = entries?.topEthReceivers;
    }

    return dataEntries.map((entry) => {
      const value =
        "count" in entry
          ? entry.count
          : entry.value && entry.value !== "NaN"
          ? Number.parseFloat(entry.value)
          : 0;

      const percentage =
        entry.percentage && entry.percentage !== "NaN"
          ? Number.parseFloat(entry.percentage)
          : 0;

      return {
        name: formatHash(entry.address, 6, 4),
        value,
        percentage,
        address: entry.address,
        rank: entry.rank,
      };
    });
  }, [statsData, dataType]);

  // Calculate total from chart data
  const total = useMemo(() => {
    return chartData.reduce((acc, item) => acc + (item.value || 0), 0);
  }, [chartData]);

  // Get time range label
  const timeRangeLabel = useMemo(() => {
    switch (timeRange) {
      case "1d":
        return "Last 24 hours";
      case "3d":
        return "Last 3 days";
      case "all":
        return "All time (showing 7-day data)";
      default:
        return "Last 7 days";
    }
  }, [timeRange]);

  // Get data type label
  const dataTypeLabel = useMemo(() => {
    switch (dataType) {
      case "txnSenders":
        return "Transaction Senders";
      case "txnReceivers":
        return "Transaction Receivers";
      case "ethSenders":
        return "ETH Senders";
      case "ethReceivers":
        return "ETH Receivers";
      default:
        return "Transaction Senders";
    }
  }, [dataType]);

  // Handle download CSV
  const handleDownloadCSV = () => {
    if (!chartData.length) return;

    const headers = [
      "Rank",
      "Address",
      dataType.includes("Eth") ? "Value (ETH)" : "Transactions",
      "Percentage",
    ];
    const csvContent = [
      headers.join(","),
      ...chartData.map((item) =>
        [
          item.rank,
          item.address,
          item.value,
          item.percentage.toFixed(2) + "%",
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `network-activity-${dataType}-${timeRange}.csv`,
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>DEX Activity</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Select value={dataType} onValueChange={setDataType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select data type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="txnSenders">Transaction Senders</SelectItem>
                <SelectItem value="txnReceivers">
                  Transaction Receivers
                </SelectItem>
                <SelectItem value="ethSenders">ETH Senders</SelectItem>
                <SelectItem value="ethReceivers">ETH Receivers</SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">Last 24 hours</SelectItem>
                <SelectItem value="3d">Last 3 days</SelectItem>
                <SelectItem value="all">1 weeks</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center text-sm text-muted-foreground mb-4">
            {dataTypeLabel} - {timeRangeLabel}
            <br />
            Source: Helios Explorer
          </div>

          <div className="h-[400px]">
            {isLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Skeleton className="h-[350px] w-full" />
              </div>
            ) : chartData.length > 0 ? (
              <ChartContainer
                config={chartData.reduce<
                  Record<string, { label: string; color: string }>
                >((acc, item, index) => {
                  acc[`item${index}`] = {
                    label: item.name || `Item ${index + 1}`,
                    color: COLORS[index % COLORS.length],
                  };
                  return acc;
                }, {})}
                className="h-full w-full"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={80}
                      outerRadius={140}
                      fill="#8884d8"
                      paddingAngle={1}
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percentage }) =>
                        `${name || ""} (${(percentage || 0).toFixed(2)}%)`
                      }
                      labelLine={true}
                    >
                      {chartData.map((_item, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </div>

          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={handleDownloadCSV}
              disabled={chartData.length === 0}
            >
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>

          <div className="mt-8 rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rank</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead className="text-right">
                    {dataType.includes("Eth")
                      ? "Value (ETH)"
                      : "Total Transactions"}
                  </TableHead>
                  <TableHead className="text-right">Percentage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-40" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-20 ml-auto" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-16 ml-auto" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : chartData.length > 0 ? (
                  chartData.map((row: ChartDataItem, index: number) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {row.rank || index + 1}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div
                            className="h-6 w-6 rounded-full"
                            style={{
                              backgroundColor: COLORS[index % COLORS.length],
                            }}
                          ></div>
                          {row.address}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatNumberWithNotation(row.value || 0)}
                      </TableCell>
                      <TableCell className="text-right">
                        {(row.percentage || 0).toFixed(2)}%
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={4}
                      className="text-center py-4 text-muted-foreground"
                    >
                      No data found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            A breakdown of network activity tracked by Helios Explorer. This
            chart allows you to view different types of blockchain activity:
          </p>
          <ul className="mt-2 text-sm text-muted-foreground list-disc pl-5 space-y-1">
            <li>
              <strong>Transaction Senders:</strong> Addresses that send the most
              transactions
            </li>
            <li>
              <strong>Transaction Receivers:</strong> Addresses that receive the
              most transactions
            </li>
            <li>
              <strong>ETH Senders:</strong> Addresses that send the most ETH by
              value
            </li>
            <li>
              <strong>ETH Receivers:</strong> Addresses that receive the most
              ETH by value
            </li>
          </ul>
          <p className="mt-2 text-sm text-muted-foreground">
            <strong>Note:</strong> Currently, data is only available for 1-day,
            3-day, and 7-day periods. Selecting longer periods will display the
            7-day data.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
