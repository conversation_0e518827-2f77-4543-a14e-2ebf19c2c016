import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, ResponsiveContainer, XAxis, YAxis } from "recharts"

interface GasUsedChartProps {
  isLoading: boolean
  blocksChartData: Array<{
    block: number
    timestamp: string
    baseFee: number
    gasUsed: number
    time: string
    date: string
  }>
}

export default function GasUsedChart({ isLoading, blocksChartData }: GasUsedChartProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
            <span className="text-blue-600 text-xs">Ξ</span>
          </div>
          <CardTitle className="text-base">Gas Used per Block</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[250px]">
          {isLoading ? (
            <div className="flex h-full w-full items-center justify-center">
              <Skeleton className="h-[200px] w-full" />
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={blocksChartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="block" tickLine={false} axisLine={false} tickFormatter={(value) => value.toString()} />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`}
                />
                <Bar dataKey="gasUsed" name="Gas Used" fill="#3498db" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
