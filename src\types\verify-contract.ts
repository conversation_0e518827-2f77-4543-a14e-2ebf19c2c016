export enum BytecodeType {
  CREATION_INPUT = "CREATION_INPUT",
  DEPLOYED_BYTECODE = "DEPLOYED_BYTECODE",
  BYTECODE_TYPE_UNSPECIFIED = "BYTECODE_TYPE_UNSPECIFIED",
}

export enum VerifyResponseStatus {
  STATUS_UNSPECIFIED = "STATUS_UNSPECIFIED",
  SUCCESS = "SUCCESS",
  FAILURE = "FAILURE",
}

export enum SourceType {
  SOURCE_TYPE_UNSPECIFIED = "SOURCE_TYPE_UNSPECIFIED",
  SOLIDITY = "SOLIDITY",
  VYPER = "VYPER",
  YUL = "YUL",
}

export enum SourceMatchType {
  SOURCE_MATCH_TYPE_UNSPECIFIED = "SOURCE_MATCH_TYPE_UNSPECIFIED",
  MATCH = "MATCH",
  MISMATCH = "<PERSON><PERSON><PERSON><PERSON>",
}

export enum CompilerType {
  SOLIDITY_MULTI = "solidity-multi",
  SOLIDITY_JSON = "solidity-json",
  VYPER_MULTI = "vyper-multi",
  VYPER_JSON = "vyper-json",
}

export type SolidityVerifierStandardParams = {
  bytecode: string
  bytecodeType: BytecodeType
  compilerVersion: string
  input: string
  contractAddress?: string
  metadata?: {
    chainId: string
    contractAddress: string
    transactionHash: string
    blockNumber: string
    transactionIndex: string
    deployer: string
    creationCode: string
    runtimeCode: string
  }
}

export type SolidityVerifierMultiplePartParams = {
  bytecode: string
  bytecodeType: BytecodeType
  compilerVersion: string
  evmVersion: string
  optimizationRuns: string

  sourceFiles: Record<string, string>
  libraries: Record<string, string>

  metadata?: {
    chainId: string
    contractAddress: string
    transactionHash: string
    blockNumber: string
    transactionIndex: string
    deployer: string
    creationCode: string
    runtimeCode: string
  }
}

export type VyperVerifierMultiplePartParams = {
  bytecode: string
  bytecodeType: BytecodeType
  compilerVersion: string
  evmVersion: string
  sourceFiles: Record<string, string>
  interfaces: Record<string, string>
  metadata?: {
    chainId: string
    contractAddress: string
    transactionHash: string
    blockNumber: string
    transactionIndex: string
    deployer: string
    creationCode: string
    runtimeCode: string
  }
}

export type VyperVerifierStandardParams = {
  bytecode: string
  bytecodeType: BytecodeType
  compilerVersion: string
  input: string
  metadata?: {
    chainId: string
    contractAddress: string
    transactionHash: string
    blockNumber: string
    transactionIndex: string
    deployer: string
    creationCode: string
    runtimeCode: string
  }
}

export interface VersionsContractResponse {
  compilerVersions: string[]
}

export interface VyperVersionsResponse {
  id: number
  jsonrpc: string
  result: {
    compilerVersions: string[]
  }
}

export interface VerifyContractResponse {
  id: number
  jsonrpc: string
  result: {
    message: string
    status: VerifyResponseStatus
    source: {
      fileName: string
      contractName: string
      compilerVersion: string
      compilerSettings: string
      sourceType: SourceType
      sourceFiles: Record<string, string>
      abi: string
      constructorArguments: string
      matchType: SourceMatchType
      compilationArtifacts: string
      creationInputArtifacts: string
      deployedBytecodeArtifacts: string
      isBlueprint: boolean
    }
  }
}

export interface VerifyContractResponseResult {
  message: string
  status: VerifyResponseStatus
  source: {
    fileName: string
    contractName: string
    compilerVersion: string
    compilerSettings: string
    sourceType: SourceType
    sourceFiles: Record<string, string>
    abi: string
    constructorArguments: string
    matchType: SourceMatchType
    compilationArtifacts: string
    creationInputArtifacts: string
    deployedBytecodeArtifacts: string
    isBlueprint: boolean
  }
}

export interface SearchCodeParams {
  bytecode: string
  bytecodeType: BytecodeType
}

export interface Source {
  fileName: string
  contractName: string
  compilerVersion: string
  compilerSettings: string
  sourceType: string
  sourceFiles: Record<string, string>
  abi: string
  constructorArguments: string
  matchType: string
  compilationArtifacts: string
  creationInputArtifacts: string
  deployedBytecodeArtifacts: string
  isBlueprint: boolean
}

export interface SearchCodeResponse {
  sources: Source[]
}

export enum AccountTypeEnum {
  ADDRESS = "ADDRESS",
  SMART_CONTRACT = "SMART_CONTRACT",
  CRON = "cron",
}

export interface AccountInfo {
  id: number
  createdAt: string
  updatedAt: string
  address: string
  isContract: boolean
  accountType: AccountTypeEnum
  ownerAddress: any
  targetContract: any
  cronId: any
  firstSeenBlock: number
  firstTxHash: string
  lastSeenBlock: number
  lastTxHash: string
  txCount: number
  checkedERC20: boolean
  isProxy: boolean
}

export interface AccountInfoResponse {
  id: number
  jsonrpc: string
  result: AccountInfo
}

export interface GetAbiResponse {
  jsonrpc: string
  id: number
  result: string
}
