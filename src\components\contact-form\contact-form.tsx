"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { EnhancedAddNameTagForm } from "./component/enhanced-add-name-tag-form"
import { GeneralInquiryForm } from "./component/general-inquiry-form"
import { RemoveNameTagForm } from "./component/remove-name-tag-form"
import { UpdateTokenInfoForm } from "./component/update-token-info-form"

const subjectToIdMap: Record<string, string> = {
  "1.a": "1",
  "1.b": "2",
  "1.c": "3",
  "2.a": "4",
  "2.b": "5",
  "2.c": "6",
  "2.d": "7",
  "2.e": "8",
  "3.a": "9",
  "3.b": "10",
  "4": "11",
  "5": "12",
}

const idToSubjectMap: Record<string, string> = Object.entries(
  subjectToIdMap,
).reduce((acc, [subject, id]) => {
  acc[id] = subject
  return acc
}, {} as Record<string, string>)

const subjects = [
  {
    group: "1. Inquiries",
    items: [
      { value: "1.a", label: "1.a. General Inquiry" },
      { value: "1.b", label: "1.b. Advertising" },
      { value: "1.c", label: "1.c. EaaS" },
    ],
  },
  {
    group: "2. Submissions",
    items: [
      { value: "2.a", label: "2.a. Update Token Info" },
      { value: "2.b", label: "2.b. Add Name Tag/Label" },
      { value: "2.c", label: "2.c. Request Removal of Name Tag" },
      { value: "2.d", label: "2.d. Suggest Transaction Action" },
      {
        value: "2.e",
        label: "2.e. Update Proxy Contract's Implementation Address",
      },
    ],
  },
  {
    group: "3. Security",
    items: [
      { value: "3.a", label: "3.a. Report Phishing Address" },
      { value: "3.b", label: "3.b. Security Audit" },
    ],
  },
  {
    group: "4. Support",
    items: [{ value: "4", label: "4. Priority Support" }],
  },
  {
    group: "5. API",
    items: [{ value: "5", label: "5. API Support" }],
  },
]

export default function ContactForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [subjectType, setSubjectType] = useState<string>("")

  useEffect(() => {
    const id = searchParams.get("id")
    if (id && idToSubjectMap[id]) {
      setSubjectType(idToSubjectMap[id])
    }
  }, [searchParams])

  const handleSubjectChange = (value: string) => {
    setSubjectType(value)

    const id = subjectToIdMap[value]
    if (id) {
      router.push(`?id=${id}`, { scroll: false })
    } else {
      router.push("", { scroll: false })
    }
  }

  const renderForm = () => {
    switch (subjectType) {
      case "1.a":
        return <GeneralInquiryForm />
      case "2.a":
        return <UpdateTokenInfoForm />
      case "2.b":
        return <EnhancedAddNameTagForm />
      case "2.c":
        return <RemoveNameTagForm />
      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="px-6 py-4 border-b">
        <div className="flex items-center space-x-2">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject
          </Label>
          <span className="text-red-500">*</span>
        </div>
        <Select value={subjectType} onValueChange={handleSubjectChange}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Please Select Your Message Subject" />
          </SelectTrigger>
          <SelectContent>
            {subjects.map((group) => (
              <SelectGroup key={group.group}>
                <SelectLabel>{group.group}</SelectLabel>
                {group.items.map((item) => (
                  <SelectItem key={item.value} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            ))}
          </SelectContent>
        </Select>
      </CardHeader>

      <CardContent className="p-6">
        {subjectType ? (
          renderForm()
        ) : (
          <div className="text-center text-gray-500 py-8">
            Please select a subject to continue
          </div>
        )}
      </CardContent>
    </Card>
  )
}
