"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { InfoIcon } from "lucide-react"

import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Alert, AlertDescription } from "@/components/ui/alert"

import { updateTokenInfoSchema } from "@/lib/validations/contact-form-schemas"
import type { UpdateTokenInfoFormValues } from "@/lib/validations/contact-form-schemas"
import { SubmitButton } from "./submit-button"

export function UpdateTokenInfoForm() {
  const form = useForm<UpdateTokenInfoFormValues>({
    resolver: zodResolver(updateTokenInfoSchema),
    defaultValues: {
      name: "",
      email: "",
      tokenAddress: "",
      tokenName: "",
      tokenSymbol: "",
      tokenDecimals: "",
      additionalComment: "",
    },
  })

  const onSubmit = (data: UpdateTokenInfoFormValues) => {
    console.log("Form submitted:", data)

    alert("Form submitted successfully!")
    form.reset()
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Alert className="bg-blue-50 border-blue-200 text-blue-800">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>To update token information:</strong>
            <br />
            Log in to your Helios account and proceed to our token update page:{" "}
            <a href="https://explorer.helioschainlabs.org/tokenupdate" className="text-blue-600 hover:underline">
              https://explorer.helioschainlabs.org/tokenupdate
            </a>
            <br />
            If you do not have an account, please sign up here:{" "}
            <a href="https://explorer.helioschainlabs.org/register" className="text-blue-600 hover:underline">
              https://explorer.helioschainlabs.org/register
            </a>
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="Your name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Email Address <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="tokenAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Token Address <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input placeholder="0x..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            control={form.control}
            name="tokenName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Token Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="e.g. Ethereum" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tokenSymbol"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Token Symbol <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="e.g. ETH" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tokenDecimals"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Token Decimals <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="e.g. 18" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="additionalComment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Comment</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Any additional information you'd like to provide"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <SubmitButton />
      </form>
    </Form>
  )
}
