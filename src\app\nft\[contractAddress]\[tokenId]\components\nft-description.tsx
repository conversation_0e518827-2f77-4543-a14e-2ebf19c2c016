"use client"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, Info } from "lucide-react"
import { useState } from "react"

interface NFTDescriptionProps {
  description?: string
}

export function NFTDescription({ description }: NFTDescriptionProps) {
  const [isOpen, setIsOpen] = useState(true)

  if (!description) {
    return null
  }

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="border rounded-lg overflow-hidden"
    >
      <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gray-50 hover:bg-gray-100">
        <div className="flex items-center">
          <Info className="h-4 w-4 mr-2" />
          <span className="font-medium">Description</span>
        </div>
        {isOpen ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="p-4">
          <p className="text-gray-700 leading-relaxed">{description}</p>
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
