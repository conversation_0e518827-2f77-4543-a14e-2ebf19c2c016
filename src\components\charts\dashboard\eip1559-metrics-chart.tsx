"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { useBlockList } from "@/hooks/useBlocks"
import { ChartTypeEnum, useChart } from "@/hooks/useChart"
import { hexToNumber } from "@/lib/utils"
import { useMemo, useState } from "react"
import AboutEIP1559 from "./eip1559/about-eip1559"
import BaseFeeSection from "./eip1559/base-fee-section"
import BurntFeesChartSection from "./eip1559/burnt-fees-chart-section"
import BurntFeesTable from "./eip1559/burnt-fees-table"
import GasUsedChart from "./eip1559/gas-used-chart"
import SummaryCards from "./eip1559/summary-card"

const timeRangeOptions = [
  { value: "1d", label: "1d" },
  { value: "1w", label: "1w" },
  { value: "1m", label: "1m" },
  { value: "3m", label: "3m" },
  { value: "all", label: "All" },
]

export default function EIP1559MetricsChart() {
  const [timeRange, setTimeRange] = useState<string>("1m")
  const [activeTab, setActiveTab] = useState<string>("overview")
  const [searchBlock, setSearchBlock] = useState<string>("")

  // Calculate time range in milliseconds
  const { startTime, endTime } = useMemo(() => {
    const now = Date.now()
    let start = now

    switch (timeRange) {
      case "1d":
        start = now - 24 * 60 * 60 * 1000
        break
      case "1w":
        start = now - 7 * 24 * 60 * 60 * 1000
        break
      case "1m":
        start = now - 30 * 24 * 60 * 60 * 1000
        break
      case "3m":
        start = now - 90 * 24 * 60 * 60 * 1000
        break
      case "all":
        start = now - 365 * 24 * 60 * 60 * 1000
        break
    }

    return { startTime: start, endTime: now }
  }, [timeRange])

  const { data: metricsResponse, isLoading: isLoadingMetrics } = useChart(
    startTime,
    endTime,
    ChartTypeEnum.EIP1559_METRICS,
  )

  const { data: blocksResponse, isLoading: isLoadingBlocks } = useBlockList({
    page: 1,
    limit: 100,
    refetchInterval: 60 * 1000,
  })

  // Safely access the data with proper type checking
  const eip1559Data = metricsResponse || []
  const blocksData = blocksResponse?.result?.data || []

  // Process EIP1559 metrics data for charts
  const chartData = useMemo(() => {
    if (!Array.isArray(eip1559Data)) return []

    return eip1559Data.map((item: any) => ({
      date: new Date(item.timestamp).toISOString().split("T")[0],
      burntFees: Number.parseFloat(item.totalBurnFee) / 1e18, // Convert from wei to ETH directly
      burntFeesUsd: Number.parseFloat(item.totalBurnFeeUsd || "0"),
    }))
  }, [eip1559Data])

  // Process blocks data for base fee and gas used charts
  const blocksChartData = useMemo(() => {
    if (!Array.isArray(blocksData)) return []

    return blocksData
      .map((block: any) => ({
        block: hexToNumber(block.number || "0x0"),
        timestamp: new Date(Number.parseInt(block.timestamp || "0", 16) * 1000).toISOString(),
        baseFee: hexToNumber(block.baseFeePerGas || "0x0") / 1e9, // Convert to Gwei
        gasUsed: hexToNumber(block.gasUsed || "0x0"),
        time: new Date(Number.parseInt(block.timestamp || "0", 16) * 1000).toLocaleTimeString(),
        date: new Date(Number.parseInt(block.timestamp || "0", 16) * 1000).toLocaleDateString(),
      }))
      .sort((a, b) => a.block - b.block)
  }, [blocksData])

  // Filter blocks data for table view
  const filteredBlocksData = useMemo(() => {
    if (!blocksChartData.length) return []

    return blocksChartData.filter((block) => (searchBlock ? block.block.toString().includes(searchBlock) : true))
  }, [blocksChartData, searchBlock])

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!chartData.length) return { totalBurnt: 0, totalBurntUsd: 0, highestDailyBurn: 0 }

    const totalBurnt = chartData.reduce((sum, item) => sum + item.burntFees, 0)
    const totalBurntUsd = chartData.reduce((sum, item) => sum + item.burntFeesUsd, 0)
    const highestDailyBurn = Math.max(...chartData.map((item) => item.burntFees))

    return {
      totalBurnt,
      totalBurntUsd,
      highestDailyBurn,
    }
  }, [chartData])

  const isLoading = isLoadingMetrics || isLoadingBlocks

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <SummaryCards isLoading={isLoading} summaryStats={summaryStats} />

      {/* Main Content */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <BurntFeesChartSection
            isLoading={isLoading}
            chartData={chartData}
            timeRange={timeRange}
            setTimeRange={setTimeRange}
            timeRangeOptions={timeRangeOptions}
          />
        </div>

        <div>
          <BurntFeesTable isLoading={isLoading} chartData={chartData} />
        </div>
      </div>

      {/* Gas Used Per Block */}
      <GasUsedChart isLoading={isLoading} blocksChartData={blocksChartData} />

      {/* Base Fee and Priority Fee */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-1">
          <TabsTrigger value="overview">Base Fee Per Block</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-4">
          <BaseFeeSection
            isLoading={isLoading}
            blocksChartData={blocksChartData}
            filteredBlocksData={filteredBlocksData}
            searchBlock={searchBlock}
            setSearchBlock={setSearchBlock}
          />
        </TabsContent>
      </Tabs>

      <AboutEIP1559 />
    </div>
  )
}
