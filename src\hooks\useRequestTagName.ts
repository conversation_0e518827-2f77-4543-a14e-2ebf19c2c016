import apiContact from "@/lib/api/apiContact"
import {
  RequestNamesPayload,
  RequestNamesResponse,
} from "@/types/request-tag-name"
import { useMutation } from "@tanstack/react-query"

export const useRequestNames = () => {
  return useMutation<RequestNamesResponse, Error, RequestNamesPayload>({
    mutationFn: async (data: RequestNamesPayload) => {
      const response = await apiContact.post<RequestNamesResponse>(
        "request-names",
        data,
      )

      if (!response) {
        throw new Error("Failed to submit name request")
      }

      return response as unknown as RequestNamesResponse
    },
  })
}
