"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { NATIVE_SYMBOL } from "@/constants";
import {
  Area,
  AreaChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  XAxis,
  <PERSON>Axis,
} from "recharts";
import TimeRangeSelector from "../../time-range-seletor";

interface BurntFeesChartProps {
  isLoading: boolean;
  chartData: Array<{
    date: string;
    burntFees: number;
    burntFeesUsd: number;
  }>;
  timeRange: string;
  setTimeRange: (value: string) => void;
  timeRangeOptions: Array<{ value: string; label: string }>;
}

export default function BurntFeesChartSection({
  isLoading,
  chartData,
  timeRange,
  setTimeRange,
  timeRangeOptions,
}: BurntFeesChartProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
            <span className="text-blue-600 text-xs">Ξ</span>
          </div>
          <CardTitle className="text-base">Total Burnt Fees</CardTitle>
        </div>
        <TimeRangeSelector
          timeRange={timeRange}
          onChange={setTimeRange}
          options={timeRangeOptions}
        />
      </CardHeader>
      <CardContent>
        <div className="h-[350px]">
          {isLoading ? (
            <div className="flex h-full w-full items-center justify-center">
              <Skeleton className="h-[300px] w-full" />
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart
                data={chartData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient
                    id="colorBurntFees"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="5%" stopColor="#3498db" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#3498db" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }}
                />
                <YAxis
                  yAxisId="left"
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value.toFixed(4)}`}
                  domain={["auto", "auto"]}
                />
                <YAxis
                  yAxisId="right"
                  orientation="right"
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `$${value.toFixed(2)}`}
                  domain={["auto", "auto"]}
                />
                <Legend />
                <Area
                  yAxisId="left"
                  type="monotone"
                  dataKey="burntFees"
                  name={`Burnt Fees (${NATIVE_SYMBOL})`}
                  stroke="#3498db"
                  fillOpacity={1}
                  fill="url(#colorBurntFees)"
                />
                <Area
                  yAxisId="right"
                  type="monotone"
                  dataKey="burntFeesUsd"
                  name="Burnt Fees (USD)"
                  stroke="#f39c12"
                  fill="none"
                />
              </AreaChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
