$responsive: 1084px;

.nav {
  --transition: 0.2s var(--cubic);
  font-weight: 400;
  margin-right: auto;
  position: relative;
  z-index: 1000;

  &.open {
    .list {
      transform: translateX(0);
    }
  }

  // Force close button to be hidden in desktop view regardless of state
  @media screen and (min-width: ($responsive + 1px)) {
    .close {
      display: none !important; 
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
    }

    // Reset mobile menu state when in desktop view
    &.open .list {
      transform: none;
    }
  }
}

.close {
  position: absolute;
  top: var(--content-padding);
  right: var(--content-padding);
  transform: translateY(15%);
  transition: opacity 0.3s ease, visibility 0.3s ease;

  @media screen and (min-width: ($responsive + 1px)) {
    display: none;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }
}

.list {
  display: flex;
  align-items: center;
  gap: 0.25em;

  @media screen and (max-width: 1200px) {
    gap: 0.1em;
    font-size: 0.85em;
  }

  @media screen and (max-width: $responsive) {
    position: fixed;
    inset: 0;
    left: auto;
    z-index: 9999;
    background-color: hsl(var(--background) / 0.85);
    width: calc(300px + 40 * (100vw - 320px) / 1080);
    backdrop-filter: blur(16px);
    color: hsl(var(--text));
    border-radius: var(--radius) 0 0 var(--radius);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1) inset,
      0 16px 80px rgba(0, 0, 0, 0.5);
    padding: 1.5em;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    font-size: 1.1em;
    font-weight: 500;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
    transform: translateX(100%);
    transition: transform 0.6s var(--cubic);

    &::-webkit-scrollbar {
      display: none;
    }

    .sub {
      padding-left: 0.75em;
      font-size: 0.95em;
    }
  }

  li {
    position: relative;

    @media screen and (max-width: $responsive) {
      width: 100%;

      &+li {
        margin-top: 0.5em;
      }
    }

    &:hover {
      &>.item {
        background-color: hsl(var(--white) / 0.075);

        &~.sub {
          opacity: 1;
          visibility: visible;
          top: calc(100% + 1em);
        }
      }
    }
  }
}

.item {
  cursor: pointer;
  border-radius: 50px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5em;
  transition: background-color var(--transition);
  white-space: nowrap;
  padding: 0.5em 1.25em 0.55em;

  &:hover {
    background-color: hsl(var(--white) / 0.075);
  }

  .icon {
    width: 1em;
    height: 1em;
    padding: 0.1em;
    border-radius: 50%;
    border: 1px solid hsl(var(--white) / 0.1);
    transition: background-color var(--transition);
    position: relative;
    top: 1px;
    margin-right: -0.25em;
  }

  @media screen and (max-width: $responsive) {
    padding: 0.5em 0.5em 0.55em;

    &[data-sub] {
      justify-content: space-between;
      color: hsl(var(--muted-foreground));
    }

    &:hover {
      background-color: transparent;
      cursor: default;

      &:not([data-sub]) {
        cursor: pointer;
        color: hsl(var(--primary));
      }
    }
  }
}

.sub {
  @media screen and (min-width: ($responsive + 1px)) {
    padding: 0.75em;
    position: absolute;
    color: hsl(var(--white));
    border-radius: var(--radius);
    backdrop-filter: blur(16px);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition), visibility var(--transition),
      top var(--transition);
    top: 100%;
    left: 0;
    background-color: hsl(var(--foreground) / 0.25);
    box-shadow: 0 0 0 1000px hsl(var(--white) / 0.1) inset,
      0 0 0 1px rgba(255, 255, 255, 0.05) inset, 0 16px 80px rgba(0, 0, 0, 0.5);
  }
}