name: Deploy explorer dev

on:
  push:
    branches:
      - develop

jobs:
  deploy-explorer-staging:
    runs-on: self-hosted
    
    steps:
      - name: Deploy explorer staging
        run: |
          cd /home/<USER>/helios-explorer
          pwd
          ls -la
          git checkout develop --force && git pull --no-rebase
          cp .env.dev .env
          docker compose -f docker-compose.dev.yaml up -d --build

