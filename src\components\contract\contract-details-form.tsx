"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { InfoIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import {
  useGetVersionsSolidity,
  useGetVersionsVyper,
} from "@/hooks/useVerifyContract";
import {
  contractDetailsSchema,
  type ContractDetailsFormValues,
} from "@/lib/validations/contract-schema";
import { CompilerType } from "@/types/verify-contract";

interface ContractDetailsFormProps {
  onSubmit: (data: ContractDetailsFormValues) => void;
  defaultValues?: Partial<ContractDetailsFormValues>;
}

export function ContractDetailsForm({
  onSubmit,
  defaultValues,
}: ContractDetailsFormProps) {
  const [showNightlyCommits, setShowNightlyCommits] = useState(false);
  const [compilerVersionType, setCompilerVersionType] = useState<string>(
    defaultValues?.compilerVersionType || "",
  );

  const { data: solidityVersionsData } = useGetVersionsSolidity();
  const { data: vyperVersionsData } = useGetVersionsVyper();

  const solidityVersions = solidityVersionsData?.result?.compilerVersions || [];

  const vyperVersions = vyperVersionsData?.result?.compilerVersions || [];

  const form = useForm<ContractDetailsFormValues>({
    resolver: zodResolver(contractDetailsSchema),
    defaultValues: {
      contractAddress: defaultValues?.contractAddress || "",
      compilerType: defaultValues?.compilerType || "",
      compilerVersion: defaultValues?.compilerVersion || "",
      licenseType: defaultValues?.licenseType || "",
      agreeToTerms: defaultValues?.agreeToTerms || false,
      compilerVersionType: defaultValues?.compilerVersionType || "",
    },
  });

  const compilerType = form.watch("compilerType");

  // Update compiler version type when compiler type changes
  const handleCompilerTypeChange = (value: string) => {
    form.setValue("compilerType", value, { shouldValidate: true });

    const versionType = value.toLowerCase().includes("vyper")
      ? "vyper"
      : "solidity";

    setCompilerVersionType(versionType);
    // form.setValue("compilerVersionType", versionType, { shouldValidate: true });
    form.setValue("compilerVersion", "", { shouldValidate: false });
  };

  // Filter out nightly builds if needed
  const filteredSolidityVersions = showNightlyCommits
    ? solidityVersions
    : solidityVersions.filter((v) => !v.includes("nightly"));

  const filteredVyperVersions = showNightlyCommits
    ? vyperVersions
    : vyperVersions.filter((v) => !v.includes("nightly"));

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="contractAddress"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>
                Please enter the Contract Address you would like to verify
              </FormLabel>
              <FormControl>
                <Input placeholder="0x..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="compilerType"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>Please select Compiler Type</FormLabel>
              <Select
                onValueChange={handleCompilerTypeChange}
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value={CompilerType.SOLIDITY_MULTI}>
                    Solidity (Multi-Part files)
                  </SelectItem>
                  <SelectItem value={CompilerType.SOLIDITY_JSON}>
                    Solidity (Standard-Json-Input)
                  </SelectItem>
                  <SelectItem value={CompilerType.VYPER_MULTI}>
                    Vyper (Multi-Part files)
                  </SelectItem>
                  <SelectItem value={CompilerType.VYPER_JSON}>
                    Vyper-Json (Experimental)
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="compilerVersion"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>Please select Compiler Version</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!compilerType}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {compilerVersionType === "vyper"
                    ? filteredVyperVersions.map((version) => (
                        <SelectItem key={version} value={version}>
                          {version}
                        </SelectItem>
                      ))
                    : filteredSolidityVersions.map((version) => (
                        <SelectItem key={version} value={version}>
                          {version}
                        </SelectItem>
                      ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex items-center space-x-2">
          <Checkbox
            id="nightlyCommits"
            checked={!showNightlyCommits}
            onCheckedChange={(checked) => setShowNightlyCommits(!checked)}
          />
          <Label
            htmlFor="nightlyCommits"
            className="text-sm text-muted-foreground"
          >
            Hide nightly builds
          </Label>
        </div>

        <FormField
          control={form.control}
          name="licenseType"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <div className="flex items-center space-x-2">
                <FormLabel>Please select Open Source License Type</FormLabel>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Choosing a license defines how others can use your code
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Please Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="mit">MIT License</SelectItem>
                  <SelectItem value="gpl-3.0">
                    GNU General Public License v3.0
                  </SelectItem>
                  <SelectItem value="apache-2.0">Apache License 2.0</SelectItem>
                  <SelectItem value="bsd-2-clause">
                    BSD 2-Clause License
                  </SelectItem>
                  <SelectItem value="bsd-3-clause">
                    BSD 3-Clause License
                  </SelectItem>
                  <SelectItem value="mpl-2.0">
                    Mozilla Public License 2.0
                  </SelectItem>
                  <SelectItem value="unlicense">The Unlicense</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="agreeToTerms"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="!mt-0">
                <FormLabel className="text-sm">
                  I agree to the{" "}
                  <a href="#" className="text-primary hover:underline">
                    terms of service
                  </a>
                </FormLabel>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        <div className="flex space-x-4 pt-4">
          <Button type="submit" className="px-8">
            Continue
          </Button>
          <Button type="button" variant="outline" onClick={() => form.reset()}>
            Reset
          </Button>
        </div>
      </form>
    </Form>
  );
}
