"use client"

import CopyTooltip from "@/app/components/copy-tooltip"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { NATIVE_SYMBOL } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash } from "@/helpers/format"
import { ChevronDown, ChevronUp, ExternalLink } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface StateChange {
  address: string
  before: {
    value: string
    nonce?: number
  }
  after: {
    value: string
    nonce?: number
  }
  stateDifference?: string
}

interface StorageChange {
  storageAddress: string
  before: string
  after: string
}

interface StateViewerProps {
  stateChanges: StateChange[]
  storageChanges?: StorageChange[]
}

export function StateViewer({
  stateChanges,
  storageChanges = [],
}: StateViewerProps) {
  const [expandedStorage, setExpandedStorage] = useState<boolean>(false)

  if (!stateChanges || stateChanges.length === 0) {
    return (
      <Card className="border rounded-lg p-6">
        <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
          <div className="h-12 w-12 mb-4 opacity-50 flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-layers"
            >
              <path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z" />
              <path d="m22 12.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 12.5" />
              <path d="m22 17.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 17.5" />
            </svg>
          </div>
          <h3 className="text-lg font-medium mb-2">
            No state changes found for this transaction
          </h3>
          <p className="text-gray-500 max-w-md mx-auto">
            This transaction did not modify any state or the state changes are
            not available.
          </p>
        </div>
      </Card>
    )
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="card">
      <Card className="border rounded-lg overflow-hidden">
        <div className="p-4 bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-muted-foreground"
              >
                <path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z" />
                <path d="m22 12.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 12.5" />
                <path d="m22 17.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 17.5" />
              </svg>
              <h2 className="text-lg font-semibold">State Changes</h2>
            </div>
            <Badge
              variant="outline"
              className="bg-blue-50 text-blue-700 border-blue-200"
            >
              {stateChanges.length} Address
              {stateChanges.length !== 1 ? "es" : ""}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            A set of information that represents the current state is updated
            when a transaction takes place on the network. The below is a
            summary of those changes:
          </p>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/4">Address</TableHead>
                <TableHead>Before</TableHead>
                <TableHead>After</TableHead>
                <TableHead>State Difference</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {stateChanges.map((change, index) => (
                <TableRow key={`state-change-${index}`}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Link
                              href={PATH_ROUTER.ADDRESS_DETAIL(change.address)}
                              className="text-blue-600 hover:underline"
                            >
                              {formatHash(change.address, 10, 8)}
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{change.address}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <CopyTooltip content={change.address} />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        asChild
                      >
                        <Link href={PATH_ROUTER.ADDRESS_DETAIL(change.address)}>
                          <ExternalLink className="h-3.5 w-3.5" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono text-sm">
                      {change.before.value} {NATIVE_SYMBOL}
                      {change.before.nonce !== undefined && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Nonce: {change.before.nonce}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono text-sm">
                      {change.after.value} {NATIVE_SYMBOL}
                      {change.after.nonce !== undefined && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Nonce: {change.after.nonce}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {change.stateDifference && (
                      <div
                        className={`font-mono text-sm ${
                          change.stateDifference.startsWith("+")
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {change.stateDifference}
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {storageChanges.length > 0 && (
          <>
            <Separator />
            <div className="p-4">
              <Button
                variant="ghost"
                className="flex items-center gap-2 w-full justify-between"
                onClick={() => setExpandedStorage(!expandedStorage)}
              >
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    Storage ({storageChanges.length})
                  </span>
                </div>
                {expandedStorage ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>

              {expandedStorage && (
                <div className="mt-4">
                  {storageChanges.map((storage, index) => (
                    <div
                      key={`storage-${index}`}
                      className="mb-4 border rounded-md p-4"
                    >
                      <div className="mb-2">
                        <span className="font-medium">Storage Address:</span>
                        <span className="ml-2 font-mono text-sm">
                          {storage.storageAddress}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm font-medium mb-1">
                            Before:
                          </div>
                          <div className="bg-muted p-2 rounded font-mono text-xs break-all">
                            {storage.before}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-1">After:</div>
                          <div className="bg-muted p-2 rounded font-mono text-xs break-all">
                            {storage.after}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </Card>
    </div>
  )
}
