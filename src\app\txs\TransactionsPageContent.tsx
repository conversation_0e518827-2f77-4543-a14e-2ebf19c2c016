"use client"

import { TransactionsTable } from "@/components/transaction-table"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import { PATH_ROUTER } from "@/constants/routers"
import { useTransactionList } from "@/hooks/useTransactions"
import { AlertCircle } from "lucide-react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { numericFormatter } from "react-number-format"

export default function TransactionsPageContent() {
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const blockNumber = Number(searchParams.get("block")) || 0
  const limit = 25

  const { data, isLoading, isError, error } = useTransactionList({
    page,
    limit,
    fromBlock: blockNumber > 0 ? blockNumber : undefined,
    toBlock: blockNumber > 0 ? blockNumber : undefined,
  })

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Transactions
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link
                href={`${PATH_ROUTER.TRANSACTIONS}?page=1${
                  blockNumber ? `&block=${blockNumber}` : ""
                }`}
              >
                Try Again
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          {blockNumber > 0
            ? `Transactions for Block #${blockNumber}`
            : "Transactions"}
        </h1>
        <div className="text-sm text-muted-foreground">
          {!isLoading && data?.result?.metadata ? (
            <>
              More than{" "}
              {numericFormatter(`${data?.result.metadata.total}`, {
                thousandSeparator: true,
                decimalScale: 0,
              })}{" "}
              {data?.result.metadata.total === 1 ? 'transaction' : 'transactions'} found
            </>
          ) : (
            <Skeleton className="h-5 w-40" />
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="card mb-4">
          <div className="border rounded-lg">
            <LoadingTransactionsTable />
          </div>
        </div>
      ) : !data?.result.data || data?.result.data.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg mb-4">
          <h3 className="text-lg font-semibold">No Transactions Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            {blockNumber > 0
              ? `There are no transactions for block #${blockNumber}.`
              : "There are no transactions available to display."}
          </p>
        </div>
      ) : (
        <div className="card mb-4">
          <div className="border rounded-lg">
            <TransactionsTable
              transactions={data?.result.data}
              isLoading={false}
              mapNameTag={data?.mapNameTag}
            />
          </div>
        </div>
      )}

      {!isLoading && data?.result.metadata && (
        <PaginationWithLinks
          page={page}
          pageSize={limit}
          totalCount={data?.result.metadata.total || 0}
          baseUrl={PATH_ROUTER.TRANSACTIONS}
          pageSearchParam="page"
        />
      )}
    </div>
  )
}

function LoadingTransactionsTable() {
  return (
    <div className="w-full overflow-auto">
      <table className="w-full caption-bottom text-sm">
        <thead className="[&_tr]:border-b">
          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Txn Hash
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Method
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Block
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Age
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              From
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              To
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              Value
            </th>
            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              Fee
            </th>
          </tr>
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {Array.from({ length: 10 }).map((_, index) => (
            <tr
              key={index}
              className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
            >
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-32" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-20" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-16" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-20" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-32" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-32" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-5 w-20 ml-auto" />
              </td>
              <td className="p-4 align-middle text-right">
                <Skeleton className="h-5 w-16 ml-auto" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
