"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { InfoIcon, Loader2 } from "lucide-react"
import { useForm } from "react-hook-form"
import { toast } from "react-toastify"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

import { useRequestNames } from "@/hooks/useRequestTagName"
import {
  type AddNameTagFormValues,
  addNameTagSchema,
} from "@/lib/validations/contact-form-schemas"

export function AddNameTagForm() {
  const form = useForm<AddNameTagFormValues>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(addNameTagSchema),
    defaultValues: {
      name: "",
      email: "",
      companyName: "",
      companyWebsite: "",
      // isYourAddress: "",
      // smartContract: "",
      suggestedNameTag: "",
      website: "",
      categoryLabel: "",
      shortDescription: "",
      additionalComment: "",
    },
  })

  const { mutate, isPending } = useRequestNames()

  const onSubmit = (data: AddNameTagFormValues) => {
    // Transform form data to match the RequestNamesPayload structure
    const payload = {
      requesterName: data.name,
      requesterEmail: data.email,
      companyName: data.companyName,
      companyWebsite: data.companyWebsite,
      discoverAddress: data.smartContract,
      ownership:
        data.isYourAddress === "personal"
          ? "owner"
          : ("not_owner" as "owner" | "not_owner"),
      reasonDiscover: data.additionalComment || "Name tag request",
      addressSigner: data.smartContract, // Using the same address as addressSigner
      items: [
        {
          address: data.smartContract,
          name: data.suggestedNameTag,
          website: data.website,
          category: data.categoryLabel || "General",
          description: data.shortDescription,
        },
      ],
    }

    mutate(payload, {
      onSuccess: (response) => {
        toast.success("Name tag request submitted successfully!")
        form.reset()
      },
      onError: (error) => {
        toast.error(`Error: ${error.message}`)
      },
    })
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Alert className="bg-blue-50 border-blue-200 text-blue-800">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>To tag an address for private use:</strong>
            <br />
            Log in to your Helios account, navigate to the address page and
            click on "Update?" located next to the "My Name Tag" section.
            <br />
            <br />
            <strong>To update token information:</strong>
            <br />
            Log in to your Helios account and proceed to our token update page:{" "}
            <a
              href="https://explorer.helioschainlabs.org/tokenupdate"
              className="text-blue-600 hover:underline"
            >
              https://explorer.helioschainlabs.org/tokenupdate
            </a>
            <br />
            If you do not have an account, please sign up here:{" "}
            <a
              href="https://explorer.helioschainlabs.org/register"
              className="text-blue-600 hover:underline"
            >
              https://explorer.helioschainlabs.org/register
            </a>
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="Your name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Email Address <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="companyName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Company Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="Your company" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="companyWebsite"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Company Website <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="https://yourcompany.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="isYourAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Is this your address or someone else's address?{" "}
                <span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="personal">
                    This is my personal/project address
                  </SelectItem>
                  <SelectItem value="other">
                    This is someone else's address
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="pt-4">
          <h3 className="text-lg font-medium mb-4">Name Tagging</h3>

          <FormField
            control={form.control}
            name="smartContract"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel>
                  Smart Contract / Address{" "}
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="0x..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="suggestedNameTag"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Suggested Name Tag <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. Uniswap: Router" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Website <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="categoryLabel"
            render={({ field }) => (
              <FormItem className="mt-4">
                <FormLabel>Category Label</FormLabel>
                <FormControl>
                  <Input placeholder="e.g. DeFi" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="shortDescription"
            render={({ field }) => (
              <FormItem className="mt-4">
                <FormLabel>
                  Short Description <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Brief description of the contract or address"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="mt-4">
            <Button
              type="button"
              variant="link"
              className="text-blue-600 p-0 h-auto"
              onClick={() => {
                // Logic to add more name tagging fields
                alert("Add more name tagging functionality would go here")
              }}
            >
              <InfoIcon className="h-4 w-4 mr-1" />
              Add more name tagging
            </Button>
          </div>
        </div>

        <FormField
          control={form.control}
          name="additionalComment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Comment</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Any additional information you'd like to provide"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isPending}>
          {isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            "Submit Request"
          )}
        </Button>
      </form>
    </Form>
  )
}
