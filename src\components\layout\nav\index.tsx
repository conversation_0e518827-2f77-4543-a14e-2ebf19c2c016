import { Button } from "@/components/ui/button"
import clsx from "clsx"
import { X } from "lucide-react"
import { useEffect, useRef } from "react"
import { NavItem } from "./item"
import { list } from "./list"
import s from "./nav.module.scss"

interface NavProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}

export const Nav = ({ isOpen, setIsOpen }: NavProps) => {
  const navRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, setIsOpen])

  return (
    <nav ref={navRef} className={clsx(s.nav, isOpen && s.open)}>
      <ul className={s.list}>
        {list.map((item) => (
          <NavItem
            key={item.label}
            {...item}
            setIsOpen={setIsOpen}
            isOpen={isOpen}
          />
        ))}
        <Button
          variant="outline"
          className={s.close}
          onClick={() => setIsOpen(false)}
          size="sm"
        >
          Close
          <X className="w-4 h-4" />
        </Button>
      </ul>
    </nav>
  )
}
