"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, formatTimestamp, hexToDecimal } from "@/helpers/format"
import { useBlockDetails, useGetBlockByHash } from "@/hooks/useBlocks"
import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  CuboidIcon as CubeIcon,
} from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { numericFormatter } from "react-number-format"

export default function BlockPage() {
  const params = useParams()
  const id = params.id as string
  const isBlockHash = id.startsWith("0x") && id.length > 18

  const {
    data: blockData,
    isLoading,
    isError,
    error,
  } = isBlockHash ? useGetBlockByHash(id) : useBlockDetails(id)

  if (isLoading) {
    return <BlockSkeleton />
  }

  if (isError) {
    return (
      <Card className="container mx-auto px-4 py-8">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">Error Loading Block Data</h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link href={PATH_ROUTER.BLOCKS}>Return to Blocks</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!blockData) {
    return (
      <Card className="container mx-auto px-4 py-8">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <CubeIcon className="h-10 w-10 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold">Block Not Found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              No block data found for ID: {id}
            </p>
            <Button asChild className="mt-4">
              <Link href={PATH_ROUTER.BLOCKS}>Return to Blocks</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const blockNumber = hexToDecimal(blockData.number)
  const prevBlockNumber = Number(blockNumber) - 1
  const nextBlockNumber = Number(blockNumber) + 1

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-2 mb-6">
        <CubeIcon className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Block #{blockNumber}</h1>
      </div>

      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="outline"
          size="icon"
          asChild
          disabled={prevBlockNumber < 0}
        >
          <Link href={PATH_ROUTER.BLOCK_DETAIL(prevBlockNumber)}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Previous Block</span>
          </Link>
        </Button>
        <Button variant="outline" size="icon" asChild>
          <Link href={PATH_ROUTER.BLOCK_DETAIL(nextBlockNumber)}>
            <ArrowRight className="h-4 w-4" />
            <span className="sr-only">Next Block</span>
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <div className="card mb-4">
            <div className="border rounded-lg">
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium w-1/4">
                      Block Height:
                    </TableCell>
                    <TableCell>{blockNumber}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Timestamp:</TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="cursor-default">
                            {formatTimestamp(blockData?.timestamp)}
                          </TooltipTrigger>
                          <TooltipContent>{blockData?.timestamp}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>
                  {blockData.epochNumber && (
                    <TableRow>
                      <TableCell className="font-medium">
                        Proposed On:
                      </TableCell>
                      <TableCell>
                        Epoch{" "}
                        <Link
                          href={PATH_ROUTER.EPOCH_DETAIL(blockData.epochNumber)}
                          className="text-blue-700 hover:underline"
                        >
                          {blockData.epochNumber}
                        </Link>
                      </TableCell>
                    </TableRow>
                  )}
                  <TableRow>
                    <TableCell className="font-medium">Transactions:</TableCell>
                    <TableCell>
                      <Link
                        href={`${PATH_ROUTER.TRANSACTIONS}?block=${blockData.number}`}
                        className="text-blue-700 hover:underline"
                      >
                        {blockData.transactionCount} transactions
                      </Link>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Miner:</TableCell>
                    <TableCell>
                      <Link
                        href={PATH_ROUTER.ADDRESS_DETAIL(blockData.miner)}
                        className="text-primary hover:underline"
                      >
                        {formatHash(blockData.miner)}
                      </Link>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Gas Used:</TableCell>
                    <TableCell>
                      {blockData?.gasUsed
                        ? numericFormatter(hexToDecimal(blockData?.gasUsed), {
                            thousandSeparator: true,
                            decimalScale: 0,
                          })
                        : ""}{" "}
                      (
                      {(
                        (Number(hexToDecimal(blockData.gasUsed)) /
                          Number(hexToDecimal(blockData.gasLimit))) *
                        100
                      ).toFixed(2)}
                      %)
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Gas Limit:</TableCell>
                    <TableCell>
                      {numericFormatter(hexToDecimal(blockData?.gasLimit), {
                        thousandSeparator: true,
                        decimalScale: 0,
                      })}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Hash:</TableCell>
                    <TableCell className="break-all">
                      {formatHash(blockData.hash)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      Transactions Root:
                    </TableCell>
                    <TableCell className="break-all">
                      {blockData.transactionsRoot}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Parent Hash:</TableCell>
                    <TableCell className="break-all">
                      <Link
                        href={PATH_ROUTER.BLOCK_DETAIL(prevBlockNumber)}
                        className="text-blue-700 hover:underline"
                      >
                        {formatHash(blockData.parentHash)}
                      </Link>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Nonce:</TableCell>
                    <TableCell>{blockData.nonce}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Difficulty:</TableCell>
                    <TableCell>{hexToDecimal(blockData.difficulty)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      Total Difficulty:
                    </TableCell>
                    <TableCell>
                      {hexToDecimal(blockData.totalDifficulty)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Size:</TableCell>
                    <TableCell>{hexToDecimal(blockData.size)} bytes</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

function BlockSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-2 mb-6">
        <Skeleton className="h-6 w-6 rounded" />
        <Skeleton className="h-8 w-48" />
      </div>

      <div className="flex items-center gap-2 mb-6">
        <Skeleton className="h-10 w-10 rounded" />
        <Skeleton className="h-10 w-10 rounded" />
      </div>

      <div className="w-full">
        <Skeleton className="h-10 w-24 mb-4" />
        <div className="border rounded-lg">
          <Table>
            <TableBody>
              {Array.from({ length: 12 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell className="w-1/4">
                    <Skeleton className="h-5 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-full max-w-md" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
