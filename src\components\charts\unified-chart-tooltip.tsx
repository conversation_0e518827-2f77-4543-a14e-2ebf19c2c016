"use client"

import { NATIVE_SYMBOL } from "@/constants"
import { formatNumberWithNotation } from "@/helpers/format"
import { type ChartType, ChartTypeEnum, getLabelForChartType } from "@/hooks/useChart"
import BigNumber from "bignumber.js"
import { format } from "date-fns"

interface UnifiedChartTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
  chartType: ChartType
  data?: any
}

export default function UnifiedChartTooltip({ active, payload, label, chartType, data }: UnifiedChartTooltipProps) {
  if (!active || !payload || !payload.length || !label) {
    return null
  }

  const dataPoint = payload[0].payload
  const date = new Date(label)
  const formattedDate = format(date, "EEEE, MMMM d, yyyy")
  const chartLabel = getLabelForChartType(chartType)
  const value = payload[0].value

  // Format value based on chart type
  const formattedValue = (() => {
    switch (chartType) {
      case ChartTypeEnum.AVG_BLOCK_TIME:
        return `${value.toFixed(2)}`
      case ChartTypeEnum.AVG_BLOCK_SIZE:
        return typeof value === "string" ? value : value.toLocaleString()
      case ChartTypeEnum.DAILY_TXN:
        return value.toLocaleString()
      case ChartTypeEnum.ERC20_TOKEN_TRANSFER:
        return value.toLocaleString()
      case ChartTypeEnum.AVG_GAS_PRICE:
        return `${value.toFixed(2)} Gwei`
      case ChartTypeEnum.BLOCK_COUNT_AND_REWARD:
        return value.toLocaleString()
      case ChartTypeEnum.DAILY_VERIFIED_CONTRACT:
      case ChartTypeEnum.DAILY_DEPLOYED_CONTRACT:
        return value.toLocaleString()
      case ChartTypeEnum.AVG_PENDING_TRANSACTION:
        return value.toLocaleString()
      case ChartTypeEnum.EIP1559_METRICS:
        return `${value.toFixed(2)} Gwei`
      case ChartTypeEnum.CONTRACT_STATS:
        return value.toLocaleString()
      default:
        return value.toLocaleString()
    }
  })()

  const formatBlockRewards = (rewardsValue: string): string => {
    try {
      const bn = new BigNumber(rewardsValue)
      const ethValue = bn.dividedBy(new BigNumber(10).pow(18))
      return ethValue.toFixed(4)
    } catch (error) {
      console.error("Error formatting block rewards:", error)

      if (typeof formatNumberWithNotation === "function") {
        const formatted = formatNumberWithNotation(rewardsValue, 4)
        return formatted || "0"
      }

      return "0"
    }
  }

  // Render additional data based on chart type
  const renderAdditionalData = () => {
    if ((chartType === ChartTypeEnum.DAILY_TXN || chartType === ChartTypeEnum.ERC20_TOKEN_TRANSFER) && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          {dataPoint.avgBlockTime && <div>Avg Block Time: {dataPoint.avgBlockTime.toFixed(2)}</div>}
          {dataPoint.avgBlockSize && <div>Avg Block Size: {Number(dataPoint.avgBlockSize).toLocaleString()}</div>}
          {dataPoint.blockCount && <div>Total Block Count: {dataPoint.blockCount.toLocaleString()}</div>}
          {dataPoint.newAddressSeen !== undefined && (
            <div>New Address Seen: {dataPoint.newAddressSeen.toLocaleString()}</div>
          )}
        </div>
      )
    }

    if (chartType === ChartTypeEnum.BLOCK_COUNT_AND_REWARD && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          <div>Block Count: {dataPoint.blockCount.toLocaleString()}</div>
          {dataPoint.blockRewards && (
            <div>
              Block Rewards: {formatBlockRewards(dataPoint.blockRewards)} {NATIVE_SYMBOL}
            </div>
          )}
        </div>
      )
    }

    if (chartType === ChartTypeEnum.ACTIVE_ADDRESS && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          <div>Active Addresses: {dataPoint.addressActive.toLocaleString()}</div>
          <div>Receive from: {dataPoint.addressReceive.toLocaleString()}</div>
          <div>Send to: {dataPoint.addressSend.toLocaleString()}</div>
        </div>
      )
    }

    if (chartType === ChartTypeEnum.DAILY_VERIFIED_CONTRACT && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          {dataPoint.totalVerified && <div>Total Verified: {dataPoint.totalVerified.toLocaleString()}</div>}
          {dataPoint.verificationRate && <div>Verification Rate: {dataPoint.verificationRate.toFixed(2)}%</div>}
        </div>
      )
    }

    if (chartType === ChartTypeEnum.DAILY_DEPLOYED_CONTRACT && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          {dataPoint.totalDeployed && <div>Total Deployed: {dataPoint.totalDeployed.toLocaleString()}</div>}
          {dataPoint.deploymentGas && <div>Avg Deployment Gas: {dataPoint.deploymentGas.toLocaleString()}</div>}
        </div>
      )
    }

    if (chartType === ChartTypeEnum.AVG_PENDING_TRANSACTION && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          {dataPoint.avgGasPrice && <div>Avg Gas Price: {dataPoint.avgGasPrice.toFixed(2)} Gwei</div>}
          {dataPoint.avgWaitTime && <div>Avg Wait Time: {dataPoint.avgWaitTime.toFixed(2)} sec</div>}
          {dataPoint.totalTxns && <div>Total Txns: {dataPoint.totalTxns.toLocaleString()}</div>}
        </div>
      )
    }

    if (chartType === ChartTypeEnum.EIP1559_METRICS && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          {dataPoint.baseFee && <div>Base Fee: {dataPoint.baseFee.toFixed(2)} Gwei</div>}
          {dataPoint.priorityFee && <div>Priority Fee: {dataPoint.priorityFee.toFixed(2)} Gwei</div>}
          {dataPoint.burntFees && (
            <div>
              Burnt Fees: {formatBlockRewards(dataPoint.burntFees)} {NATIVE_SYMBOL}
            </div>
          )}
        </div>
      )
    }

    if (chartType === ChartTypeEnum.CONTRACT_STATS && dataPoint) {
      return (
        <div className="mt-2 space-y-1 text-xs text-muted-foreground">
          {dataPoint.totalContracts && <div>Total Contracts: {dataPoint.totalContracts.toLocaleString()}</div>}
          {dataPoint.verifiedPercentage && <div>Verified: {dataPoint.verifiedPercentage.toFixed(2)}%</div>}
          {dataPoint.dailyInteractions && <div>Daily Interactions: {dataPoint.dailyInteractions.toLocaleString()}</div>}
        </div>
      )
    }

    return null
  }

  return (
    <div className="bg-background/95 border border-border rounded-md shadow-md p-3 text-sm">
      <div className="font-medium text-foreground mb-1">{formattedDate}</div>
      <div className="text-primary font-medium">
        {chartLabel}: {formattedValue}
      </div>
      {renderAdditionalData()}
    </div>
  )
}
