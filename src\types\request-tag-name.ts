export interface RequestNameItem {
  address: string
  name: string
  website: string
  category: string
  description: string
}

export interface RequestNamesPayload {
  requesterName: string
  requesterEmail: string
  companyName: string
  companyWebsite: string
  discoverAddress: string
  ownership: "owner" | "not_owner"
  reasonDiscover: string
  addressSigner: string
  items: RequestNameItem[]
}

export interface RequestNameResponse {
  status: string
  requesterName: string
  requesterEmail: string
  companyName: string
  companyWebsite: string
  discoverAddress: string
  ownership: string
  reasonDiscover: string
  addressSigner: string
  signature: string | null
  messageSign: string | null
  address: string
  name: string
  website: string
  category: string
  description: string
  id: number
  createdAt: string
  updatedAt: string
}

export interface RequestNamesResponse {
  statusCode: number
  data: RequestNameResponse[]
  metadata: {
    timestamp: string
  }
  success: boolean
  message: string
}
