"use client"

import { useParams } from "next/navigation"
import { NFTDetailContainer } from "./components/nft-detail-container"

export default function NFTDetailPage() {
  const params = useParams()
  const contractAddress = params.contractAddress as string
  const tokenId = params.tokenId as string

  return (
    <div className="container mx-auto px-4 py-8">
      <NFTDetailContainer contractAddress={contractAddress} tokenId={tokenId} />
    </div>
  )
}
