"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { useEffect, useRef, useState } from "react"
import {
  blockchainDataCharts,
  contractDataCharts,
  dashboardCharts,
  marketDataCharts,
  networkDataCharts,
  statisticsCharts,
} from "./chart-config"
import ChartThumbnail from "./chart-thumnail"

const sections = [
  // { id: "market-data", title: "Market Data", charts: marketDataCharts },
  {
    id: "blockchain-data",
    title: "Blockchain Data",
    charts: blockchainDataCharts,
  },
  { id: "dashboards", title: "Dashboards", charts: dashboardCharts },
  { id: "network-data", title: "Network Data", charts: networkDataCharts },
  { id: "top-statistics", title: "Top Statistics", charts: statisticsCharts },
  { id: "contracts", title: "Contracts", charts: contractDataCharts },
]

export default function HeliosCharts() {
  const [activeSection, setActiveSection] = useState("market-data")
  const sectionRefs = useRef<Record<string, HTMLDivElement | null>>({})

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId)
    const sectionElement = sectionRefs.current[sectionId]
    if (sectionElement) {
      const yOffset = -80 // Adjust for header height
      const y =
        sectionElement.getBoundingClientRect().top +
        window.pageYOffset +
        yOffset
      window.scrollTo({ top: y, behavior: "smooth" })
    }
  }

  // Set up intersection observer to update active section on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const sectionId = entry.target.id
            setActiveSection(sectionId)
          }
        })
      },
      { rootMargin: "-100px 0px -80% 0px" },
    )

    Object.values(sectionRefs.current).forEach((ref) => {
      if (ref) observer.observe(ref)
    })

    return () => {
      Object.values(sectionRefs.current).forEach((ref) => {
        if (ref) observer.unobserve(ref)
      })
    }
  }, [])

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 bg-background">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-6">
            Helios Charts & Statistics
          </h1>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Sidebar Navigation */}
            <aside className="md:w-64 flex-shrink-0">
              <div className="sticky top-20">
                <nav className="space-y-1">
                  {sections.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => scrollToSection(section.id)}
                      className={cn(
                        "w-full text-left px-3 py-2 rounded-md text-sm font-medium",
                        activeSection === section.id
                          ? "bg-primary/10 text-primary"
                          : "text-muted-foreground hover:bg-primary/5 hover:text-primary",
                      )}
                    >
                      {section.title}
                    </button>
                  ))}
                </nav>
              </div>
            </aside>

            {/* Main Content */}
            <div className="flex-1 space-y-12">
              {sections.map((section) => (
                <section
                  key={section.id}
                  id={section.id}
                  ref={(el: HTMLDivElement | null) => {
                    sectionRefs.current[section.id] = el
                  }}
                  className="scroll-mt-20 "
                >
                  <h2 className="text-xl font-semibold mb-6">
                    {section.title}
                  </h2>

                  {section?.charts?.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {section.charts.map((chart) => (
                        <Link
                          key={chart.id}
                          href={`/charts/${chart.id}`}
                          className="block transition-transform hover:scale-[1.02] "
                        >
                          <div className="card">
                            <Card className="overflow-hidden border bg-card/50 hover:bg-card/80 cursor-pointer">
                              <CardContent className="p-0">
                                <div className="p-4 border-b border-border/50">
                                  <h3 className="text-sm font-medium">
                                    {chart.title}
                                  </h3>
                                </div>
                                <div className="h-[150px] w-full">
                                  <ChartThumbnail
                                    chartId={chart.id}
                                    chartType={chart.type}
                                    color={chart.color}
                                  />
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground text-sm">
                      No charts available in this section yet.
                    </div>
                  )}
                </section>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
