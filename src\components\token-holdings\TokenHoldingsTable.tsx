"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatWeiToEther, truncateAddress } from "@/helpers/format"
import type { TokenBalance } from "@/types/tokens"
import { ExternalLink, MoreHorizontal } from "lucide-react"
import Link from "next/link"

interface TokenHoldingsTableProps {
  holdings: TokenBalance[]
  showEthValue?: boolean
}

export function TokenHoldingsTable({
  holdings,
  showEthValue = false,
}: TokenHoldingsTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Asset</TableHead>
          <TableHead>Symbol</TableHead>
          <TableHead>Contract Address</TableHead>
          <TableHead className="text-right">Quantity</TableHead>
          <TableHead className="text-right">Value</TableHead>
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {holdings.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-6">
              No tokens found
            </TableCell>
          </TableRow>
        ) : (
          holdings.map((token, index) => (
            <TableRow key={index}>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs">
                    {token.symbol.charAt(0)}
                  </div>
                  <span>{token.description || token.symbol}</span>
                </div>
              </TableCell>
              <TableCell>{token.symbol}</TableCell>
              <TableCell>
                <Link
                  href={PATH_ROUTER.TOKEN_DETAIL(token.address)}
                  className="text-primary hover:underline flex items-center gap-1"
                >
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="text-blue-700 hover:underline">
                        {truncateAddress(token.address)}
                      </TooltipTrigger>
                      <TooltipContent>{token.address}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <ExternalLink className="h-3 w-3" />
                </Link>
              </TableCell>
              <TableCell className="text-right font-mono">
                {formatWeiToEther(token.balance)}
              </TableCell>
              <TableCell className="text-right">
                {showEthValue ? (
                  <span className="font-mono">♦0</span>
                ) : (
                  <span className="font-mono">0</span>
                )}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={PATH_ROUTER.TOKEN_DETAIL(token.address)}>
                        View Token Info
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        href={`${PATH_ROUTER.TOKEN_DETAIL(
                          token.address,
                        )}/transfers`}
                      >
                        View Token Transfers
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        href={`${PATH_ROUTER.TOKEN_DETAIL(
                          token.address,
                        )}/analytics`}
                      >
                        View Analytics
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
