"use client"

import type React from "react"

import CopyTooltip from "@/app/components/copy-tooltip"
import { CSVExportButton } from "@/components/csv-button-export"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { DEFAULT_TOKEN_IMAGE } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatAge,
  formatHash,
  formatWeiToEther,
  normalizeAddress,
} from "@/helpers/format"
import { useGetTransferTokenEventByAddress } from "@/hooks/useTokens"
import { formatTokenTransferEventsForCSV } from "@/lib/utils/csv-export"
import Image from "next/image"
import { useRouter } from "next/navigation"

interface TokenTransfersTableProps {
  address: string
  loadingFallback?: React.ReactNode
}

export function TokenTransfersTable({
  address,
  loadingFallback,
}: TokenTransfersTableProps) {
  const router = useRouter()

  const { data: transferEvents, isLoading } = useGetTransferTokenEventByAddress(
    null,
    address,
    1,
    25,
  )

  if (isLoading && loadingFallback) {
    return <>{loadingFallback}</>
  }

  const getTransactionDirection = (transfer: any) => {
    const normalizedAddress = normalizeAddress(address)
    const normalizedTo = normalizeAddress(transfer?.to)

    if (normalizedAddress === normalizedTo) {
      return "in"
    }
    return "out"
  }

  const getTokenLogoUrl = (tokenDetails: any) => {
    if (tokenDetails?.logo) {
      return `https://testnet1-cdn.helioschainlabs.org/hash/${tokenDetails.logo}`
    }
    return DEFAULT_TOKEN_IMAGE
  }

  const renderTokenLogo = (tokenDetails: any) => {
    const logoUrl = getTokenLogoUrl(tokenDetails)

    return (
      <Image
        src={logoUrl || "/placeholder.svg"}
        alt={tokenDetails?.symbol || "Token"}
        width={20}
        height={20}
        className="rounded-full"
        onError={(e) => {
          const target = e.target as HTMLImageElement
          target.src = DEFAULT_TOKEN_IMAGE || "/placeholder.svg"
        }}
      />
    )
  }

  return (
    <div>
      <div className="m-4 flex items-center justify-between">
        <div className="text-sm text-muted-foreground flex items-center gap-2">
          Latest {transferEvents?.data?.length || 0} HRC-20 Token Transfer
          Events
          <span
            className="text-blue-600 hover:underline cursor-pointer"
            onClick={() => {
              router.push(`${PATH_ROUTER.TOKEN_TRANSFERS}?a=${address}`)
            }}
          >
            (View All)
          </span>
        </div>
        <CSVExportButton
          data={transferEvents?.data ?? []}
          formatter={formatTokenTransferEventsForCSV}
          filename={`token-${address}-transfers.csv`}
          disabled={isLoading || !transferEvents?.data?.length}
        />
      </div>
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]"></TableHead>
              <TableHead>Transaction Hash</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Block</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>From</TableHead>
              <TableHead>To</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead>Token</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transferEvents?.data?.map((transfer, index) => {
              const direction = getTransactionDirection(transfer)
              const tokenDetails = transfer.tokenDetails
              console.log("tokenDetails", tokenDetails)
              return (
                <TableRow key={`${transfer?.transactionHash}-${index}`}>
                  <TableCell>
                    <div className="flex items-center justify-center">
                      <div className="h-4 w-4 rounded-full border border-gray-300"></div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <CopyTooltip content={transfer?.transactionHash} />
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline"
                            onClick={() => {
                              router.push(
                                PATH_ROUTER.TRANSACTION_DETAIL(
                                  transfer?.transactionHash,
                                ),
                              )
                            }}
                          >
                            {formatHash(transfer?.transactionHash, 6, 6)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {transfer?.transactionHash}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-xs px-2 py-1 bg-gray-100 rounded-md inline-block">
                      Transfer
                    </div>
                  </TableCell>
                  <TableCell>
                    <div
                      className="text-blue-700 hover:underline cursor-pointer"
                      onClick={() => {
                        router.push(
                          PATH_ROUTER.BLOCK_DETAIL(transfer?.blockNumber),
                        )
                      }}
                    >
                      {transfer?.blockNumber}
                    </div>
                  </TableCell>
                  <TableCell>{formatAge(transfer?.timestamp)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline"
                            onClick={() => {
                              router.push(
                                PATH_ROUTER.ADDRESS_DETAIL(normalizeAddress(
                                  transfer?.from,
                                ) || transfer?.from),
                              )
                            }}
                          >
                            {formatHash(normalizeAddress(transfer?.from), 6, 4)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {normalizeAddress(transfer?.from)}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <CopyTooltip content={normalizeAddress(address) ?? ""} />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="outline"
                        className={`px-2 py-0.5 text-xs ${
                          direction === "in"
                            ? "bg-green-50 text-green-700 border-green-200"
                            : "bg-amber-50 text-amber-700 border-amber-200"
                        }`}
                      >
                        {direction === "in" ? "IN" : "OUT"}
                      </Badge>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline"
                            onClick={() => {
                              router.push(
                                PATH_ROUTER.ADDRESS_DETAIL(normalizeAddress(
                                  transfer?.to,
                                ) || transfer?.to),
                              )
                            }}
                          >
                            {formatHash(normalizeAddress(transfer?.to), 6, 4)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {normalizeAddress(transfer?.to)}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <CopyTooltip
                        content={normalizeAddress(transfer?.to) ?? ""}
                      />
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {formatWeiToEther(transfer?.amount || transfer?.value)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {renderTokenLogo(tokenDetails)}
                      <div className="flex flex-col">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              className="text-blue-700 hover:underline text-left"
                              onClick={() => {
                                if (tokenDetails?.address) {
                                  router.push(
                                    PATH_ROUTER.TOKEN_DETAIL(tokenDetails.address),
                                  )
                                }
                              }}
                            >
                              {tokenDetails?.name || "Unknown Token"}
                            </TooltipTrigger>
                            <TooltipContent>
                              <div>
                                <div>
                                  Token: {tokenDetails?.name || "Unknown"}
                                </div>
                                <div>
                                  Symbol: {tokenDetails?.symbol || "N/A"}
                                </div>
                                <div>
                                  Address: {tokenDetails?.address || "N/A"}
                                </div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <span className="text-gray-500 text-xs">
                          ({tokenDetails?.symbol || "N/A"})
                        </span>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}

            {(!transferEvents?.data || transferEvents.data.length === 0) && (
              <TableRow>
                <TableCell
                  colSpan={9}
                  className="text-center py-6 text-muted-foreground"
                >
                  No token transfers found for this address
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
