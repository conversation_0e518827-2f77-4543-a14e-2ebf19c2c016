"use client"

import { Card, CardContent } from "@/components/ui/card"
import { NATIVE_SYMBOL } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { truncateAddress } from "@/helpers/format"
import { StatsTopType } from "@/types/charts"
import { ChevronRight, ExternalLink } from "lucide-react"
import Link from "next/link"
import type React from "react"

const formatValue = (val: string) => {
  if (!val) return "-"
  const num = Number.parseFloat(val)
  if (isNaN(num)) return val
  if (num >= 1e18) return (num / 1e18).toFixed(2) + ` ${NATIVE_SYMBOL}`
  if (num >= 1e9) return (num / 1e9).toFixed(2) + " Gwei"
  return num.toLocaleString()
}

const StatRow = ({
  label,
  value,
  address,
  percentage,
  icon,
  totalLabel,
}: {
  label: string
  value: string
  address?: string
  percentage?: string
  icon?: React.ReactNode
  totalLabel?: string
}) => (
  <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
    <div className="flex items-center gap-2">
      {icon}
      <span className="text-sm text-gray-700">{label}</span>
    </div>
    <div className="flex flex-col items-end">
      <div className="flex items-center gap-1">
        {totalLabel && (
          <span className="text-xs text-gray-500">{totalLabel}</span>
        )}
        <span className="font-medium text-sm">{value}</span>
        {percentage && (
          <span className="text-xs text-gray-500">({percentage}%)</span>
        )}
      </div>
      {address && (
        <Link
          href={PATH_ROUTER.ADDRESS_DETAIL(address)}
          className="text-xs text-blue-500 hover:underline flex items-center gap-1"
          title={`${address}`}
        >
          {truncateAddress(address)}
          <ExternalLink size={12} />
        </Link>
      )}
    </div>
  </div>
)

interface ViewMoreLinkProps {
  type: StatsTopType
  onViewMore: (type: StatsTopType) => void
}

const ViewMoreLink = ({ type, onViewMore }: ViewMoreLinkProps) => (
  <div className="flex justify-end mt-2">
    <button
      onClick={() => onViewMore(type)}
      className="text-xs text-blue-500 hover:underline flex items-center gap-1"
    >
      View Top 10
      <ChevronRight size={14} />
    </button>
  </div>
)

interface OverviewStatsProps {
  data: any
  onViewMore: (type: StatsTopType) => void
}

export const OverviewStats = ({ data, onViewMore }: OverviewStatsProps) => {
  if (!data) return null

  const { transaction, token, network } = data

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <Card className="h-full">
            <CardContent className="p-0">
              <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                <h3 className="font-semibold text-gray-800">Transactions</h3>
                <ViewMoreLink
                  type={StatsTopType.TRANSACTION}
                  onViewMore={onViewMore}
                />
              </div>
              <div className="p-4">
                <StatRow
                  label={`Top ${NATIVE_SYMBOL} Sender`}
                  value={formatValue(transaction?.topEthSenders.value)}
                  address={transaction?.topEthSenders.address}
                  percentage={transaction?.topEthSenders.percentage}
                  totalLabel={`Total ${NATIVE_SYMBOL}`}
                />
                <StatRow
                  label={`Top ${NATIVE_SYMBOL} Receiver`}
                  value={formatValue(transaction?.topEthReceivers.value)}
                  address={transaction?.topEthReceivers.address}
                  percentage={transaction?.topEthReceivers.percentage}
                  totalLabel={`Total ${NATIVE_SYMBOL}`}
                />
                <StatRow
                  label="Top Txn Count Sent"
                  value={`${transaction?.topTxnSenders.count} txns`}
                  address={transaction?.topTxnSenders.address}
                  percentage={transaction?.topTxnSenders.percentage}
                  totalLabel="Total Txn"
                />
                <StatRow
                  label="Top Txn Count Received"
                  value={`${transaction?.topTxnReceivers.count} txns`}
                  address={transaction?.topTxnReceivers.address}
                  percentage={transaction?.topTxnReceivers.percentage}
                  totalLabel="Total Txn"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="card">
          <Card className="h-full">
            <CardContent className="p-0">
              <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                <h3 className="font-semibold text-gray-800">Tokens</h3>
                <ViewMoreLink
                  type={StatsTopType.TOKEN}
                  onViewMore={onViewMore}
                />
              </div>
              <div className="p-4">
                <StatRow
                  label="Top Unique Sender"
                  value={`${token?.topTokensByUniqueSenders.count} senders`}
                  address={token?.topTokensByUniqueSenders.address}
                  totalLabel="Total"
                  icon={
                    <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-xs text-blue-600">
                      T
                    </div>
                  }
                />
                <StatRow
                  label="Top Unique Receiver"
                  value={`${token?.topTokensByUniqueReceivers.count} receivers`}
                  address={token?.topTokensByUniqueReceivers.address}
                  totalLabel="Total"
                  icon={
                    <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-xs text-blue-600">
                      T
                    </div>
                  }
                />
                <StatRow
                  label="Top Total Uniques"
                  value={`${token?.topTokensByUniqueTotal.count} total users`}
                  address={token?.topTokensByUniqueTotal.address}
                  totalLabel="Total"
                  icon={
                    <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-xs text-blue-600">
                      T
                    </div>
                  }
                />
                <StatRow
                  label="Top Txn Count"
                  value={`${token?.topTokensByTxCount.count} txns`}
                  address={token?.topTokensByTxCount.address}
                  percentage={token?.topTokensByTxCount.percentage}
                  totalLabel="Txn Count"
                  icon={
                    <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-xs text-blue-600">
                      T
                    </div>
                  }
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="card lg:col-span-2">
          <Card>
            <CardContent className="p-0">
              <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                <h3 className="font-semibold text-gray-800">Network</h3>
                <ViewMoreLink
                  type={StatsTopType.NETWORK}
                  onViewMore={onViewMore}
                />
              </div>
              <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <StatRow
                  label="Top Gas Used"
                  value={formatValue(network?.topGasUsers.value)}
                  address={network?.topGasUsers.address}
                  percentage={network?.topGasUsers.percentage}
                  totalLabel="Gas Used"
                />
                <StatRow
                  label="Top Txn Count"
                  value={`${network?.topActiveAddresses.count} txns`}
                  address={network?.topActiveAddresses.address}
                  percentage={network?.topActiveAddresses.percentage}
                  totalLabel="Txn Count"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
