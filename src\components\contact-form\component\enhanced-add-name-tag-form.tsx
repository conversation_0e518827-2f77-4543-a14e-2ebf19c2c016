"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { InfoIcon, Loader2, Plus, Trash2 } from "lucide-react"
import { useForm, useFieldArray } from "react-hook-form"
import { toast } from "react-toastify"
import { useState } from "react"

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"

import { ContractAddressInput } from "./contract-address-input"
import { VerifyOwnershipDialog } from "./verify-ownership-dialog"

import { useRequestNames } from "@/hooks/useRequestTagName"
import { type AddNameTagFormValues, addNameTagSchema } from "@/lib/validations/contact-form-schemas"

const categoryOptions = [
  "DeFi",
  "NFT",
  "Gaming",
  "Exchange",
  "Bridge",
  "DAO",
  "Infrastructure",
  "Oracle",
  "Staking",
  "Lending",
  "DEX",
  "Yield Farming",
  "Insurance",
  "Analytics",
  "Wallet",
  "Other",
]

export function EnhancedAddNameTagForm() {
  const [isContract, setIsContract] = useState(false)
  const [verifiedContracts, setVerifiedContracts] = useState<Set<string>>(new Set())

  const form = useForm<AddNameTagFormValues>({
    resolver: zodResolver(addNameTagSchema),
    defaultValues: {
      name: "",
      email: "",
      companyName: "",
      companyWebsite: "",
      isYourAddress: undefined,
      smartContract: "" as `0x${string}`,
      suggestedNameTag: "",
      website: "",
      categoryLabel: "",
      shortDescription: "",
      additionalComment: "",
      additionalTags: [],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "additionalTags",
  })

  const { mutate, isPending } = useRequestNames()

  const handleVerificationComplete = (signature: string, message: string) => {
    const contractAddress = form.getValues("smartContract")
    setVerifiedContracts((prev) => new Set([...prev, contractAddress]))

    // Store verification data for submission
    form.setValue("verificationSignature", signature)
    form.setValue("verificationMessage", message)
  }

  const addAdditionalTag = () => {
    append({
      address: "" as `0x${string}`,
      nameTag: "",
      website: "",
      category: "",
      description: "",
    })
  }

  const onSubmit = (data: AddNameTagFormValues) => {
    // Check if contract verification is required but not completed
    if (isContract && data.isYourAddress === "personal" && !verifiedContracts.has(data.smartContract)) {
      toast.error("Please verify ownership of the smart contract before submitting.")
      return
    }

    // Transform form data to match the RequestNamesPayload structure
    const payload = {
      requesterName: data.name,
      requesterEmail: data.email,
      companyName: data.companyName,
      companyWebsite: data.companyWebsite,
      discoverAddress: data.smartContract,
      ownership: data.isYourAddress === "personal" ? "owner" : ("not_owner" as "owner" | "not_owner"),
      reasonDiscover: data.additionalComment || "Name tag request",
      addressSigner: data.smartContract,
      verificationSignature: data.verificationSignature,
      verificationMessage: data.verificationMessage,
      items: [
        {
          address: data.smartContract,
          name: data.suggestedNameTag,
          website: data.website,
          category: data.categoryLabel || "General",
          description: data.shortDescription,
        },
        // Add additional tags if any
        ...(data.additionalTags?.map((tag) => ({
          address: tag.address,
          name: tag.nameTag,
          website: tag.website,
          category: tag.category || "General",
          description: tag.description,
        })) || []),
      ],
    }

    mutate(payload, {
      onSuccess: (response) => {
        toast.success("Name tag request submitted successfully!")
        form.reset()
        setVerifiedContracts(new Set())
      },
      onError: (error) => {
        toast.error(`Error: ${error.message}`)
      },
    })
  }

  const watchedIsYourAddress = form.watch("isYourAddress")
  const watchedSmartContract = form.watch("smartContract")

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Alert className="bg-blue-50 border-blue-200 text-blue-800">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>Important Guidelines:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Only submit requests through this official form</li>
              <li>Avoid multiple submissions for the same address to prevent delays</li>
              <li>For project submissions, use your official project email domain</li>
              <li>All provided information must be accurate and verifiable</li>
              <li>Smart contract ownership verification may be required</li>
            </ul>
          </AlertDescription>
        </Alert>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Email Address <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Cryptokitties" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyWebsite"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Website <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="https://cryptokitties.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="isYourAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Is this your address or someone else's address? <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="personal">This is my personal/project address</SelectItem>
                      <SelectItem value="other">This is someone else's address</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Primary Name Tag */}
        <Card>
          <CardHeader>
            <CardTitle>Primary Name Tag</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="smartContract"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Smart Contract / Address <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <ContractAddressInput
                      value={field.value}
                      onChange={field.onChange}
                      onContractTypeDetected={setIsContract}
                      error={form.formState.errors.smartContract?.message}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Ownership Verification */}
            {isContract && watchedIsYourAddress === "personal" && watchedSmartContract && (
              <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-yellow-800">Ownership Verification Required</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Since you claim ownership of this smart contract, please verify your ownership by signing a
                      message.
                    </p>
                  </div>
                  <VerifyOwnershipDialog
                    contractAddress={watchedSmartContract}
                    onVerificationComplete={handleVerificationComplete}
                    isVerified={verifiedContracts.has(watchedSmartContract)}
                  />
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="suggestedNameTag"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Suggested Name Tag <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Compound Contract" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Website <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="https://www.example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="categoryLabel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Label</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categoryOptions.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="shortDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Short Description <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the contract or address function"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Additional Name Tags */}
        {fields.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Additional Name Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {fields.map((field, index) => (
                <div key={field.id} className="p-4 border rounded-lg relative">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => remove(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>

                  <div className="space-y-4 pr-10">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`additionalTags.${index}.address`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address</FormLabel>
                            <FormControl>
                              <Input placeholder="0x..." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`additionalTags.${index}.nameTag`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name Tag</FormLabel>
                            <FormControl>
                              <Input placeholder="Tag name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`additionalTags.${index}.website`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Website</FormLabel>
                            <FormControl>
                              <Input placeholder="https://..." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`additionalTags.${index}.category`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {categoryOptions.map((category) => (
                                  <SelectItem key={category} value={category}>
                                    {category}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`additionalTags.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea placeholder="Description" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        <div className="flex justify-center">
          <Button type="button" variant="outline" onClick={addAdditionalTag} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add More Name Tags
          </Button>
        </div>

        <Separator />

        <FormField
          control={form.control}
          name="additionalComment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Comment</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Any additional information you'd like to provide"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isPending}>
          {isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting Request...
            </>
          ) : (
            "Submit Name Tag Request"
          )}
        </Button>
      </form>
    </Form>
  )
}
