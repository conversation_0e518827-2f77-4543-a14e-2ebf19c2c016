"use client"

import Copy<PERSON>ooltip from "@/app/components/copy-tooltip"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatHash,
  formatWeiToEther,
  normalizeAddress,
} from "@/helpers/format"
import { useGetTransferTokenEventByAddress } from "@/hooks/useTokens"
import { AlertCircle, CircleArrowRight } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"

export default function TokenTransferContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const pageSize = Number(searchParams.get("pageSize") || "25")
  const limit = pageSize
  const addressFilter = searchParams.get("a")

  const {
    data: transfers,
    isLoading,
    isError,
    error,
  } = useGetTransferTokenEventByAddress(null, addressFilter, page, limit)

  const navigateToTransaction = (hash: string) => {
    router.push(PATH_ROUTER.TRANSACTION_DETAIL(hash))
  }

  const navigateToAddress = (address: string) => {
    router.push(PATH_ROUTER.ADDRESS_DETAIL(normalizeAddress(address) || address))
  }

  const navigateToBlock = (blockNumber: number) => {
    router.push(PATH_ROUTER.BLOCK_DETAIL(blockNumber))
  }

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Token Transfers
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link href={`${PATH_ROUTER.TOKEN_TRANSFERS}?page=1`}>
                Try Again
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          Token Transfer Events
          {addressFilter && (
            <span className="text-lg font-normal text-muted-foreground ml-2">
              for {formatHash(normalizeAddress(addressFilter), 10, 9)}
            </span>
          )}
        </h1>
        {addressFilter && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(PATH_ROUTER.TOKEN_TRANSFERS)}
          >
            Clear Filter
          </Button>
        )}
      </div>

      {isLoading ? (
        <LoadingSkeleton />
      ) : !transfers?.data ||
        !Array.isArray(transfers.data) ||
        transfers.data.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-6 text-center">
          <h3 className="text-lg font-semibold">No Token Transfers Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            There are no token transfers available to display.
          </p>
        </div>
      ) : (
        <div className="card mb-4">
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[60px]">#</TableHead>
                  <TableHead>Transaction Hash</TableHead>
                  <TableHead>From</TableHead>
                  <TableHead></TableHead>
                  <TableHead>To</TableHead>
                  <TableHead className="text-right">Value</TableHead>
                  <TableHead className="text-right">Block Number</TableHead>
                  <TableHead className="text-right">Timestamp</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transfers.data.map((transfer, index) => (
                  <TableRow key={index}>
                    <TableCell>{index + 1 + (page - 1) * limit}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <CopyTooltip content={transfer?.transactionHash} />
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger
                              className="text-blue-700 hover:underline cursor-pointer"
                              onClick={() =>
                                navigateToTransaction(transfer?.transactionHash)
                              }
                            >
                              {formatHash(transfer?.transactionHash, 6, 6)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {transfer?.transactionHash}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline cursor-pointer"
                            onClick={() => navigateToAddress(transfer?.from)}
                          >
                            {formatHash(
                              normalizeAddress(transfer?.from),
                              10,
                              9,
                            )}
                          </TooltipTrigger>
                          <TooltipContent>
                            {normalizeAddress(transfer?.from)}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <div className="p-1 inline-flex rounded-full ">
                        <CircleArrowRight className="h-4 w-4 text-[#00a082] dark:text-[#00e6ba]" />
                      </div>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline cursor-pointer"
                            onClick={() => navigateToAddress(transfer?.to)}
                          >
                            {formatHash(normalizeAddress(transfer?.to), 10, 9)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {normalizeAddress(transfer?.to)}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="text-right">
                      {formatWeiToEther(
                        transfer?.amount || transfer?.value,
                        18,
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div
                        className="text-blue-700 hover:underline cursor-pointer"
                        onClick={() => navigateToBlock(transfer?.blockNumber)}
                      >
                        {transfer?.blockNumber}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {new Date(transfer?.timestamp).toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {!isLoading && transfers?.data && (
        <PaginationWithLinks
          page={page}
          pageSize={pageSize}
          totalCount={transfers?.metadata?.total || 0}
          pageSearchParam="page"
          pageSizeSelectOptions={{
            pageSizeOptions: [10, 25, 50, 100],
          }}
        />
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="card mb-4">
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[60px]">#</TableHead>
              <TableHead>Transaction Hash</TableHead>
              <TableHead>From</TableHead>
              <TableHead></TableHead>
              <TableHead>To</TableHead>
              <TableHead className="text-right">Value</TableHead>
              <TableHead className="text-right">Block Number</TableHead>
              <TableHead className="text-right">Timestamp</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-5 w-8" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-32" />
                </TableCell>
                <TableCell>
                  <div className="p-1 inline-flex rounded-full">
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-32" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-20 ml-auto" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-16 ml-auto" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-32 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
