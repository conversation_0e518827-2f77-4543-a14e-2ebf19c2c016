"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertCircle, Search } from "lucide-react"
import { isAddress } from "viem"

interface ContractAddressInputProps {
  value: string
  onChange: (value: string) => void
  onContractTypeDetected: (isContract: boolean) => void
  error?: string
}

export function ContractAddressInput({ value, onChange, onContractTypeDetected, error }: ContractAddressInputProps) {
  const [isValidating, setIsValidating] = useState(false)
  const [contractInfo, setContractInfo] = useState<{
    isContract: boolean
    isVerified: boolean
    name?: string
  } | null>(null)

  const validateAddress = async (address: string) => {
    if (!isAddress(address)) {
      setContractInfo(null)
      onContractTypeDetected(false)
      return
    }

    setIsValidating(true)

    try {
      // Simulate API call to check if address is a contract
      // In real implementation, this would call your blockchain API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mock response - in real app, check if address has bytecode
      const isContract = Math.random() > 0.3 // 70% chance it's a contract for demo
      const mockInfo = {
        isContract,
        isVerified: isContract ? Math.random() > 0.5 : false,
        name: isContract ? "Sample Contract" : undefined,
      }

      setContractInfo(mockInfo)
      onContractTypeDetected(isContract)
    } catch (error) {
      console.error("Address validation failed:", error)
      setContractInfo(null)
      onContractTypeDetected(false)
    } finally {
      setIsValidating(false)
    }
  }

  useEffect(() => {
    if (value && isAddress(value)) {
      const timeoutId = setTimeout(() => {
        validateAddress(value)
      }, 500)

      return () => clearTimeout(timeoutId)
    } else {
      setContractInfo(null)
      onContractTypeDetected(false)
    }
  }, [value])

  return (
    <div className="space-y-3">
      <div className="relative">
        <Input
          placeholder="0x..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={error ? "border-red-500" : ""}
        />
        {isValidating && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <Search className="h-4 w-4 animate-spin" />
          </div>
        )}
      </div>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {contractInfo && (
        <Alert className={contractInfo.isContract ? "border-green-200 bg-green-50" : "border-yellow-200 bg-yellow-50"}>
          {contractInfo.isContract ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          )}
          <AlertDescription>
            {contractInfo.isContract ? (
              <div>
                <p className="text-green-700">
                  ✓ Smart contract detected {contractInfo.name && `(${contractInfo.name})`}
                </p>
                {contractInfo.isVerified && (
                  <p className="text-sm text-green-600 mt-1">Contract source code is verified</p>
                )}
              </div>
            ) : (
              <p className="text-yellow-700">
                ⚠️ This appears to be an EOA (Externally Owned Account), not a smart contract. Ownership verification is
                only available for smart contracts.
              </p>
            )}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
