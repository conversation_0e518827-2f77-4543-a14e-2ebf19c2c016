"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChartTypeEnum, getTimeRanges, useChart } from "@/hooks/useChart";
import { useGetHotContractStats } from "@/hooks/useStatistics";
import { Download, Info } from "lucide-react";
import { useMemo, useState } from "react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";
import TimeRangeSelector, { type TimeRangeOption } from "../time-range-seletor";
import UnifiedChartTooltip from "../unified-chart-tooltip";

const timeRangeOptions: TimeRangeOption[] = [
  { value: "1m", label: "1m" },
  { value: "3m", label: "3m" },
  { value: "6m", label: "6m" },
  { value: "1y", label: "1y" },
  { value: "all", label: "All" },
];

export default function ContractStatsChart() {
  const [timeRange, setTimeRange] = useState<string>("1m");
  const [activeTab, setActiveTab] = useState<string>("overview");

  const { data: dataHotContractStats } = useGetHotContractStats("five_minute");

  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d");
      case "3m":
        return getTimeRanges("90d");
      case "6m":
        return getTimeRanges("180d");
      case "1y":
        return getTimeRanges("1y");
      case "all":
        return getTimeRanges("all");
      default:
        return getTimeRanges("30d");
    }
  }, [timeRange]);

  const { data, isLoading, error } = useChart(
    startTime,
    endTime,
    ChartTypeEnum.CONTRACT_STATS,
  );

  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.map((item: any) => ({
      date: new Date(item.timestamp).toISOString().split("T")[0],
      value: item.totalContracts || 0,
      totalContracts: item.totalContracts || 0,
      verifiedPercentage: item.verifiedPercentage || 0,
      dailyInteractions: item.dailyInteractions || 0,
    }));
  }, [data]);

  // Mock data for pie charts
  const contractTypeData = [
    { name: "ERC-20", value: 45, color: "#3498db" },
    { name: "ERC-721", value: 30, color: "#2ecc71" },
    { name: "ERC-1155", value: 15, color: "#f39c12" },
    { name: "Other", value: 10, color: "#9b59b6" },
  ];

  const verificationStatusData = [
    { name: "Verified", value: 35, color: "#2ecc71" },
    { name: "Unverified", value: 65, color: "#e74c3c" },
  ];

  // Mock data for top contracts table
  const topContractsData = [
    {
      address: "0x1234...5678",
      name: "USDT",
      type: "ERC-20",
      interactions: "1,245,678",
      verified: "Yes",
    },
    {
      address: "0x2345...6789",
      name: "CryptoKitties",
      type: "ERC-721",
      interactions: "987,654",
      verified: "Yes",
    },
    {
      address: "0x3456...7890",
      name: "Uniswap V3",
      type: "ERC-20",
      interactions: "876,543",
      verified: "Yes",
    },
    {
      address: "0x4567...8901",
      name: "Unknown",
      type: "ERC-1155",
      interactions: "765,432",
      verified: "No",
    },
    {
      address: "0x5678...9012",
      name: "Aave",
      type: "ERC-20",
      interactions: "654,321",
      verified: "Yes",
    },
    {
      address: "0x6789...0123",
      name: "Unknown",
      type: "Other",
      interactions: "543,210",
      verified: "No",
    },
    {
      address: "0x7890...1234",
      name: "Compound",
      type: "ERC-20",
      interactions: "432,109",
      verified: "Yes",
    },
    {
      address: "0x8901...2345",
      name: "OpenSea",
      type: "ERC-721",
      interactions: "321,098",
      verified: "Yes",
    },
  ];

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contract Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Contract Statistics</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-4">
            <TimeRangeSelector
              timeRange={timeRange}
              onChange={setTimeRange}
              options={timeRangeOptions}
            />
          </div>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="distribution">Distribution</TabsTrigger>
              <TabsTrigger value="topContracts">Top Contracts</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4">
              <div className="h-[400px]">
                {isLoading ? (
                  <div className="flex h-full w-full items-center justify-center">
                    <Skeleton className="h-[350px] w-full" />
                  </div>
                ) : (
                  <ChartContainer
                    config={{
                      totalContracts: {
                        label: "Total Contracts",
                        color: "#3498db",
                      },
                    }}
                    className="h-full w-full"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={chartData}
                        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis
                          dataKey="date"
                          tickLine={false}
                          axisLine={false}
                          tickFormatter={(value) => {
                            const date = new Date(value);
                            return `${date.getMonth() + 1}/${date.getDate()}`;
                          }}
                        />
                        <YAxis
                          tickLine={false}
                          axisLine={false}
                          tickFormatter={(value) => {
                            if (value >= 1000) {
                              return `${(value / 1000).toFixed(0)}k`;
                            }
                            return value.toString();
                          }}
                          label={{
                            value: "Total Contracts",
                            angle: -90,
                            position: "insideLeft",
                            style: { textAnchor: "middle" },
                          }}
                        />
                        <ChartTooltip
                          content={
                            <UnifiedChartTooltip
                              chartType={ChartTypeEnum.CONTRACT_STATS}
                            />
                          }
                          cursor={false}
                        />
                        <Area
                          type="monotone"
                          dataKey="totalContracts"
                          name="Total Contracts"
                          stroke="#3498db"
                          fill="#3498db"
                          fillOpacity={0.2}
                          strokeWidth={2}
                          activeDot={{ r: 6, fill: "#3498db" }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                )}
              </div>

              <div className="grid grid-cols-2 gap-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">
                      Daily Contract Interactions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={chartData}
                          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            vertical={false}
                          />
                          <XAxis
                            dataKey="date"
                            tickLine={false}
                            axisLine={false}
                            tickFormatter={(value) => {
                              const date = new Date(value);
                              return `${date.getMonth() + 1}/${date.getDate()}`;
                            }}
                          />
                          <YAxis
                            tickLine={false}
                            axisLine={false}
                            tickFormatter={(value) => {
                              if (value >= 1000) {
                                return `${(value / 1000).toFixed(0)}k`;
                              }
                              return value.toString();
                            }}
                          />
                          <ChartTooltip
                            content={
                              <UnifiedChartTooltip
                                chartType={ChartTypeEnum.CONTRACT_STATS}
                              />
                            }
                            cursor={false}
                          />
                          <Bar
                            dataKey="dailyInteractions"
                            name="Daily Interactions"
                            fill="#9b59b6"
                            radius={[4, 4, 0, 0]}
                            barSize={20}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Verification Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart
                          data={chartData}
                          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            vertical={false}
                          />
                          <XAxis
                            dataKey="date"
                            tickLine={false}
                            axisLine={false}
                            tickFormatter={(value) => {
                              const date = new Date(value);
                              return `${date.getMonth() + 1}/${date.getDate()}`;
                            }}
                          />
                          <YAxis
                            tickLine={false}
                            axisLine={false}
                            tickFormatter={(value) => `${value}%`}
                            domain={[0, 100]}
                          />
                          <ChartTooltip
                            content={
                              <UnifiedChartTooltip
                                chartType={ChartTypeEnum.CONTRACT_STATS}
                              />
                            }
                            cursor={false}
                          />
                          <Area
                            type="monotone"
                            dataKey="verifiedPercentage"
                            name="Verification Rate"
                            stroke="#2ecc71"
                            fill="#2ecc71"
                            fillOpacity={0.2}
                            strokeWidth={2}
                            activeDot={{ r: 6, fill: "#2ecc71" }}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="distribution" className="mt-4">
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Contract Types</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={contractTypeData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                          >
                            {contractTypeData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">
                      Verification Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={verificationStatusData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                          >
                            {verificationStatusData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="topContracts" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">
                    Top Contracts by Interactions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Address</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Interactions</TableHead>
                          <TableHead>Verified</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {topContractsData.map((row, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium text-blue-500">
                              {row.address}
                            </TableCell>
                            <TableCell>{row.name}</TableCell>
                            <TableCell>{row.type}</TableCell>
                            <TableCell>{row.interactions}</TableCell>
                            <TableCell>
                              <span
                                className={
                                  row.verified === "Yes"
                                    ? "text-green-500 font-medium"
                                    : "text-red-500 font-medium"
                                }
                              >
                                {row.verified}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" className="flex items-center">
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About Contract Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Contract Statistics dashboard provides comprehensive insights
            into smart contracts on the Helios blockchain. This includes
            deployment trends, verification rates, contract types, and
            interaction patterns.
          </p>
          <ul className="mt-2 space-y-1 text-sm text-muted-foreground list-disc pl-5">
            <li>
              <span className="font-medium">Total Contracts</span> - The
              cumulative number of smart contracts deployed on the network.
            </li>
            <li>
              <span className="font-medium">Verification Rate</span> - The
              percentage of contracts with verified source code.
            </li>
            <li>
              <span className="font-medium">Contract Types</span> - Distribution
              of contracts by standard (ERC-20, ERC-721, etc.).
            </li>
            <li>
              <span className="font-medium">Daily Interactions</span> - Number
              of transactions interacting with smart contracts each day.
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
