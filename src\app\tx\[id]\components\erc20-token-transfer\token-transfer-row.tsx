"use client"

import { Badge } from "@/components/ui/badge"
import { TableCell, TableRow } from "@/components/ui/table"
import { PATH_ROUTER } from "@/constants/routers"
import { normalizeAddress, truncateAddress } from "@/helpers/format"
import { TokenType, type TransferTokenEvent } from "@/types/tokens"
import { formatUnits } from "ethers"
import { ArrowRight } from "lucide-react"
import Link from "next/link"
import { AddressWithTooltip } from "./address-with-tooltip"
import { TokenLogo } from "./token-logo"

interface TokenTransferRowProps {
  transfer: TransferTokenEvent
  type: "token" | "nft"
}

export function TokenTransferRow({ transfer, type }: TokenTransferRowProps) {
  const checkSummedFromAddress = normalizeAddress(transfer.from)
  const checkSummedToAddress = normalizeAddress(transfer.to)

  return (
    <TableRow className="border-b-0">
      <TableCell className="py-3">
        <div className="flex items-center gap-2">
          <TokenLogo
            tokenDetails={transfer.tokenDetails}
            tokenType={transfer.type}
          />
          <div className="flex flex-col">
            <Link
              href={PATH_ROUTER.TOKEN_DETAIL(transfer.tokenAddress)}
              className="text-blue-600 hover:underline font-medium text-sm"
              title={transfer.tokenDetails?.name}
            >
              {transfer.tokenDetails?.symbol ||
                truncateAddress(transfer.tokenAddress)}
            </Link>
            {transfer.tokenDetails?.name && (
              <span className="text-xs text-muted-foreground">
                {transfer.tokenDetails.name}
              </span>
            )}
            <Badge variant="outline" className="text-xs w-fit mt-1">
              {getTokenTypeLabel(transfer.type)}
            </Badge>
          </div>
        </div>
      </TableCell>
      {checkSummedFromAddress ? (
        <TableCell className="py-3">
          <AddressWithTooltip address={checkSummedFromAddress} />
        </TableCell>
      ) : (
        <TableCell className="py-3 text-muted-foreground italic">N/A</TableCell>
      )}
      <TableCell className="py-3 text-center">
        <ArrowRight className="h-4 w-4 text-muted-foreground" />
      </TableCell>
      {checkSummedToAddress ? (
        <TableCell className="py-3">
          <AddressWithTooltip address={checkSummedToAddress} />
        </TableCell>
      ) : (
        <TableCell className="py-3 text-muted-foreground italic">N/A</TableCell>
      )}
      <TableCell className="py-3 text-right">
        <div className="flex flex-col items-end">
          {type === "token" ? (
            <span className="font-medium">
              {formatTokenAmount(transfer)}{" "}
              {transfer.tokenDetails?.symbol || ""}
            </span>
          ) : transfer.tokenId ? (
            <span className="font-medium">Token ID: {transfer.tokenId}</span>
          ) : (
            <span className="font-medium">
              Amount: {formatTokenAmount(transfer)}
            </span>
          )}
          {transfer.type === TokenType.ERC1155 && transfer.amount && (
            <span className="text-xs text-muted-foreground">
              Qty: {transfer.amount}
            </span>
          )}
        </div>
      </TableCell>
    </TableRow>
  )
}

export function formatTokenAmount(transfer: TransferTokenEvent) {
  try {
    // Use amount for NFTs, value for ERC20 tokens
    const isNFT =
      transfer.type === TokenType.ERC721 || transfer.type === TokenType.ERC1155
    const rawAmount = isNFT ? transfer.amount : transfer.value

    if (!rawAmount || rawAmount === "0") return "0"

    if (isNFT) {
      return rawAmount
    }

    // For ERC20 tokens, format with decimals
    const formatted = formatUnits(rawAmount)
    const num = Number.parseFloat(formatted)

    if (num === 0) return "0"
    if (num < 0.000001) return "< 0.000001"

    return num.toLocaleString(undefined, {
      maximumFractionDigits: 6,
      minimumFractionDigits: 0,
    })
  } catch (error) {
    console.error("Error formatting token amount:", error)
    return transfer.amount || transfer.value || "0"
  }
}

export function getTokenTypeLabel(type: string) {
  switch (type) {
    case TokenType.ERC721:
      return "ERC-721"
    case TokenType.ERC1155:
      return "ERC-1155"
    case TokenType.ERC20:
      return "HRC-20"
    case TokenType.HRC20:
      return "HRC-20"
    default:
      return type || "Token"
  }
}
