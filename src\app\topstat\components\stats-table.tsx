import type React from "react";
interface Column {
  header: string;
  accessor: string;
  align?: "left" | "right" | "center";
  cell?: (value: any) => React.ReactNode;
}

interface StatsTableProps {
  columns: Column[];
  data: any[];
}

export function StatsTable({ columns, data }: StatsTableProps) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr>
            {columns.map((column, index) => (
              <th key={index} className={`text-${column.align || "left"}`}>
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <tr key={rowIndex} className="border-t">
              {columns.map((column, colIndex) => {
                const value = row[column.accessor];
                return (
                  <td
                    key={colIndex}
                    className={`py-2 text-${column.align || "left"}`}
                  >
                    {column.cell ? column.cell(value) : value}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
