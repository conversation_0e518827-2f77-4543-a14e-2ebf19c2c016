"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { useReadContract } from "wagmi";
import type { ReadContractErrorType } from "wagmi/actions";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { ContractMethod } from "@/types/contract";
import { ContractMethodCardBase } from "./contract-method-card-base";

interface ReadMethodCardProps {
  method: ContractMethod;
  contractAddress: string;
  index: number;
}

export function ReadMethodCard({
  method,
  contractAddress,
  index,
}: ReadMethodCardProps) {
  const [currentArgs, setCurrentArgs] = useState<unknown[]>([]);
  const [isQuerying, setIsQuerying] = useState(false);
  const [shouldFetch, setShouldFetch] = useState(false);

  // Check if the method requires inputs
  const hasInputs = method.inputs && method.inputs.length > 0;

  const {
    data: readData,
    error: readError,
    refetch,
  } = useReadContract({
    address: contractAddress as `0x${string}`,
    abi: [method],
    functionName: method.name,
    args: currentArgs,
    // Only enable automatic fetching for methods without inputs
    query: { enabled: !hasInputs || shouldFetch },
  });

  // Helper function to render result data
  const renderResultData = (data: unknown) => {
    if (Array.isArray(data)) {
      return (
        <div className="mt-2 space-y-1">
          {data.map((item, i) => (
            <div key={i} className="flex items-center gap-2">
              <span>
                [{i}]: {String(item)}
              </span>
            </div>
          ))}
        </div>
      );
    }

    if (typeof data === "object" && data !== null) {
      return (
        <div className="mt-2 space-y-1">
          {Object.entries(data as Record<string, unknown>).map(
            ([key, value]) => (
              <div key={key} className="flex items-center gap-2">
                <span>
                  {key}: {String(value)}
                </span>
              </div>
            ),
          )}
        </div>
      );
    }

    return (
      <div className="mt-2 p-2 bg-muted rounded-md">
        <code>{String(data)}</code>
      </div>
    );
  };

  const handleQuery = useCallback(async () => {
    setIsQuerying(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Error querying contract:", error);
    } finally {
      setIsQuerying(false);
      // Reset shouldFetch after query completes
      setShouldFetch(false);
    }
  }, [refetch]);

  useEffect(() => {
    // For methods with inputs, only query when shouldFetch is true
    if (hasInputs && shouldFetch) {
      handleQuery();
    }
  }, [shouldFetch, handleQuery, hasInputs]);

  const renderActions = (args: unknown[], shouldFetchArg: boolean) => {
    // Only show query button for methods with inputs
    if (!hasInputs) {
      return null;
    }

    return (
      <div className="flex items-center gap-2">
        <Button
          type="submit"
          variant="secondary"
          disabled={isQuerying}
          onClick={() => {
            setCurrentArgs(args);
            setShouldFetch(shouldFetchArg);
          }}
        >
          {isQuerying ? "Processing..." : "Query"}
        </Button>
      </div>
    );
  };

  const renderResults = () => {
    return (
      <>
        {/* Read data display */}
        {readData !== undefined && renderResultData(readData)}

        {/* Error display */}
        {readError && (
          <Alert variant="destructive" className="mt-2">
            <AlertDescription>
              {(readError as ReadContractErrorType)?.message ||
                "An error occurred"}
            </AlertDescription>
          </Alert>
        )}
      </>
    );
  };

  return (
    <ContractMethodCardBase
      method={method}
      contractAddress={contractAddress}
      index={index}
      isWrite={false}
      renderActions={renderActions}
      renderResults={renderResults}
    />
  );
}
