"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON>, ExternalLink, FileIcon } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, normalizeAddress } from "@/helpers/format"

interface LogsViewerProps {
  logs: any[]
  contractAbi?: any
}

export function LogsViewer({ logs, contractAbi }: LogsViewerProps) {
  const [viewMode, setViewMode] = useState<"dec" | "hex">("hex")

  if (!logs || logs.length === 0) {
    return (
      <Card className="border rounded-lg p-6">
        <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
          <FileIcon className="h-12 w-12 mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">
            No logs found for this transaction
          </h3>
          <p className="text-gray-500 max-w-md mx-auto">
            This transaction either did not call a smart contract or no events
            were emitted during execution.
          </p>
        </div>
      </Card>
    )
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const formatData = (data: string) => {
    if (viewMode === "dec" && data.startsWith("0x")) {
      try {
        return BigInt(data).toString()
      } catch (e) {
        return data
      }
    }
    return data
  }

  // Function to detect if a topic is an address
  const isTopicAddress = (topic: string): boolean => {
    return (
      topic?.startsWith("0x") &&
      topic.length === 66 &&
      topic.substring(2, 26) === "000000000000000000000000"
    )
  }

  // Function to extract address from topic
  const topicToAddress = (topic: string): string => {
    if (isTopicAddress(topic)) {
      const addressPart = "0x" + topic.substring(26)
      try {
        return normalizeAddress(addressPart) ?? addressPart
      } catch (e) {
        return addressPart
      }
    }
    return topic
  }

  // Function to get event name from ABI if available
  const getEventName = (log: any): string => {
    if (!contractAbi) return ""

    // Try to find event by first topic (event signature)
    const eventSignature = log.topics[0]
    const event = contractAbi.find(
      (item: any) => item.type === "event" && item.signature === eventSignature,
    )

    if (event) {
      // If we have indexed parameters, try to decode them
      const eventName = event.name

      // Check if we have parameters to decode
      if (event.inputs && event.inputs.length > 0) {
        const indexedInputs = event.inputs.filter((input: any) => input.indexed)

        // Build parameter string
        const params = indexedInputs.map((input: any, index: number) => {
          const topicIndex = index + 1 // Skip first topic (event signature)
          if (topicIndex < log.topics.length) {
            const topic = log.topics[topicIndex]
            if (input.type === "address" && isTopicAddress(topic)) {
              return `${input.name}: ${formatHash(topicToAddress(topic), 6, 4)}`
            }
            return `${input.name}: ${topic}`
          }
          return `${input.name}: ?`
        })

        return `${eventName}(${params.join(", ")})`
      }

      return eventName
    }

    return ""
  }

  return (
    <Card className="border rounded-lg overflow-hidden">
      <div className="p-4 bg-muted/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileIcon className="h-5 w-5 text-muted-foreground" />
            <h2 className="text-lg font-semibold">
              Transaction Receipt Event Logs
            </h2>
          </div>
        </div>
      </div>

      {logs.map((log, logIndex) => (
        <div key={`log-${logIndex}`} className="mb-4">
          <div className="p-4 flex items-center gap-2">
            <Badge
              variant="outline"
              className="rounded-full px-2 py-1 bg-blue-50 text-blue-700"
            >
              {logIndex}
            </Badge>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="font-medium">Address:</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        href={PATH_ROUTER.ADDRESS_DETAIL(log.address)}
                        className="text-blue-600 hover:underline"
                      >
                        {log.address}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{log.address}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5"
                  onClick={() => copyToClipboard(log.address)}
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>
                <Button variant="ghost" size="icon" className="h-5 w-5" asChild>
                  <Link href={PATH_ROUTER.ADDRESS_DETAIL(log.address)}>
                    <ExternalLink className="h-3.5 w-3.5" />
                  </Link>
                </Button>
              </div>

              <div className="mt-1">
                <span className="font-medium">Name:</span>
                {contractAbi ? (
                  <span className="ml-2">{getEventName(log)}</span>
                ) : (
                  <span className="ml-2 text-sm text-muted-foreground">
                    {isTopicAddress(log.topics[1]) &&
                    isTopicAddress(log.topics[2])
                      ? `Transfer (index_topic_1 address from, index_topic_2 address to, uint256 value)`
                      : "Unknown Event"}
                  </span>
                )}
                {contractAbi && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-2 text-xs text-blue-600"
                  >
                    View Source
                  </Button>
                )}
              </div>
            </div>
          </div>

          <Table>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium w-1/6">Topics:</TableCell>
                <TableCell>
                  <div className="space-y-2">
                    {log.topics.map((topic: string, index: number) => (
                      <div
                        key={`topic-${index}`}
                        className="flex items-center gap-2"
                      >
                        <Badge
                          variant="outline"
                          className="min-w-[40px] text-center"
                        >
                          {index}
                        </Badge>
                        <span className="font-mono text-sm break-all">
                          {topic}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5"
                          onClick={() => copyToClipboard(topic)}
                        >
                          <Copy className="h-3.5 w-3.5" />
                        </Button>

                        {isTopicAddress(topic) && index > 0 && (
                          <div className="flex items-center gap-2 ml-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Link
                                    href={PATH_ROUTER.ADDRESS_DETAIL(
                                      topicToAddress(topic),
                                    )}
                                    className="text-blue-600 hover:underline text-sm"
                                  >
                                    {formatHash(topicToAddress(topic), 10, 8)}
                                  </Link>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{topicToAddress(topic)}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-5 w-5"
                              asChild
                            >
                              <Link
                                href={PATH_ROUTER.ADDRESS_DETAIL(
                                  topicToAddress(topic),
                                )}
                              >
                                <ExternalLink className="h-3.5 w-3.5" />
                              </Link>
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell className="font-medium">Data:</TableCell>
                <TableCell>
                  <div className="flex items-center gap-4">
                    <span className="font-mono text-sm break-all">
                      {viewMode === "hex" ? log.data : formatData(log.data)}
                    </span>

                    <div className="ml-auto flex gap-2">
                      <Button
                        variant={viewMode === "dec" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("dec")}
                        className="text-xs px-2 py-1 h-7"
                      >
                        Dec
                      </Button>
                      <Button
                        variant={viewMode === "hex" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("hex")}
                        className="text-xs px-2 py-1 h-7 ml-1"
                      >
                        Hex
                      </Button>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
          {logIndex < logs.length - 1 && <Separator className="my-4" />}
        </div>
      ))}
    </Card>
  )
}
