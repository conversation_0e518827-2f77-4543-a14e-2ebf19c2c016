"use client"

import { TableSkeleton } from "@/components/table/table-skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, hexToDecimal } from "@/helpers/format"
import { useBlockList } from "@/hooks/useBlocks"
import { CuboidIcon as CubeIcon } from "lucide-react"
import Link from "next/link"

const getSecondsAgo = (blocksList: any, block: any, index: number) => {
  const nextBlock = blocksList?.data[index + 1]
  const secondsAgo =
    nextBlock && block.timestamp && nextBlock.timestamp
      ? Number(new Date(block.timestamp).getTime() / 1000) -
        Number(new Date(nextBlock.timestamp).getTime() / 1000)
      : null
  return secondsAgo
}

export function HomeBlocksTable() {
  const { data: blocksList, isLoading } = useBlockList({
    page: 1,
    limit: 6,
    refetchInterval: 5000,
  })

  if (isLoading) {
    return <TableSkeleton columns={5} rows={5} />
  }

  const blocks = blocksList?.result?.data.slice(0, 5) ?? []

  return (
    <>
      {/* Desktop Table */}
      <Table className="hidden md:table w-full overflow-x-auto rounded-lg shadow-sm">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">Block</TableHead>
            <TableHead>Hash</TableHead>
            <TableHead className="text-right">Gas Used</TableHead>
            <TableHead className="text-right">Transactions</TableHead>
            <TableHead className="text-right w-[150px]">Miner</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {blocks.map((block, index) => (
            <TableRow key={block.number} className="h-14">
              <TableCell>
                <Link
                  href={PATH_ROUTER.BLOCK_DETAIL(block.number)}
                  className="flex items-center gap-2 text-primary hover:underline"
                >
                  <div className="bg-secondary text-primary p-3 rounded-md">
                    <CubeIcon className="h-5 w-5" />
                  </div>
                  {block.number}
                </Link>
              </TableCell>
              <TableCell>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Link
                        href={PATH_ROUTER.BLOCK_DETAIL(block.hash)}
                        className="text-primary hover:underline"
                      >
                        {formatHash(block.hash)}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>{block.hash}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
              <TableCell className="text-right">
                (
                {(
                  (Number(hexToDecimal(block.gasUsed)) /
                    Number(hexToDecimal(block.gasLimit))) *
                  100
                ).toFixed(2)}
                %)
              </TableCell>
              <TableCell className="text-right">
                <Link
                  href={`${PATH_ROUTER.TRANSACTIONS}?block=${block.number}`}
                  className="text-primary hover:underline"
                >
                  {block.transactionCount} {block.transactionCount === 1 ? 'txn' : 'txns'}
                  {getSecondsAgo(blocksList?.result, block, index) !== null && (
                    <span className="text-muted-foreground">
                      {" "}
                      in {getSecondsAgo(blocksList?.result, block, index)}s
                    </span>
                  )}
                </Link>
              </TableCell>
              <TableCell className="text-right">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Link
                        href={PATH_ROUTER.ADDRESS_DETAIL(block.miner)}
                        className="text-primary hover:underline"
                      >
                        {formatHash(block.miner)}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>{block.miner}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Mobile Card */}
      <div className="block md:hidden space-y-4">
        {blocks.map((block, index) => (
          <div
            key={block.number}
            className="rounded-lg border p-4 bg-white shadow-sm flex flex-col gap-2 text-sm"
          >
            <div className="flex items-center gap-2">
              <div className="bg-secondary text-muted-foreground p-2 rounded">
                <CubeIcon className="h-5 w-5" />
              </div>
              <Link
                href={PATH_ROUTER.BLOCK_DETAIL(block.number)}
                className="text-primary hover:underline font-medium"
              >
                Block #{block.number}
              </Link>
            </div>

            <div>
              <span className="text-muted-foreground">Hash:</span>{" "}
              <Link
                href={PATH_ROUTER.BLOCK_DETAIL(block.hash)}
                className="text-primary hover:underline break-all"
              >
                {formatHash(block.hash)}
              </Link>
            </div>

            <div>
              <span className="text-muted-foreground">Gas Used:</span>{" "}
              {(
                (Number(hexToDecimal(block.gasUsed)) /
                  Number(hexToDecimal(block.gasLimit))) *
                100
              ).toFixed(2)}
              %
            </div>

            <div>
              <span className="text-muted-foreground">Transactions:</span>{" "}
              <Link
                href={`${PATH_ROUTER.TRANSACTIONS}?block=${block.number}`}
                className="text-primary hover:underline"
              >
                {block.transactionCount} txns
                {getSecondsAgo(blocksList?.result, block, index) !== null && (
                  <span className="text-muted-foreground">
                    {" "}
                    in {getSecondsAgo(blocksList?.result, block, index)}s
                  </span>
                )}
              </Link>
            </div>

            <div>
              <span className="text-muted-foreground">Miner:</span>{" "}
              <Link
                href={PATH_ROUTER.ADDRESS_DETAIL(block.miner)}
                className="text-primary hover:underline break-all"
              >
                {formatHash(block.miner)}
              </Link>
            </div>
          </div>
        ))}
      </div>
    </>
  )
}
