"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertTitle } from "@/components/ui/alert"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { NATIVE_SYMBOL, ZERO_ADDRESS } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatEtherValue,
  formatHash,
  formatTimestamp,
  normalizeAddress,
} from "@/helpers/format"
import { useInternalTransactionList } from "@/hooks/useTransactions"
import { FileIcon } from "lucide-react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

export interface InternalTransaction {
  id: number
  createdAt: string
  updatedAt: string
  from: string
  gas: string
  gasUsed: string
  input: string
  output: string
  to: string
  type: string
  value: string
  parentHash: string
  signature: string
  timestamp: string
  blockNumber: number
}

export interface Metadata {
  page: number
  limit: number
  totalCount: number
  totalPages: number
}

export interface InternalTransactionResponse {
  data: InternalTransaction[]
  metadata: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

const InternalTransactionTab = () => {
  const searchParams = useSearchParams()
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)

  useEffect(() => {
    const pageParam = searchParams.get("page")
    const pageSizeParam = searchParams.get("pageSize")

    if (pageParam) {
      setPage(Math.max(1, Number(pageParam)))
    }

    if (pageSizeParam) {
      setPageSize(Number(pageSizeParam))
    }
  }, [searchParams])

  const {
    data: transactions,
    isLoading: isTransactionsLoading,
    error,
  } = useInternalTransactionList({
    page,
    limit: pageSize,
  })

  if (isTransactionsLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Internal Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Parent Txn Hash</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Block</TableHead>
                  <TableHead>Age</TableHead>
                  <TableHead>From</TableHead>
                  <TableHead>To</TableHead>
                  <TableHead className="text-right">Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array(5)
                  .fill(0)
                  .map((_, index) => (
                    <TableRow key={index}>
                      <TableCell colSpan={7}>
                        <Skeleton className="h-8 w-full" />
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load internal transactions. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  if (!transactions?.data || transactions.data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Internal Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <FileIcon className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No transactions found</h3>
            <p className="text-sm text-muted-foreground">
              There are no internal transactions to display at this time.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const metadata = transactions.metadata || {
    page: page,
    limit: pageSize,
    total: transactions.data.length,
    totalPages: 1,
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Internal Transactions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Parent Txn Hash</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Block</TableHead>
                <TableHead>Age</TableHead>
                <TableHead>From</TableHead>
                <TableHead>To</TableHead>
                <TableHead className="text-right">Value</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.data.map((tx) => (
                <TableRow key={tx.id}>
                  <TableCell>
                    <Link
                      href={`${PATH_ROUTER.TRANSACTION_DETAIL(tx.parentHash)}`}
                      className="flex items-center gap-2 text-blue-700 hover:underline"
                    >
                      <FileIcon className="h-4 w-4" />
                      {formatHash(tx.parentHash)}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-muted rounded text-xs">
                      {tx.type}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={PATH_ROUTER.BLOCK_DETAIL(tx.blockNumber)}
                      className="text-blue-700 hover:underline"
                    >
                      {tx.blockNumber}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          {formatTimestamp(tx?.timestamp)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {new Date(tx?.timestamp).toLocaleString()}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Link
                            href={PATH_ROUTER.ADDRESS_DETAIL(
                              normalizeAddress(tx.from) || ZERO_ADDRESS,
                            )}
                            className="text-blue-700 hover:underline"
                          >
                            {tx.from
                              ? formatHash(normalizeAddress(tx.from))
                              : "0x0000...0000"}
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent>
                          {normalizeAddress(tx.from) || ZERO_ADDRESS}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Link
                            href={`${PATH_ROUTER.ADDRESS_DETAIL(
                              normalizeAddress(tx.to) || ZERO_ADDRESS,
                            )}`}
                            className="text-blue-700 hover:underline"
                          >
                            {tx.to
                              ? formatHash(normalizeAddress(tx.to))
                              : "0x0000...0000"}
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent>
                          {normalizeAddress(tx.to) || ZERO_ADDRESS}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell className="text-right">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          {formatEtherValue(tx.value)} {NATIVE_SYMBOL}
                        </TooltipTrigger>
                        <TooltipContent>{tx.value}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="mt-4">
          <PaginationWithLinks
            page={metadata.page}
            pageSize={metadata.limit}
            totalCount={metadata.total}
            pageSearchParam="page"
            pageSizeSelectOptions={{
              pageSizeSearchParam: "pageSize",
              pageSizeOptions: [10, 25, 50, 100],
            }}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default InternalTransactionTab
