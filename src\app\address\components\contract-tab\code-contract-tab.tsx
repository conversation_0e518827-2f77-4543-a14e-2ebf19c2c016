import { Button } from "@/components/ui/button";
import { TabsContent } from "@/components/ui/tabs";
import { Copy, ExternalLink } from "lucide-react";

interface ContractFile {
  name: string;
  content: string;
}

const CodeContractTab = ({ sourceFiles }: { sourceFiles: ContractFile[] }) => {
  return (
    <TabsContent value="code" className="mt-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Contract Source Code</span>
          <span className="text-xs text-muted-foreground">
            (Solidity Standard Json-Input format)
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Copy className="mr-2 h-3.5 w-3.5" />
            Copy
          </Button>
        </div>
      </div>

      {sourceFiles.map((file, index) => (
        <div key={index} className="space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-sm">
              File {index + 1} of {sourceFiles.length}: {file.name}
            </p>
            <div className="flex gap-1">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="relative rounded-md border bg-muted/50">
            <pre className="overflow-x-auto p-4 text-xs">
              <code>{file.content}</code>
            </pre>
          </div>
        </div>
      ))}
    </TabsContent>
  );
};

export default CodeContractTab;
