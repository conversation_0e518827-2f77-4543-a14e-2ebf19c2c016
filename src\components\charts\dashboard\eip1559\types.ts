export interface EIP1559MetricsResponse {
    timestamp: string
    totalBurnFee: string
    totalBurnFeeUsd: string
  }
  
  export interface Block {
    number: string
    timestamp: string
    baseFeePerGas: string
    gasUsed: string
  }
  
  export interface TimeRangeOption {
    value: string
    label: string
  }
  
  export interface ChartDataPoint {
    date: string
    burntFees: number
    burntFeesUsd: number
  }
  
  export interface BlockChartDataPoint {
    block: number
    timestamp: string
    baseFee: number
    gasUsed: number
    time: string
    date: string
  }
  
  export interface SummaryStatistics {
    totalBurnt: number
    totalBurntUsd: number
    highestDailyBurn: number
  }
  