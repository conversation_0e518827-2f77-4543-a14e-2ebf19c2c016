import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { NATIVE_SYMBOL } from "@/constants"
import { formatNumber } from "@/lib/utils"
import { Clock } from "lucide-react"

interface SummaryCardsProps {
  isLoading: boolean
  summaryStats: {
    totalBurnt: number
    totalBurntUsd: number
    highestDailyBurn: number
  }
}

export default function SummaryCards({ isLoading, summaryStats }: SummaryCardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-blue-600 text-sm">Ξ</span>
              </div>
              <h3 className="text-sm font-medium">Total Burnt Fees</h3>
            </div>
            <div className="text-xs text-muted-foreground flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>5 hours ago</span>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-3xl font-bold">
              {isLoading ? <Skeleton className="h-8 w-32" /> : formatNumber(summaryStats.totalBurnt)}
            </div>
            <div className="text-lg text-muted-foreground">{NATIVE_SYMBOL}</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-blue-600 text-sm">Ξ</span>
              </div>
              <h3 className="text-sm font-medium">Total Burnt Fees</h3>
            </div>
            <div className="text-xs text-muted-foreground flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>5 hours ago</span>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-3xl font-bold">
              {isLoading ? <Skeleton className="h-8 w-32" /> : formatNumber(summaryStats.totalBurntUsd)}
            </div>
            <div className="text-lg text-muted-foreground">USD</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-blue-600 text-sm">Ξ</span>
              </div>
              <h3 className="text-sm font-medium">Highest Daily Burn</h3>
            </div>
            <div className="text-xs text-muted-foreground flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>30 days ago</span>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-3xl font-bold">
              {isLoading ? <Skeleton className="h-8 w-32" /> : formatNumber(summaryStats.highestDailyBurn)}
            </div>
            <div className="text-lg text-muted-foreground">{NATIVE_SYMBOL}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
