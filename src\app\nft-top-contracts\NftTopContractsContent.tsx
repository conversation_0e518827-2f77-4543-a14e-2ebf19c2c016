"use client"

import { CSVExportButton } from "@/components/csv-button-export"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { PATH_ROUTER } from "@/constants/routers"
import { formatTimestamp } from "@/helpers/format"
import { useGetStatsTopCollections } from "@/hooks/useNft"
import { formatNFTCollectionsForCSV } from "@/lib/utils/csv-export"
import { StatsTopCollectionMetric, METRIC_LABELS } from "@/types/nft"
import { AlertCircle, ImageIcon } from "lucide-react"
import Link from "next/link"
import { useSearchParams, useRouter, usePathname } from "next/navigation"
import { useCallback } from "react"

const NftTopContractsContent = () => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()

  const page = Math.max(1, Number(searchParams.get("page") || "1"))
  const pageSize = Number(searchParams.get("pageSize") || "25")
  const currentMetric =
    (searchParams.get("metric") as StatsTopCollectionMetric) ||
    StatsTopCollectionMetric.THIRTY_DAYS

  const { data, isLoading, isError, error } = useGetStatsTopCollections(
    currentMetric,
    page,
    pageSize,
  )

  const updateSearchParams = useCallback(
    (updates: Record<string, string>) => {
      const params = new URLSearchParams(searchParams.toString())
      Object.entries(updates).forEach(([key, value]) => {
        if (value) {
          params.set(key, value)
        } else {
          params.delete(key)
        }
      })
      router.push(`${pathname}?${params.toString()}`)
    },
    [searchParams, router, pathname],
  )

  const handleMetricChange = (metric: string) => {
    updateSearchParams({
      metric,
      page: "1",
    })
  }

  if (isError) {
    return (
      <div className="w-full container mx-auto px-4 py-8">
        <Card className="w-full">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold">
                Error Loading NFT Collections
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                {error instanceof Error
                  ? error.message
                  : "An unexpected error occurred"}
              </p>
              <Button asChild className="mt-4">
                <Link href={`${PATH_ROUTER.NFT_TOP_CONTRACTS}?page=1`}>
                  Try Again
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full container mx-auto px-4 py-8">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row md:justify-between gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Top NFT Collections</h2>
        </div>

        <div className="flex items-center gap-2">
          {!isLoading && data && data.data && data.data.length > 0 && (
            <CSVExportButton
              data={data.data}
              formatter={formatNFTCollectionsForCSV}
              filename={`nft-collections-${currentMetric}-page-${page}.csv`}
            />
          )}
          <Select value={currentMetric} onValueChange={handleMetricChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(METRIC_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        {isLoading ? (
          <LoadingSkeleton />
        ) : !data || !data.data || data.data.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <h3 className="text-lg font-semibold">No NFT Collections Found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              There are no NFT collections available for the selected timeframe.
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12 text-center">#</TableHead>
                  <TableHead>Collection</TableHead>
                  <TableHead className="text-center">Type</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-center">Owners Count</TableHead>
                  <TableHead className="text-center">Transfers Count</TableHead>
                  <TableHead className="text-center">Total Assets</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.map((contract, index) => (
                  <TableRow
                    key={contract.id}
                    className="cursor-pointer hover:bg-muted/50"
                  >
                    <TableCell className="font-medium text-center">
                      {(page - 1) * pageSize + index + 1}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {contract.tokenDetails?.logo ? (
                          <div className="relative h-8 w-8 rounded-full overflow-hidden border">
                            <img
                              src={
                                contract.tokenDetails.logo || "/placeholder.svg"
                              }
                              alt={`${
                                contract.tokenDetails?.name || "NFT"
                              } logo`}
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src =
                                  "/placeholder.svg?height=32&width=32"
                              }}
                            />
                          </div>
                        ) : (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                            <ImageIcon className="h-4 w-4 text-muted-foreground" />
                          </div>
                        )}
                        <div className="flex flex-col">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Link
                                  href={PATH_ROUTER.TOKEN_DETAIL(
                                    contract.address || contract.tokenAddress,
                                  )}
                                  className="font-medium text-blue-700 hover:underline"
                                >
                                  {contract.tokenDetails?.name ||
                                    "Unnamed Collection"}
                                </Link>
                              </TooltipTrigger>
                              <TooltipContent>
                                {contract.address || contract.tokenAddress}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {contract.tokenDetails?.symbol && (
                            <span className="text-xs text-muted-foreground">
                              {contract.tokenDetails.symbol}
                            </span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="secondary">
                        {contract.nftType || contract.type || "Unknown"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            {formatTimestamp(contract.createdAt)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {new Date(contract.createdAt).toLocaleString()}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="text-center">
                      {contract.ownersCount?.toLocaleString() || "-"}
                    </TableCell>
                    <TableCell className="text-center">
                      {contract.transfersCount?.toLocaleString() || "-"}
                    </TableCell>
                    <TableCell className="text-center">
                      {contract.totalAssets?.toLocaleString() || "-"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {!isLoading && data && data.metadata && (
        <div className="mt-4">
          <PaginationWithLinks
            page={page}
            pageSize={pageSize}
            totalCount={data.metadata.total}
            pageSearchParam="page"
            pageSizeSelectOptions={{
              pageSizeOptions: [10, 25, 50, 100],
            }}
          />
        </div>
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12 text-center">#</TableHead>
            <TableHead>Collection</TableHead>
            <TableHead className="text-center">Type</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead className="text-center">Owners Count</TableHead>
            <TableHead className="text-center">Transfers Count</TableHead>
            <TableHead className="text-center">Total Assets</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 10 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell className="text-center">
                <Skeleton className="h-5 w-5 mx-auto" />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex flex-col gap-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-6 w-20 rounded-full mx-auto" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-32" />
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-5 w-16 mx-auto" />
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-5 w-16 mx-auto" />
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-5 w-16 mx-auto" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

export default NftTopContractsContent
