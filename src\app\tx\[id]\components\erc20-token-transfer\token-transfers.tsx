"use client"

import type React from "react"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Coins, ImageIcon } from "lucide-react"
import { useGetTransferTokensByTransactionHash } from "@/hooks/useTransactions"
import { TokenType, type TransferTokenEvent } from "@/types/tokens"
import { TokenTransfersSkeleton } from "./token-transfers-skeleton"
import { TokenTransferRow } from "./token-transfer-row"

interface TokenTransfersProps {
  transactionHash: string
}

export function TokenTransfers({ transactionHash }: TokenTransfersProps) {
  const {
    data: transferTokens,
    isLoading,
    isError,
  } = useGetTransferTokensByTransactionHash(transactionHash)

  if (isLoading) {
    return <TokenTransfersSkeleton />
  }

  if (isError || !transferTokens || transferTokens.length === 0) {
    return null
  }

  const erc20Transfers = transferTokens.filter(
    (t: TransferTokenEvent) =>
      t.type === TokenType.ERC20 || t.type === TokenType.HRC20,
  )
  const nftTransfers = transferTokens.filter(
    (t: TransferTokenEvent) =>
      t.type === TokenType.ERC721 || t.type === TokenType.ERC1155,
  )

  return (
    <div className="mt-6 space-y-6">
      {erc20Transfers.length > 0 && (
        <TokenTransferCard
          title={`HRC-20 ${erc20Transfers.length === 1 ? 'Token' : 'Tokens'} Transferred`}
          icon={<Coins className="h-4 w-4 text-muted-foreground" />}
          count={erc20Transfers.length}
          transfers={erc20Transfers}
          type="token"
        />
      )}

      {nftTransfers.length > 0 && (
        <TokenTransferCard
          title="NFTs Transferred"
          icon={<ImageIcon className="h-4 w-4 text-muted-foreground" />}
          count={nftTransfers.length}
          transfers={nftTransfers}
          type="nft"
        />
      )}
    </div>
  )
}

interface TokenTransferCardProps {
  title: string
  icon: React.ReactNode
  count: number
  transfers: TransferTokenEvent[]
  type: "token" | "nft"
}

function TokenTransferCard({
  title,
  icon,
  count,
  transfers,
  type,
}: TokenTransferCardProps) {
  return (
    <Card className="border rounded-lg overflow-hidden">
      <div className="flex">
        {/* Left side - Title */}
        <div className="w-1/4 p-4 bg-muted/30 border-r">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm font-medium">{title}</span>
            <Badge variant="secondary" className="ml-2 text-xs">
              {count}
            </Badge>
          </div>
        </div>

        {/* Right side - Table */}
        <div className="flex-1">
          <Table>
            <TableHeader>
              <TableRow className="border-b">
                <TableHead className="w-[120px]">
                  {type === "token" ? "Token" : "Collection"}
                </TableHead>
                <TableHead>From</TableHead>
                <TableHead className="w-[50px]"></TableHead>
                <TableHead>To</TableHead>
                <TableHead className="text-right">
                  {type === "token" ? "Amount" : "Token ID / Amount"}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transfers.map((transfer, index) => (
                <TokenTransferRow
                  key={`${transfer.transactionHash}-${transfer.id}-${index}`}
                  transfer={transfer}
                  type={type}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </Card>
  )
}
