import apiClient from "@/lib/api/apiClient";
import {
  StatsTopEntriesMap,
  StatsTopMetric,
  StatsTopResponse,
  StatsTopResult,
} from "@/types/charts";
import { useQuery } from "@tanstack/react-query";

export enum ChartTypeEnum {
  DAILY_TXN = "DAILY_TXN",
  ERC20_TOKEN_TRANSFER = "ERC20_TOKEN_TRANSFER",
  UNIQUE_ADDRESS = "UNIQUE_ADDRESS",
  AVG_BLOCK_SIZE = "AVG_BLOCK_SIZE",
  AVG_BLOCK_TIME = "AVG_BLOCK_TIME",
  AVG_GAS_PRICE = "AVG_GAS_PRICE",
  AVG_GAS_LIMIT = "AVG_GAS_LIMIT",
  GAS_USED = "GAS_USED",
  BLOCK_COUNT_AND_REWARD = "BLOCK_COUNT_AND_REWARD",
  FULL_NODE_SYNC = "FULL_NODE_SYNC",
  ACTIVE_ADDRESS = "ACTIVE_ADDRESS",
  ACTIVE_ERC20_ADDRESS = "ACTIVE_ERC20_ADDRESS",
  AVG_TXN_FEE = "AVG_TXN_FEE",
  DAILY_BURNT = "DAILY_BURNT",
  DAILY_VERIFIED_CONTRACT = "DAILY_VERIFIED_CONTRACT",
  DAILY_DEPLOYED_CONTRACT = "DAILY_DEPLOYED_CONTRACT",
  AVG_PENDING_TRANSACTION = "AVG_PENDING_TRANSACTION",
  EIP1559_METRICS = "EIP1559_METRICS",
  CONTRACT_STATS = "CONTRACT_STATS",
}

export type ChartType = keyof typeof ChartTypeEnum;

// Define the response data structure based on the actual API response
export type ChartDataItem = {
  timestamp: string;
  avgBlockTime?: number;
  avgBlockSize?: string;
  blockCount?: number;
  newAddressSeen?: number;
  transactionCount?: number;
  addressActive?: number;
  addressSend?: number;
  addressReceive?: number;
  erc20AddressActive?: number;
  erc20AddressSend?: number;
  erc20AddressReceive?: number;
  [key: string]: any;
};

export type ChartDataResponse = {
  result: ChartDataItem[];
};

export const useChart = (
  startTime: number | string,
  endTime: number | string,
  type: ChartType,
) => {
  return useQuery({
    queryKey: ["chartData", startTime, endTime, type],
    queryFn: async () => {
      const response = await apiClient.post<ChartDataResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "stats_dailytxs",
          params: [startTime, endTime, type],
          id: 83,
        },
      );

      return response?.result || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Helper function to get default time ranges
export const getTimeRanges = (
  range: "1d" | "7d" | "30d" | "90d" | "180d" | "1y" | "all" = "30d",
) => {
  const now = Date.now();
  const day = 24 * 60 * 60 * 1000;

  switch (range) {
    case "1d":
      return { startTime: now - day, endTime: now };
    case "7d":
      return { startTime: now - 7 * day, endTime: now };
    case "30d":
      return { startTime: now - 30 * day, endTime: now };
    case "90d":
      return { startTime: now - 90 * day, endTime: now };
    case "180d":
      return { startTime: now - 180 * day, endTime: now };
    case "1y":
      return { startTime: now - 365 * day, endTime: now };
    case "all":
      return { startTime: now - 5 * 365 * day, endTime: now }; // 5 years
    default:
      return { startTime: now - 30 * day, endTime: now };
  }
};

// Helper function to get the appropriate data field based on chart type
export const getDataFieldForChartType = (chartType: ChartType): string => {
  switch (chartType) {
    case ChartTypeEnum.AVG_BLOCK_TIME:
      return "avgBlockTime";
    case ChartTypeEnum.AVG_BLOCK_SIZE:
      return "avgBlockSize";
    case ChartTypeEnum.DAILY_TXN:
      return "transactionCount";
    case ChartTypeEnum.BLOCK_COUNT_AND_REWARD:
      return "blockCount";
    case ChartTypeEnum.UNIQUE_ADDRESS:
      return "newAddressSeen";
    case ChartTypeEnum.ERC20_TOKEN_TRANSFER:
      return "transactionCount";
    case ChartTypeEnum.ACTIVE_ADDRESS:
      return "addressActive";
    case ChartTypeEnum.ACTIVE_ERC20_ADDRESS:
      return "erc20AddressActive";
    default:
      return "transactionCount";
  }
};

export const getLabelForChartType = (chartType: ChartType): string => {
  switch (chartType) {
    case ChartTypeEnum.AVG_BLOCK_TIME:
      return "Avg Block Time";
    case ChartTypeEnum.AVG_BLOCK_SIZE:
      return "Block Size (Bytes)";
    case ChartTypeEnum.DAILY_TXN:
      return "Total Transactions";
    case ChartTypeEnum.BLOCK_COUNT_AND_REWARD:
      return "Block Count";
    case ChartTypeEnum.UNIQUE_ADDRESS:
      return "New Address Seen";
    case ChartTypeEnum.ERC20_TOKEN_TRANSFER:
      return "Total Token Transfer";
    case ChartTypeEnum.AVG_GAS_PRICE:
      return "Avg Gas Price";
    case ChartTypeEnum.AVG_GAS_LIMIT:
      return "Avg Gas Limit";
    case ChartTypeEnum.GAS_USED:
      return "Gas Used";
    case ChartTypeEnum.ACTIVE_ADDRESS:
      return "Active Addresses";
    case ChartTypeEnum.ACTIVE_ERC20_ADDRESS:
      return "Active ERC20 Addresses";
    default:
      return "Value";
  }
};

export const useStatsTop = <T extends keyof StatsTopEntriesMap>(
  metric: StatsTopMetric,
  type: T,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    refetchOnWindowFocus?: boolean;
    refetchOnMount?: boolean;
    refetchOnReconnect?: boolean;
    retry?: boolean | number;
    retryDelay?: number;
    onSuccess?: (data: StatsTopResult<T>) => void;
    onError?: (error: Error) => void;
  },
) => {
  return useQuery({
    queryKey: ["statsTop", metric, type],
    queryFn: async () => {
      const response = await apiClient.post<StatsTopResponse<T>["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "stats_top",
          params: [metric, type],
          id: 1,
        },
      );

      if (!response || !response.result) {
        throw new Error("Failed to fetch stats data");
      }

      return response.result;
    },
    ...options,
  });
};
