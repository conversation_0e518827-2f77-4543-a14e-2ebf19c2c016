import { z } from "zod";

export interface ContractMethod {
  name: string;
  signature?: string;
  type?: string;
  stateMutability?: string;
  inputs?: ContractInput[];
  outputs?: ContractOutput[];
}

export interface ContractInput {
  name: string;
  type: string;
  internalType?: string;
  components?: ContractInput[];
}

export interface ContractOutput {
  name: string;
  type: string;
  internalType?: string;
  components?: ContractOutput[];
}

export interface Source {
  abi: string;
  [key: string]: any;
}

// Result types for contract interactions
export interface ContractReadResult {
  data?: unknown;
  error?: string;
}

export interface ContractWriteResult {
  hash?: string;
  error?: string;
}

export type ContractResult = ContractReadResult | ContractWriteResult;

// Improved Zod schemas for validation
export const addressSchema = z
  .union([
    z.string().regex(/^0x[a-fA-F0-9]{40}$/, "Invalid address"),
    z.literal(""),
  ])
  .optional()
  .transform((val) => val || "");

export const uint256Schema = z
  .union([
    z.string().regex(/^\d+$/, "Must be a positive integer"),
    z
      .number()
      .int()
      .nonnegative()
      .transform((n) => n.toString()),
  ])
  .optional()
  .or(z.literal(""))
  .transform((val) => val || "");

export const bytesSchema = z
  .union([
    z.string().regex(/^0x[a-fA-F0-9]*$/, "Invalid bytes format"),
    z.literal(""),
  ])
  .optional()
  .transform((val) => val || "");

export const boolSchema = z
  .union([
    z.boolean(),
    z.string().regex(/^(true|false)$/, "Must be 'true' or 'false'"),
  ])
  .transform((val) => (typeof val === "boolean" ? val.toString() : val))
  .default("false");

export const stringSchema = z
  .string()
  .optional()
  .or(z.literal(""))
  .transform((val) => val || "");

export const getSchemaForType = (
  type: string,
  components?: ContractInput[],
): z.ZodTypeAny => {
  // Handle tuple types (e.g., (uint256,address))
  if (type.startsWith("(") && type.endsWith(")")) {
    if (components && components.length > 0) {
      return getSchemaForComponents(components);
    }
    // Simple tuple handling without component information
    return z.array(z.any()).default([]);
  }

  // Handle array types (e.g., uint256[], address[5])
  if (/\[\d*\]$/.test(type)) {
    // Extract the base type (remove the array part)
    const baseType = type.replace(/\[\d*\]$/, "");
    const baseSchema = getSchemaForType(baseType);

    return z
      .union([z.array(baseSchema), z.undefined(), z.null()])
      .transform((val) => (Array.isArray(val) ? val : []));
  }

  // Handle struct types with components
  if (components && components.length > 0) {
    return getSchemaForComponents(components);
  }

  // Handle numeric types (int, uint)
  if (type.startsWith("uint") || type.startsWith("int")) {
    return uint256Schema;
  }
  // Handle address type
  else if (type === "address") {
    return addressSchema;
  }
  // Handle bytes types (bytes, bytes32, etc.)
  else if (type.startsWith("bytes")) {
    return bytesSchema;
  }
  // Handle boolean type
  else if (type === "bool") {
    return boolSchema;
  }
  // Handle string type
  else if (type === "string") {
    return stringSchema;
  }
  // Handle fixed-point types (fixed128x18, ufixed64x18, etc.)
  else if (/^u?fixed\d+x\d+$/.test(type)) {
    return z
      .union([
        z.string().regex(/^-?\d+(\.\d+)?$/, "Must be a decimal number"),
        z.number().transform((n) => n.toString()),
      ])
      .optional()
      .default("");
  }

  // Default fallback with better error handling
  return z
    .string()
    .default("")
    .refine(() => true, { message: `Unsupported Solidity type: ${type}` });
};

// Helper function to create schema from components (for structs and tuples)
export const getSchemaForComponents = (components: ContractInput[]) => {
  const shape = components.reduce((acc, component) => {
    acc[component.name || "unnamed"] = getSchemaForType(
      component.type,
      component.components,
    );
    return acc;
  }, {} as Record<string, z.ZodTypeAny>);

  return z.object(shape).optional().default({});
};

// Function to create a schema for a contract method input
export const createInputSchema = (method: ContractMethod) => {
  if (!method.inputs || method.inputs.length === 0) {
    return z.object({}).optional();
  }

  const shape = method.inputs.reduce((acc, input) => {
    acc[input.name || `param${Object.keys(acc).length}`] = getSchemaForType(
      input.type,
      input.components,
    );
    return acc;
  }, {} as Record<string, z.ZodTypeAny>);

  return z.object(shape);
};

// Function to parse and validate contract method outputs
export const parseContractOutput = (
  outputs: ContractOutput[] | undefined,
  data: unknown,
): unknown => {
  if (!outputs || outputs.length === 0) {
    return data;
  }

  // Handle single output
  if (outputs.length === 1) {
    return data;
  }

  // Handle multiple outputs (typically returned as an array)
  if (Array.isArray(data)) {
    return outputs.reduce((acc, output, index) => {
      if (output.name) {
        acc[output.name] = data[index];
      } else {
        acc[`output${index}`] = data[index];
      }
      return acc;
    }, {} as Record<string, unknown>);
  }

  return data;
};
