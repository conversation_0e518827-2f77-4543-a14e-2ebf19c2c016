import { z } from "zod"

export const contractDetailsSchema = z.object({
  contractAddress: z.string().min(1, "Contract address is required"),
  compilerType: z.string().min(1, "Compiler type is required"),
  compilerVersion: z.string().min(1, "Compiler version is required"),
  licenseType: z.string().min(1, "License type is required"),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms of service",
  }),
  compilerVersionType: z.string().optional(),
})

export const contractCodeSchema = z.object({
  contractCode: z.string().min(1, "Contract code is required"),
  optimization: z.string(),
  runs: z.string(),
  evmVersion: z.string(),
  constructorArguments: z.string().optional(),
  sourceFiles: z.record(z.string()).optional(),
  bytecode: z.string().optional(),
})

export const verifyContractSchema = contractDetailsSchema.merge(contractCodeSchema)

export type ContractDetailsFormValues = z.infer<typeof contractDetailsSchema>
export type ContractCodeFormValues = z.infer<typeof contractCodeSchema>
export type VerifyContractFormValues = z.infer<typeof verifyContractSchema>

