"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { NATIVE_SYMBOL } from "@/constants"
import { PATH_ROUTER } from "@/constants/routers"
import { formatWeiToEther } from "@/helpers/format"
import { useEpochDetails } from "@/hooks/useEpoch"
import { format } from "date-fns"
import { CuboidIcon as Cube, Info } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"

export default function EpochDetailPage() {
  const params = useParams()
  const router = useRouter()
  const epochId = params.id as string
  const epochNumber = Number.parseInt(epochId)

  // Fetch data using the useEpochDetails hook
  const { data: epochDetails, isLoading, error } = useEpochDetails(epochNumber)

  // Navigation functions
  const goToPreviousEpoch = () => {
    if (epochNumber > 0) {
      router.push(`/epoch/${epochNumber - 1}`)
    }
  }

  const goToNextEpoch = () => {
    router.push(`/epoch/${epochNumber + 1}`)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (error || !epochDetails) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>Failed to load epoch data. Please try again later.</p>
        </div>
      </div>
    )
  }

  // Process validators data
  const activeValidators = epochDetails.validators.filter(
    (v) => v.status === true,
  )
  const inactiveValidators = epochDetails.validators.filter(
    (v) => v.status !== true,
  )
  const totalValidators = epochDetails.validators.length

  // Determine if this is the current epoch (this would need to be adjusted based on your actual data)
  const isCurrentEpoch = epochDetails.epochNumber === epochNumber

  const epochTime = new Date(epochDetails.timestamp)

  // Calculate participation rate
  const participationRate =
    totalValidators > 0 ? (activeValidators.length / totalValidators) * 100 : 0

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <button
          onClick={goToPreviousEpoch}
          className="flex flex-col items-center p-2 rounded-full"
          disabled={epochNumber <= 0}
        >
          <div
            className={`p-3 rounded-full ${
              epochNumber > 0 ? "bg-blue-50 hover:bg-gray-100" : "bg-gray-100"
            }`}
          >
            <Cube
              className={`h-5 w-5 ${
                epochNumber > 0 ? "text-blue-500" : "text-gray-400"
              }`}
            />
          </div>
          <div
            className={`font-medium ${
              epochNumber > 0
                ? "text-blue-500 hover:text-blue-600"
                : "text-gray-400"
            }`}
          >
            {epochNumber > 0 ? epochNumber - 1 : "-"}
          </div>
        </button>

        <div className="flex flex-col items-center">
          <div className="bg-blue-100 p-4 rounded-full mb-2">
            <Cube className="h-8 w-8 text-blue-600" />
          </div>
          <div className="text-xl font-bold">{epochId}</div>
          <div className="text-sm text-gray-500">Epoch</div>
        </div>

        <button
          onClick={goToNextEpoch}
          className="flex flex-col items-center p-2 rounded-full"
        >
          <div className="bg-blue-50 p-3 rounded-full hover:bg-gray-100">
            <Cube className="h-5 w-5 text-blue-500" />
          </div>

          <div className="font-medium text-blue-500 hover:text-blue-600">
            {epochNumber + 1}
          </div>
        </button>
      </div>

      {/* Epoch Details */}
      <div className="card mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Epoch Number</span>
                  </div>
                  <span className="font-medium">
                    {epochDetails.epochNumber}
                  </span>
                </div>

                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Finalized</span>
                  </div>
                  <Badge
                    variant={isCurrentEpoch ? "outline" : "default"}
                    className="font-medium"
                  >
                    {isCurrentEpoch ? "No" : "Yes"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Age</span>
                  </div>
                  <span className="font-medium">
                    {format(epochTime, "MMM dd, yyyy HH:mm:ss")} UTC
                  </span>
                </div>

                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Blocks</span>
                  </div>
                  <Badge variant="secondary" className="font-medium">
                    {epochDetails.epochLength} Proposed Blocks
                  </Badge>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Participation Rate</span>
                  </div>
                  <span className="font-medium">
                    {participationRate.toFixed(2)}%
                  </span>
                </div>

                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Votes</span>
                  </div>
                  <div className="flex flex-wrap gap-2 justify-end">
                    {Object.entries(epochDetails.votesMap)
                      .filter(([token]) => token !== NATIVE_SYMBOL)
                      .map(([token, amount]) => (
                        <Badge
                          key={token}
                          variant="secondary"
                          className="font-medium"
                        >
                          {formatWeiToEther(amount, 18)} {token}
                        </Badge>
                      ))}
                  </div>
                </div>

                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Repartition</span>
                  </div>
                  <div className="flex flex-wrap gap-2 justify-end">
                    {Object.entries(epochDetails.repartitionMap)
                      .filter(([token]) => token !== NATIVE_SYMBOL)
                      .map(([token, percentage]) => (
                        <Badge
                          key={token}
                          variant="outline"
                          className="font-medium"
                        >
                          {Number.parseFloat(percentage) * 100} % {token}
                        </Badge>
                      ))}
                  </div>
                </div>

                <div className="flex items-center justify-between py-3 border-b">
                  <div className="flex items-center">
                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Attestations</span>
                  </div>
                  <span className="font-medium">{activeValidators.length}</span>
                </div>
              </div>
            </div>

            {/* Validator Counts */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
              <div className="flex items-center justify-between py-3 border-b">
                <div className="flex items-center">
                  <Info className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Total Validator Count</span>
                </div>
                <span className="font-medium">{totalValidators}</span>
              </div>

              <div className="flex items-center justify-between py-3 border-b">
                <div className="flex items-center">
                  <Info className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Active Count</span>
                </div>
                <span className="font-medium">{activeValidators.length}</span>
              </div>

              <div className="flex items-center justify-between py-3 border-b">
                <div className="flex items-center">
                  <Info className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Pending Count</span>
                </div>
                <span className="font-medium">{inactiveValidators.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      {/* Validators Tab */}
      <Tabs defaultValue="validators" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="validators">
            Validators ({activeValidators.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="validators">
          <div className="card">
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        <th className="text-left p-4">#</th>
                        <th className="text-left p-4">Validator</th>
                        <th className="text-left p-4">Status</th>
                        <th className="text-left p-4">Staker</th>
                        <th className="text-right p-4">Voting Power</th>
                      </tr>
                    </thead>
                    <tbody>
                      {activeValidators.map((validator, index) => (
                        <tr
                          key={validator.operatorAddress}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="p-4">{index + 1}</td>
                          <td className="p-4">
                            <div className="flex flex-col">
                              <span className="font-medium">
                                <Link
                                  href={PATH_ROUTER.ADDRESS_DETAIL(
                                    validator.operatorAddress,
                                  )}
                                  className="text-primary hover:underline"
                                >
                                  {validator.operatorAddress}
                                </Link>
                              </span>
                            </div>
                          </td>
                          <td className="p-4">
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                validator.status
                                  ? "bg-green-100 text-green-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {validator.status ? "Active" : "Inactive"}
                            </span>
                          </td>
                          <td className="p-4">
                            <div className="flex flex-col gap-1">
                              {validator.assetWeights.map((asset, i) => (
                                <span key={i} className="text-xs">
                                  {formatWeiToEther(asset.baseAmount, 18)}{" "}
                                  {asset.symbol}
                                  {asset.symbol === NATIVE_SYMBOL
                                    ? " (Boost)"
                                    : ""}
                                </span>
                              ))}
                            </div>
                          </td>
                          <td className="p-4 text-right">
                            {formatWeiToEther(validator.votingPower, 18)}
                          </td>
                        </tr>
                      ))}

                      {activeValidators.length === 0 && (
                        <tr>
                          <td
                            colSpan={5}
                            className="p-4 text-center text-gray-500"
                          >
                            No validators found for this epoch
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
